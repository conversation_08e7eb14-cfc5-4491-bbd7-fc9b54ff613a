#!/usr/bin/env python3
"""
Simple test script to check if all imports work correctly
"""

def test_moviepy_imports():
    """Test MoviePy imports specifically"""
    print("Testing MoviePy imports...")

    try:
        import moviepy
        print(f"✅ MoviePy version: {moviepy.__version__}")
    except ImportError as e:
        print(f"❌ MoviePy import failed: {e}")
        return False

    try:
        # Try different import methods for different MoviePy versions
        try:
            from moviepy.editor import VideoFileClip, ImageClip, AudioFileClip, ColorClip, CompositeVideoClip, concatenate_videoclips
            print("✅ MoviePy editor imports successful (method 1)")
        except ImportError:
            # Try alternative imports for newer versions
            from moviepy import VideoFileClip, ImageClip, AudioFileClip, ColorClip, CompositeVideoClip, concatenate_videoclips
            print("✅ MoviePy editor imports successful (method 2)")
    except ImportError as e:
        print(f"❌ MoviePy editor imports failed: {e}")

        # Try to see what's available in moviepy
        try:
            import moviepy
            print(f"Available in moviepy: {dir(moviepy)}")
        except:
            pass

        return False

    return True

def test_other_imports():
    """Test other required imports"""
    print("\nTesting other imports...")

    try:
        import numpy as np
        print(f"✅ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False

    try:
        from PIL import Image, ImageDraw, ImageFont
        print(f"✅ Pillow version: {Image.__version__}")
    except ImportError as e:
        print(f"❌ Pillow import failed: {e}")
        return False

    try:
        import requests
        print(f"✅ Requests version: {requests.__version__}")
    except ImportError as e:
        print(f"❌ Requests import failed: {e}")
        return False

    return True

def test_local_modules():
    """Test local module imports"""
    print("\nTesting local module imports...")

    try:
        # Test video_processor import
        import sys
        import os

        # Add current directory to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        from video_processor import VideoProcessor
        print("✅ VideoProcessor imported successfully")

        # Test creating an instance
        processor = VideoProcessor()
        print("✅ VideoProcessor instance created successfully")

    except ImportError as e:
        print(f"❌ VideoProcessor import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ VideoProcessor creation failed: {e}")
        return False

    try:
        from text_processor import TextProcessor
        print("✅ TextProcessor imported successfully")

        # Test creating an instance
        text_proc = TextProcessor()
        print("✅ TextProcessor instance created successfully")

    except ImportError as e:
        print(f"❌ TextProcessor import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ TextProcessor creation failed: {e}")
        return False

    try:
        from audio_processor import AudioProcessor
        print("✅ AudioProcessor imported successfully")

        # Test creating an instance
        audio_proc = AudioProcessor()
        print("✅ AudioProcessor instance created successfully")

    except ImportError as e:
        print(f"❌ AudioProcessor import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ AudioProcessor creation failed: {e}")
        return False

    return True

def main():
    """Run all tests"""
    print("🚀 Testing Video Processor Imports\n")

    # Test MoviePy imports
    moviepy_ok = test_moviepy_imports()

    # Test other imports
    other_ok = test_other_imports()

    # Test local modules
    local_ok = test_local_modules()

    # Summary
    print("\n" + "="*50)
    print("📊 IMPORT TEST SUMMARY")
    print("="*50)

    results = [
        ("MoviePy Imports", moviepy_ok),
        ("Other Dependencies", other_ok),
        ("Local Modules", local_ok)
    ]

    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False

    if all_passed:
        print("\n🎉 All imports working correctly!")
        print("You can now run: python main.py sample_template.json")
    else:
        print("\n⚠️ Some imports failed. Please check the errors above.")

    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
