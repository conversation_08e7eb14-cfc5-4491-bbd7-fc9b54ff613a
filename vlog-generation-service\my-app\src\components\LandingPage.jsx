import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Badge } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import './LandingPage.css';
import {
  VideoLibrary as VideoIcon,
  AutoAwesome as MagicIcon,
  AutoAwesome as AutoAwesomeIcon,
  Speed as SpeedIcon,
  Brush as BrushIcon,
  Mic as MicIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Movie as MovieIcon
} from '@mui/icons-material';

// Import images
const heroImage = '/images/hero_image.jpg';

const LandingPage = () => {
  const navigate = useNavigate();

  const handleEnterApp = () => {
    navigate('/dashboard');
  };

  // No animation variants needed

  const features = [
    {
      title: 'AI-Powered Content Generation',
      description: 'Leverage Grok AI to automatically generate engaging vlog content tailored to your niche.',
      icon: <MagicIcon fontSize="large" className="text-primary mb-3" />,
      image: '/images/ai_content_generation.jpg'
    },
    {
      title: 'Shorts Creation',
      description: 'Create engaging short-form video content optimized for platforms like TikTok, Instagram Reels, and YouTube Shorts.',
      icon: <VideoIcon fontSize="large" className="text-danger mb-3" />,
      image: '/images/shorts_creation.jpg'
    },
    {
      title: 'AI Image Generation',
      description: 'Generate high-quality, custom images for your vlogs using advanced AI models with simple text prompts.',
      icon: <BrushIcon fontSize="large" className="text-info mb-3" />,
      image: '/images/ai_image_generation.jpg'
    },
    {
      title: 'Automatic Subtitles',
      description: 'Add professional subtitles to your videos automatically, improving accessibility and engagement.',
      icon: <SpeedIcon fontSize="large" className="text-success mb-3" />,
      image: '/images/automatic_subtitles.jpg'
    },
    {
      title: 'AI Speech Generation',
      description: 'Convert your content to natural-sounding speech with customizable voice options for professional narration.',
      icon: <MicIcon fontSize="large" className="text-warning mb-3" />,
      image: '/images/ai_speech_generation.jpg'
    },
    {
      title: 'Image to Video (Coming Soon)',
      description: 'Transform static images into dynamic video sequences with our upcoming image-to-video conversion feature.',
      icon: <AutoAwesomeIcon fontSize="large" className="text-secondary mb-3" />,
      image: '/images/image_to_video.jpg',
      comingSoon: true
    },
    {
      title: 'Automated Editing & Transitions',
      description: 'Apply professional video edits, transitions, and effects automatically to create polished, engaging content.',
      icon: <MovieIcon fontSize="large" className="text-primary mb-3" />,
      image: '/images/automated_editing.jpg'
    },
    {
      title: 'Video Rendering & Compilation',
      description: 'Render and compile your videos in various formats and resolutions for different platforms and purposes.',
      icon: <SettingsIcon fontSize="large" className="text-dark mb-3" />,
      image: '/images/video_rendering.jpg'
    }
  ];

  return (
    <div className="landing-page" style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <header className="py-3" style={{ background: 'linear-gradient(90deg, #ffffff 0%, #f8f9ff 100%)', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}>
        <Container>
          <Row className="align-items-center">
            <Col>
              <div className="d-flex align-items-center">
                <div className="logo-container position-relative" style={{ width: '50px', height: '50px', marginRight: '15px' }}>
                  <div className="position-absolute" style={{ top: 0, left: 0, width: '100%', height: '100%', background: '#2541b8', borderRadius: '12px', transform: 'rotate(45deg)', animation: 'pulse 3s infinite ease-in-out' }}></div>
                  <div className="position-absolute d-flex align-items-center justify-content-center" style={{ top: 0, left: 0, width: '100%', height: '100%', zIndex: 2 }}>
                    <span className="fw-bold text-white" style={{ fontSize: '1.2rem' }}>VF</span>
                  </div>
                </div>
                <div>
                  <h4 className="mb-0 fw-bold" style={{ color: '#2541b8', letterSpacing: '0.5px' }}>VisionFrame</h4>
                  <div className="d-flex align-items-center">
                    <span className="fw-bold" style={{ color: '#ff5757', fontSize: '1rem', letterSpacing: '1px' }}>AI</span>
                    <span className="ms-2 badge bg-primary" style={{ fontSize: '0.7rem', backgroundColor: '#e6ebff !important', color: '#2541b8', border: '1px solid #d1d9ff' }}>BETA</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col className="text-end">
              <Button
                variant="primary"
                size="lg"
                onClick={handleEnterApp}
                className="d-flex align-items-center ms-auto"
                style={{ backgroundColor: '#2541b8', borderColor: '#2541b8', borderRadius: '30px', padding: '10px 20px', boxShadow: '0 4px 10px rgba(37, 65, 184, 0.3)', transition: 'all 0.3s ease' }}
                onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}
                onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
              >
                <DashboardIcon className="me-2" />
                Enter Dashboard
              </Button>
            </Col>
          </Row>
        </Container>
      </header>

      {/* Hero Section */}
      <section className="py-5 text-center" style={{ background: 'white' }}>
        <Container>
          <Row className="align-items-center">
            <Col lg={7} className="text-lg-start">
              <div className="hero-content">
                <div className="hero-text-container p-4 rounded" style={{ backgroundColor: 'white', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
                  <h1 className="display-4 fw-bold mb-4" style={{ color: '#2541b8', fontSize: 'calc(1.8rem + 1.5vw)' }}>
                    <span className="d-inline-block" style={{ animation: 'fadeInUp 0.8s ease-out' }}>Vision</span>
                    <span className="d-inline-block" style={{ animation: 'fadeInUp 0.8s ease-out 0.2s', animationFillMode: 'both', color: '#4a6bff' }}>Frame</span>
                    <span className="d-inline-block" style={{ animation: 'fadeInUp 0.8s ease-out 0.4s', animationFillMode: 'both', color: '#ff5757' }}>AI</span>
                    <span className="d-block mt-2" style={{ animation: 'fadeInUp 0.8s ease-out 0.6s', animationFillMode: 'both', fontSize: 'calc(1.2rem + 1vw)' }}>Revolutionize Your Content Creation</span>
                  </h1>
                  <p className="lead mb-4" style={{ color: '#333333', fontSize: 'calc(1rem + 0.3vw)', fontWeight: '500', lineHeight: '1.6' }}>
                    Generate engaging vlog content, thumbnails, speech, and complete videos with AI assistance.
                    Our all-in-one platform streamlines your content creation workflow and boosts your productivity.
                  </p>
                </div>
                <div className="d-flex flex-wrap gap-3 justify-content-lg-start justify-content-center">
                  <Button
                    variant="primary"
                    size="lg"
                    className="px-5 py-3 fw-bold"
                    onClick={handleEnterApp}
                    style={{ backgroundColor: '#2541b8', borderColor: '#2541b8' }}
                  >
                    Get Started Now
                  </Button>
                  <Button
                    variant="outline-primary"
                    size="lg"
                    className="px-5 py-3 fw-bold"
                    onClick={() => document.getElementById('features').scrollIntoView({ behavior: 'smooth' })}
                    style={{ borderColor: '#2541b8', color: '#2541b8' }}
                  >
                    Explore Features
                  </Button>
                </div>
                <div className="mt-4 d-flex flex-wrap gap-2 justify-content-lg-start justify-content-center">
                  <Badge bg="primary" className="p-2 fw-bold" style={{ fontSize: '0.9rem', backgroundColor: '#2541b8', color: 'white', border: 'none' }}>AI Content</Badge>
                  <Badge bg="primary" className="p-2 fw-bold" style={{ fontSize: '0.9rem', backgroundColor: '#2541b8', color: 'white', border: 'none' }}>Image Generation</Badge>
                  <Badge bg="primary" className="p-2 fw-bold" style={{ fontSize: '0.9rem', backgroundColor: '#2541b8', color: 'white', border: 'none' }}>Speech Synthesis</Badge>
                  <Badge bg="primary" className="p-2 fw-bold" style={{ fontSize: '0.9rem', backgroundColor: '#2541b8', color: 'white', border: 'none' }}>Automatic Subtitles</Badge>
                  <Badge bg="primary" className="p-2 fw-bold" style={{ fontSize: '0.9rem', backgroundColor: '#2541b8', color: 'white', border: 'none' }}>Video Editing</Badge>
                </div>
              </div>
            </Col>
            <Col lg={5} className="mt-5 mt-lg-0">
              <div className="hero-image-container position-relative" style={{ height: '400px', padding: '8px', background: 'white', borderRadius: '8px', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
                <img
                  src={heroImage}
                  alt="VisionFrame AI"
                  className="img-fluid rounded shadow-lg"
                  style={{ objectFit: 'cover', height: '100%', width: '100%', border: '3px solid #f0f4ff' }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'https://via.placeholder.com/600x400?text=VisionFrame+AI';
                  }}
                />
                <div className="position-absolute" style={{ top: '-20px', right: '-20px', zIndex: 1 }}>
                  <div className="bg-danger text-white p-2 rounded-circle shadow-lg d-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
                    <div className="text-center">
                      <div className="fw-bold">NEW</div>
                      <small>Features</small>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Features Section */}
      <section id="features" className="py-5">
        <Container>
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold">Powerful Features</h2>
            <p className="lead text-muted">Everything you need to create compelling vlog content</p>
          </div>

          <div className="features-container">
            <Row>
              {features.map((feature, index) => (
                <Col md={6} lg={3} key={index} className="mb-4">
                  <div className="feature-item">
                    <Card className="h-100 shadow-sm border-0 text-center overflow-hidden">
                      <div className="feature-image-container" style={{ height: '200px', overflow: 'hidden', position: 'relative' }}>
                        <img
                          src={feature.image}
                          alt={feature.title}
                          className="w-100 h-100 object-cover"
                          style={{ objectFit: 'cover', transition: 'transform 0.5s ease' }}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = `https://via.placeholder.com/400x200?text=${feature.title.replace(' ', '+')}`;
                          }}
                        />
                        {feature.comingSoon && (
                          <div className="coming-soon-badge" style={{
                            position: 'absolute',
                            top: '10px',
                            right: '10px',
                            background: 'rgba(255, 0, 0, 0.8)',
                            color: 'white',
                            padding: '5px 10px',
                            borderRadius: '5px',
                            fontWeight: 'bold',
                            fontSize: '0.8rem'
                          }}>
                            COMING SOON
                          </div>
                        )}
                      </div>
                      <Card.Body className="p-4">
                        {feature.icon}
                        <Card.Title className="fw-bold mb-3">{feature.title}</Card.Title>
                        <Card.Text className="text-muted">{feature.description}</Card.Text>
                      </Card.Body>
                    </Card>
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </Container>
      </section>

      {/* How It Works Section */}
      <section className="py-5 bg-light">
        <Container>
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold">How It Works</h2>
            <p className="lead text-muted">Simple steps to generate your vlog content</p>
          </div>

          <Row className="align-items-center">
            <Col lg={6} className="mb-4 mb-lg-0">
              <div className="position-relative rounded shadow overflow-hidden" style={{ maxHeight: '500px' }}>
                <img
                  src={heroImage}
                  alt="Vlog Generator Dashboard"
                  className="img-fluid w-100"
                  style={{ objectFit: 'cover', height: '100%' }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'https://via.placeholder.com/600x400?text=Vlog+Generator+Dashboard';
                  }}
                />
                <div className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                  <div className="bg-dark bg-opacity-50 text-white p-4 rounded">
                    <h3 className="mb-0">Streamlined Vlog Creation</h3>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="steps-container">
                <div className="step-item p-3 mb-4 bg-light rounded shadow-sm">
                  <div className="d-flex">
                    <div className="me-4">
                      <div className="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                        <span className="fw-bold fs-4">1</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-primary">Create Content Prompts</h4>
                      <p className="text-muted">Define your content prompts with specific categories, tones, and formats. Our system helps you create structured prompts that generate high-quality content.</p>
                      <div className="mt-2">
                        <Badge bg="info" className="me-2">Categories</Badge>
                        <Badge bg="info" className="me-2">Tones</Badge>
                        <Badge bg="info" className="me-2">Formats</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="step-item p-3 mb-4 bg-light rounded shadow-sm">
                  <div className="d-flex">
                    <div className="me-4">
                      <div className="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                        <span className="fw-bold fs-4">2</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-success">Generate Content with AI</h4>
                      <p className="text-muted">Use Grok AI to automatically generate engaging vlog content, images, speech, and subtitles based on your prompts.</p>
                      <div className="mt-2">
                        <Badge bg="success" className="me-2">Text</Badge>
                        <Badge bg="success" className="me-2">Images</Badge>
                        <Badge bg="success" className="me-2">Speech</Badge>
                        <Badge bg="success" className="me-2">Subtitles</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="step-item p-3 mb-4 bg-light rounded shadow-sm">
                  <div className="d-flex">
                    <div className="me-4">
                      <div className="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                        <span className="fw-bold fs-4">3</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-danger">Edit & Compile</h4>
                      <p className="text-muted">Our system automatically applies transitions, edits, and compiles your content into a finished video ready for publishing.</p>
                      <div className="mt-2">
                        <Badge bg="danger" className="me-2">Transitions</Badge>
                        <Badge bg="danger" className="me-2">Editing</Badge>
                        <Badge bg="danger" className="me-2">Rendering</Badge>
                        <Badge bg="danger" className="me-2">Export</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <Button
                    variant="primary"
                    size="lg"
                    className="mt-3"
                    onClick={handleEnterApp}
                  >
                    Start Creating Now
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Project Details Section */}
      <section className="py-5">
        <Container>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="text-center mb-5">
                <h2 className="display-5 fw-bold">About This Project</h2>
                <p className="lead text-muted">Streamlining content creation for vloggers</p>
              </div>

              <div className="project-details">
                <Card className="border-0 shadow-sm">
                  <Card.Body className="p-5">
                    <p className="mb-4">
                      The Vlog Content Generator is a comprehensive tool designed to help content creators streamline their vlog production workflow. By leveraging AI technology, specifically Grok AI, this application automates the content creation process, allowing vloggers to focus on filming and editing.
                    </p>
                    <p className="mb-4">
                      This project was developed to address the challenges content creators face when maintaining consistent output while ensuring quality and engagement. The application provides a structured approach to content generation, with features for managing content prompts, generating tailored content, and organizing production assets.
                    </p>
                    <p>
                      Key capabilities include:
                    </p>
                    <ul className="mb-4">
                      <li>Content prompt management with customizable categories and formats</li>
                      <li>AI-powered content generation with Grok integration</li>
                      <li>Thumbnail prompt creation for visual consistency</li>
                      <li>Speech duration tracking and management</li>
                      <li>Comprehensive dashboard for monitoring content production</li>
                    </ul>
                    <p>
                      Whether you're a solo vlogger or part of a content team, this tool can help you maintain a consistent publishing schedule while reducing the creative burden of constant content ideation.
                    </p>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Call to Action */}
      <section className="py-5 text-center position-relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #2541b8 0%, #4a6bff 100%)' }}>
        <div className="position-absolute" style={{ top: 0, left: 0, width: '100%', height: '100%', overflow: 'hidden', opacity: 0.1 }}>
          <div style={{ position: 'absolute', top: '10%', left: '5%', width: '20px', height: '20px', borderRadius: '50%', background: 'white', animation: 'float 8s infinite ease-in-out' }}></div>
          <div style={{ position: 'absolute', top: '70%', left: '15%', width: '40px', height: '40px', borderRadius: '50%', background: 'white', animation: 'float 12s infinite ease-in-out' }}></div>
          <div style={{ position: 'absolute', top: '30%', left: '75%', width: '30px', height: '30px', borderRadius: '50%', background: 'white', animation: 'float 10s infinite ease-in-out' }}></div>
          <div style={{ position: 'absolute', top: '60%', left: '85%', width: '25px', height: '25px', borderRadius: '50%', background: 'white', animation: 'float 9s infinite ease-in-out' }}></div>
        </div>
        <Container className="position-relative" style={{ zIndex: 2 }}>
          <div className="cta-content text-white">
            <h2 className="display-5 fw-bold mb-4">Ready to Transform Your Content Creation?</h2>
            <p className="lead mb-4">
              Experience the power of AI-driven content generation today.
            </p>
            <div className="d-flex justify-content-center gap-3">
              <Button
                variant="light"
                size="lg"
                className="px-5 py-3 fw-bold"
                onClick={handleEnterApp}
                style={{ borderRadius: '30px', boxShadow: '0 4px 15px rgba(255, 255, 255, 0.3)', transition: 'all 0.3s ease' }}
                onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}
                onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
              >
                Get Started Now
              </Button>
              <Button
                variant="outline-light"
                size="lg"
                className="px-5 py-3"
                onClick={() => document.getElementById('features').scrollIntoView({ behavior: 'smooth' })}
                style={{ borderRadius: '30px', transition: 'all 0.3s ease' }}
                onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-3px)'}
                onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
              >
                Explore Features
              </Button>
            </div>
          </div>
        </Container>
      </section>

      {/* Footer */}
      <footer className="py-4 bg-light">
        <Container>
          <Row>
            <Col className="text-center">
              <p className="mb-0 text-muted">
                &copy; {new Date().getFullYear()} VisionFrame AI | All Rights Reserved
              </p>
            </Col>
          </Row>
        </Container>
      </footer>
    </div>
  );
};

export default LandingPage;
