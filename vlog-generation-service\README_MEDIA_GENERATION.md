# Media Generation Pipeline Service

This service orchestrates the content generation pipeline for VisionFrame AI, creating videos from content chunks with optimized memory usage.

## Overview

The Media Generation Pipeline Service integrates the following existing scripts into a cohesive workflow:

1. **Import CSV** (`_1content_importcsv.py`): Imports content data from CSV files
2. **Generate Prompts** (`_2content_generate_prompt.py`): Creates image prompts for content
3. **Generate Images** (`_3content_generate_images.py`): Generates images using Runware API
4. **Generate Speech** (`_4content_generate_speech.py`): Converts text to speech using FishAudio API
5. **Create Videos** (`_5content_generate_videoimage_speech_moviepy.py`): Combines images and audio into videos
6. **Add Subtitles** (`_6content_generate_video_subtitle.py`): Adds subtitles to videos

## Key Features

- **Memory Optimization**: Each step releases memory after completion to prevent memory leaks
- **Chunk-based Processing**: Processes content in chunks to optimize resource usage
- **Comprehensive Logging**: Logs events to both file and database for monitoring
- **Flexible Execution**: Run the full pipeline or individual steps as needed
- **Error Handling**: Robust error handling with detailed logging

## Requirements

- Python 3.8+
- MySQL database
- Required Python packages (see `requirements.txt`)
- API keys for external services (Runware, FishAudio)

## Database Setup

The service requires the following database tables:

- `content_chunk`: Stores content chunks with processing status
- `generated_content`: Stores generated content
- `image_prompts`: Stores image generation prompts
- `event_logs`: Logs processing events

## Usage

### Running the Full Pipeline

To run the complete media generation pipeline:

```bash
python media_generation_service.py run
```

This will:
1. Find all pending content chunks
2. Process each chunk through all enabled pipeline steps
3. Update the processing status of each chunk
4. Log events to both file and database

### Running a Single Step

To run a specific step of the pipeline:

```bash
python media_generation_service.py step "Step Name"
```

For example:
```bash
python media_generation_service.py step "Generate Images"
```

### Listing Available Steps

To see all available pipeline steps:

```bash
python media_generation_service.py list
```

## Configuration

The pipeline steps can be configured in the `PIPELINE_STEPS` variable in `media_generation_service.py`:

```python
PIPELINE_STEPS = [
    {
        'name': 'Step Name',
        'module': 'module_name',
        'enabled': True,  # Set to False to skip this step
        'async': True,    # Whether the step uses async/await
        'main_function': 'function_name'  # For async steps
    },
    # ...
]
```

## Environment Variables

Create a `.env` file with the following variables:

```
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_DATABASE=vlog_generator

# API Keys
RUNWARE_API_KEY=your_runware_api_key
FISHAUDIO_API_KEY=your_fishaudio_api_key

# Output Directories
FISHAUDIO_OUTPUT=content-speech/
```

## Logging

Logs are written to:
- Console output
- `vlog_generator.log` file
- `event_logs` database table

## Error Handling

When an error occurs:
1. The error is logged to both file and database
2. The chunk's processing status is updated to "failed"
3. The pipeline continues with the next chunk

## Memory Management

To optimize memory usage:
- Each step releases memory after completion using garbage collection
- Each chunk is processed completely before moving to the next chunk
- Large objects are explicitly deleted when no longer needed

## Extending the Pipeline

To add a new step to the pipeline:
1. Create a new script with the required functionality
2. Add the script to the `PIPELINE_STEPS` configuration
3. Ensure the script follows the same logging and error handling patterns
