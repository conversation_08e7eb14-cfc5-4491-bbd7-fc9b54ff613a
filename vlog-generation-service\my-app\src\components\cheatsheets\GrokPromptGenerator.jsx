import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { Psychology as PsychologyIcon } from '@mui/icons-material';
import { API_BASE_URL } from '../../constants';

const GrokPromptGenerator = ({ promptType }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [textOverlay, setTextOverlay] = useState('');
  const [topic, setTopic] = useState('');
  const [contentType, setContentType] = useState('blog post');
  const [audience, setAudience] = useState('general audience');
  const [tone, setTone] = useState('informative');
  const [emotion, setEmotion] = useState('neutral');
  const [style, setStyle] = useState('digital realism');

  const contentTypes = [
    'blog post',
    'social media post',
    'vlog script',
    'short-form video script',
    'long-form article'
  ];

  const audiences = [
    'general audience',
    'beginners',
    'professionals',
    'millennials',
    'gen z',
    'business owners',
    'students'
  ];

  const tones = [
    'informative',
    'conversational',
    'professional',
    'humorous',
    'inspirational',
    'educational',
    'empathetic'
  ];

  const emotions = [
    'neutral',
    'joy',
    'shock',
    'mystery',
    'urgency',
    'inspiration',
    'suspense',
    'peaceful'
  ];

  const styles = [
    'digital realism',
    '3D illustration',
    'pop art / cartoon',
    'watercolor',
    'cinematic',
    'hyperrealistic',
    'minimalistic'
  ];

  const handleGeneratePrompt = async () => {
    if (!topic) {
      setError('Please enter a topic');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const requestData = {
        prompt_type: promptType === 'content' ? 'content_creation' :
                    promptType === 'image' ? 'image_generation' : 'thumbnail_generation',
        topic: topic,
        content_type: contentType,
        audience: audience,
        tone: tone,
        emotion: emotion,
        style: style
      };

      console.log('Sending request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate prompt: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);
      if (data.success && data.generated_prompt) {
        setGeneratedPrompt(data.generated_prompt);

        // Handle text overlay for thumbnail generation
        if (promptType === 'thumbnail' && data.text_overlay) {
          setTextOverlay(data.text_overlay);
        } else {
          setTextOverlay(''); // Clear text overlay for other prompt types
        }
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }
    } catch (err) {
      console.error('Error generating prompt:', err);
      setError(`Failed to generate prompt: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <Card.Header className="d-flex align-items-center">
        <PsychologyIcon className="me-2" />
        <h5 className="mb-0">Generate {promptType === 'content' ? 'Content' :
                              promptType === 'image' ? 'Image' : 'Thumbnail'} Prompt with Grok</h5>
      </Card.Header>
      <Card.Body>
        {error && (
          <Alert variant="danger" onClose={() => setError(null)} dismissible>
            {error}
          </Alert>
        )}

        <Form>
          <Form.Group className="mb-3">
            <Form.Label>Topic</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter your topic"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
            />
          </Form.Group>

          {promptType === 'content' && (
            <>
              <Form.Group className="mb-3">
                <Form.Label>Content Type</Form.Label>
                <Form.Select
                  value={contentType}
                  onChange={(e) => setContentType(e.target.value)}
                >
                  {contentTypes.map((type) => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Target Audience</Form.Label>
                <Form.Select
                  value={audience}
                  onChange={(e) => setAudience(e.target.value)}
                >
                  {audiences.map((aud) => (
                    <option key={aud} value={aud}>
                      {aud.charAt(0).toUpperCase() + aud.slice(1)}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Tone</Form.Label>
                <Form.Select
                  value={tone}
                  onChange={(e) => setTone(e.target.value)}
                >
                  {tones.map((t) => (
                    <option key={t} value={t}>
                      {t.charAt(0).toUpperCase() + t.slice(1)}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </>
          )}

          {(promptType === 'image' || promptType === 'thumbnail') && (
            <>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Emotion/Mood</Form.Label>
                    <Form.Select
                      value={emotion}
                      onChange={(e) => setEmotion(e.target.value)}
                    >
                      {emotions.map((e) => (
                        <option key={e} value={e}>
                          {e.charAt(0).toUpperCase() + e.slice(1)}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Visual Style</Form.Label>
                    <Form.Select
                      value={style}
                      onChange={(e) => setStyle(e.target.value)}
                    >
                      {styles.map((s) => (
                        <option key={s} value={s}>
                          {s.charAt(0).toUpperCase() + s.slice(1)}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              {promptType === 'thumbnail' && (
                <Form.Group className="mb-3">
                  <Form.Label>Content Type</Form.Label>
                  <Form.Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value)}
                  >
                    <option value="youtube vlog">YouTube Vlog</option>
                    <option value="educational tutorial">Educational Tutorial</option>
                    <option value="reaction video">Reaction Video</option>
                    <option value="wellness content">Wellness Content</option>
                    <option value="gaming video">Gaming Video</option>
                    <option value="product review">Product Review</option>
                  </Form.Select>
                </Form.Group>
              )}
            </>
          )}

          <div className="d-grid">
            <Button
              variant="primary"
              onClick={handleGeneratePrompt}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    aria-hidden="true"
                    className="me-2"
                  />
                  Generating...
                </>
              ) : (
                <>Generate Prompt with Grok</>
              )}
            </Button>
          </div>
        </Form>

        {generatedPrompt && (
          <div className="mt-4">
            <h6>Generated Prompt:</h6>
            <div className="bg-light p-3 rounded">
              <pre className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                {generatedPrompt}
              </pre>
            </div>

            {textOverlay && (
              <div className="mt-3">
                <h6>Text Overlay:</h6>
                <div className="bg-light p-3 rounded text-center">
                  <div
                    className="p-3 mb-0"
                    style={{
                      fontSize: '24px',
                      fontWeight: 'bold',
                      color: '#fff',
                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                      background: 'linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.2))',
                      borderRadius: '4px',
                      display: 'inline-block',
                      padding: '10px 20px'
                    }}
                  >
                    {textOverlay}
                  </div>
                </div>
              </div>
            )}

            <div className="d-grid mt-2">
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => {
                  const textToCopy = textOverlay
                    ? `Image Prompt:\n${generatedPrompt}\n\nText Overlay:\n${textOverlay}`
                    : generatedPrompt;
                  navigator.clipboard.writeText(textToCopy);
                }}
              >
                Copy to Clipboard
              </Button>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default GrokPromptGenerator;
