Stack trace:
Frame         Function      Args
0007FFFFAB50  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAB50, 0007FFFF9A50) msys-2.0.dll+0x1FEBA
0007FFFFAB50  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE28) msys-2.0.dll+0x67F9
0007FFFFAB50  000210046832 (000210285FF9, 0007FFFFAA08, 0007FFFFAB50, 000000000000) msys-2.0.dll+0x6832
0007FFFFAB50  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAB50  0002100690B4 (0007FFFFAB60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAE30  00021006A49D (0007FFFFAB60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEC3E20000 ntdll.dll
7FFE97600000 aswhook.dll
7FFEC3C50000 KERNEL32.DLL
7FFEC0E10000 KERNELBASE.dll
7FFEBB210000 apphelp.dll
7FFEC33C0000 USER32.dll
7FFEC1E50000 win32u.dll
7FFEC2270000 GDI32.dll
7FFEC19E0000 gdi32full.dll
7FFEC1DB0000 msvcp_win.dll
7FFEC1CB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEC22A0000 advapi32.dll
7FFEC2DC0000 msvcrt.dll
7FFEC2AF0000 sechost.dll
7FFEC2080000 RPCRT4.dll
7FFEC0720000 CRYPTBASE.DLL
7FFEC1B80000 bcryptPrimitives.dll
7FFEC25F0000 IMM32.DLL
