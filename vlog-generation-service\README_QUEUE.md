# VisionFrame AI Queue System

This document describes the queue system for VisionFrame AI, which handles the processing of content chunks through various stages of the video generation pipeline.

## Overview

The queue system is built using Celery, a distributed task queue framework, with <PERSON><PERSON> as the message broker. It provides a flexible and scalable way to process content chunks asynchronously, with support for parallel processing of different tasks.

## Components

The queue system consists of the following components:

1. **Celery App**: Defined in `celery_config.py`, this is the main entry point for the queue system.
2. **Task Modules**: Located in the `tasks` directory, these modules define the tasks that can be executed by the queue system.
3. **Worker Process**: Defined in `worker.py`, this process executes the tasks.
4. **Beat Process**: Defined in `beat.py`, this process schedules periodic tasks.
5. **Flower Monitoring**: Defined in `flower.py`, this provides a web interface for monitoring the queue system.
6. **Queue API**: Defined in `queue_api.py`, this provides API endpoints for interacting with the queue system.

## Task Flow

The queue system processes content chunks through the following steps:

1. **Generate Images**: Generate images for the content chunk.
2. **Generate Speech**: Generate speech audio for the content chunk.
3. **Create Slideshow**: Create a slideshow with transitions using the generated images.
4. **Create Subtitles**: Create subtitles for the content chunk.
5. **Mix Components**: Mix all components to create a final video.

Each step is executed in sequence, with the next step only starting after the previous step is completed. However, different chunks can be processed in parallel.

## Database Schema

The queue system uses the `process_queue` table in the database to track the status of each task. The table has the following structure:

```sql
CREATE TABLE process_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_id INT,
    chunk_id INT NOT NULL,
    process_step VARCHAR(50) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'waiting') DEFAULT 'pending',
    step_order INT NOT NULL,
    result_data JSON,
    error_message TEXT,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chunk_id) REFERENCES content_chunk(id) ON DELETE CASCADE
)
```

## API Endpoints

The queue system provides the following API endpoints:

- `POST /api/queue/submit`: Submit chunks to the processing queue.
- `GET /api/queue/status`: Get the status of the processing queue.
- `GET /api/queue/task/<task_id>`: Get details for a specific task.
- `POST /api/queue/retry/<task_id>`: Retry a failed task.
- `POST /api/queue/clear`: Clear the processing queue.

## Installation and Setup

### Prerequisites

- Python 3.8 or higher
- MySQL database
- Redis server (optional, can use memory broker for development)

### Installation

1. Install the required Python packages:

```bash
pip install -r requirements_queue.txt
```

2. Make sure the MySQL database is set up with the required tables.

3. (Optional) Install Redis server:
   - **Windows**: Install with Chocolatey: `choco install redis-64` or download from [Microsoft Archive Redis](https://github.com/microsoftarchive/redis/releases)
   - **macOS**: Install with Homebrew: `brew install redis`
   - **Ubuntu/Debian**: `sudo apt install redis-server`
   - **CentOS/RHEL**: `sudo yum install redis`

### Starting the Queue System

#### With Redis (Recommended for Production)

You can start all components of the queue system using the `start_queue_system.py` script:

```bash
python start_queue_system.py
```

This script will:

1. Check if Redis is installed and running
2. Start Redis if it's not already running
3. Start the Celery worker
4. Start the Celery beat scheduler
5. Start the Flower monitoring interface

#### Without Redis (Development Only)

For development purposes, you can use the memory broker instead of Redis:

```bash
python start_memory_queue.py
```

This script will:

1. Configure Celery to use the memory broker
2. Start the Celery worker
3. Start the Celery beat scheduler
4. Start the Flower monitoring interface

**Note**: The memory broker is not suitable for production use as it doesn't persist tasks between restarts and doesn't support multiple workers.

#### Starting Components Separately

Alternatively, you can start each component separately:

1. Start the Celery worker:

```bash
python worker.py
```

2. Start the Celery beat scheduler:

```bash
python beat.py
```

3. Start the Flower monitoring interface:

```bash
python flower.py
```

## Monitoring

You can monitor the queue system using the Flower web interface, which is available at http://localhost:5555 when the Flower process is running.

## Parallel Processing

The queue system supports parallel processing of different tasks. By default, it uses 4 worker processes, but this can be configured in the `celery_config.py` file.

The system is designed to process different chunks in parallel, while ensuring that the steps for each chunk are processed in sequence. This allows for efficient use of resources while maintaining the correct order of operations.

## Error Handling

The queue system includes robust error handling:

1. **Task Retries**: Tasks that fail can be retried automatically, with configurable retry limits and delays.
2. **Error Logging**: Detailed error information is logged for troubleshooting.
3. **Manual Retry**: Failed tasks can be manually retried through the API.

## Customization

The queue system is highly customizable:

1. **Task Configuration**: Task parameters such as retry limits and concurrency can be configured in the `celery_config.py` file.
2. **Worker Configuration**: Worker parameters such as the number of processes can be configured in the `worker.py` file.
3. **Beat Configuration**: Periodic task schedules can be configured in the `celery_config.py` file.

## Conclusion

The VisionFrame AI queue system provides a flexible and scalable way to process content chunks through various stages of the video generation pipeline. It supports parallel processing of different chunks, while ensuring that the steps for each chunk are processed in sequence.
