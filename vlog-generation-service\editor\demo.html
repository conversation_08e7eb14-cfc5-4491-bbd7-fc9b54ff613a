<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VisionFrame AI Editor - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .demo-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .feature-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold mb-4">VisionFrame AI</h1>
            <p class="text-xl opacity-90">Browser-Based Video Editor</p>
            <p class="text-lg opacity-75 mt-2">Create animated video compositions with real-time preview</p>
        </div>

        <!-- Main Demo Card -->
        <div class="demo-card rounded-2xl p-8 mb-8 max-w-4xl mx-auto">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold mb-4">Ready to Create Amazing Videos?</h2>
                <p class="text-lg opacity-90 mb-6">
                    Launch the editor and start building your animated video composition
                </p>
                <a href="index.html" 
                   class="inline-block bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 
                          text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105">
                    🚀 Launch Editor
                </a>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- Feature 1 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🎬</span>
                </div>
                <h3 class="text-xl font-bold mb-2">Multi-Element Support</h3>
                <p class="opacity-80">Add text, images, and videos to create rich compositions</p>
            </div>

            <!-- Feature 2 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🎞️</span>
                </div>
                <h3 class="text-xl font-bold mb-2">Real-Time Preview</h3>
                <p class="opacity-80">See your animations play instantly with timeline controls</p>
            </div>

            <!-- Feature 3 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">⚡</span>
                </div>
                <h3 class="text-xl font-bold mb-2">Keyframe Animation</h3>
                <p class="opacity-80">Animate position, opacity, scale, and rotation with precision</p>
            </div>

            <!-- Feature 4 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">📤</span>
                </div>
                <h3 class="text-xl font-bold mb-2">JSON Export</h3>
                <p class="opacity-80">Export projects for use with video rendering services</p>
            </div>

            <!-- Feature 5 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🌐</span>
                </div>
                <h3 class="text-xl font-bold mb-2">Browser-Based</h3>
                <p class="opacity-80">No installation required - works in any modern browser</p>
            </div>

            <!-- Feature 6 -->
            <div class="demo-card rounded-xl p-6">
                <div class="feature-icon w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🎨</span>
                </div>
                <h3 class="text-xl font-bold mb-2">Easy to Use</h3>
                <p class="opacity-80">Intuitive interface with drag-and-drop functionality</p>
            </div>
        </div>

        <!-- Quick Start Guide -->
        <div class="demo-card rounded-2xl p-8 mb-8 max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold mb-6 text-center">Quick Start Guide</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">🚀 Getting Started</h3>
                    <ol class="space-y-3 opacity-90">
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                            <span>Click "Launch Editor" to open the video editor</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                            <span>Try "Load Sample Project" to see a demo</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                            <span>Add your own text, images, or videos</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                            <span>Create animations with keyframes</span>
                        </li>
                    </ol>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">💡 Pro Tips</h3>
                    <ul class="space-y-3 opacity-90">
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-3">💡</span>
                            <span>Use the timeline scrubber to preview animations</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-3">💡</span>
                            <span>Select elements to edit properties and animations</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-3">💡</span>
                            <span>Export to JSON for video rendering services</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-3">💡</span>
                            <span>Use percentage values for responsive positioning</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Sample Project Preview -->
        <div class="demo-card rounded-2xl p-8 mb-8 max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold mb-6 text-center">Sample Project Includes</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">📝</div>
                    <h4 class="font-bold">Animated Text</h4>
                    <p class="text-sm opacity-75">Fade-in title with position animation</p>
                </div>
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">🖼️</div>
                    <h4 class="font-bold">Scaling Images</h4>
                    <p class="text-sm opacity-75">Images with scale and opacity effects</p>
                </div>
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">🔄</div>
                    <h4 class="font-bold">Rotating Logo</h4>
                    <p class="text-sm opacity-75">Continuous rotation with fade effects</p>
                </div>
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">➡️</div>
                    <h4 class="font-bold">Moving Text</h4>
                    <p class="text-sm opacity-75">Horizontal movement animation</p>
                </div>
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">📈</div>
                    <h4 class="font-bold">Scale Effects</h4>
                    <p class="text-sm opacity-75">Bounce-in scale animation</p>
                </div>
                <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4">
                    <div class="text-2xl mb-2">⏱️</div>
                    <h4 class="font-bold">10 Second Timeline</h4>
                    <p class="text-sm opacity-75">Full composition with timed sequences</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <a href="index.html" 
               class="inline-block bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 
                      text-white font-bold py-4 px-12 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                Start Creating Now →
            </a>
            <p class="mt-4 opacity-75">No signup required • Works offline • Export ready</p>
        </div>

        <!-- Footer -->
        <div class="text-center mt-12 opacity-60">
            <p>&copy; 2024 VisionFrame AI - Browser-Based Video Editor</p>
        </div>
    </div>
</body>
</html>
