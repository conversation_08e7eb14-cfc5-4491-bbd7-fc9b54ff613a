"""
Queue manager tasks for the VisionFrame AI queue system.
This module handles task scheduling and sequencing.
"""

import logging
from celery import shared_task
from .db_utils import (
    get_pending_tasks,
    update_task_status,
    get_task_by_id
)
# Import task functions after task_utils to avoid circular imports
from .task_utils import task_completed, task_failed

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Task mapping - will be populated after imports to avoid circular dependencies
TASK_MAPPING = {}

@shared_task(name='tasks.queue_manager.check_pending_tasks')
def check_pending_tasks():
    """Check for pending tasks and dispatch them sequentially by ID."""
    logger.info("Checking for pending tasks...")

    # Get pending tasks, ordered by ID to ensure sequential processing
    # Limit to 1 to process one task at a time
    pending_tasks = get_pending_tasks(limit=1)

    if not pending_tasks:
        logger.info("No pending tasks found")
        return

    logger.info(f"Found pending task: {pending_tasks[0]['id']}")

    # Process the task
    task = pending_tasks[0]
    dispatch_task.delay(task['id'])

    # Schedule another check after this task is dispatched
    # This ensures we process tasks one by one in ID order
    check_pending_tasks.apply_async(countdown=5)

    return f"Dispatched task {task['id']}"

@shared_task(name='tasks.queue_manager.dispatch_task')
def dispatch_task(queue_id):
    """Dispatch a task to the appropriate worker."""
    logger.info(f"Dispatching task {queue_id}")

    # Get the task
    task = get_task_by_id(queue_id)

    if not task:
        logger.error(f"Task {queue_id} not found")
        return

    # Update task status to processing
    update_task_status(queue_id, 'processing')

    # Get the task function
    process_step = task['process_step']
    task_function = TASK_MAPPING.get(process_step)

    if not task_function:
        logger.error(f"No task function found for process step {process_step}")
        update_task_status(queue_id, 'failed', error_message=f"No task function found for process step {process_step}")
        return

    # Execute the task
    try:
        logger.info(f"Executing task {queue_id} ({process_step})")
        task_function.delay(queue_id)
        return f"Task {queue_id} dispatched to {process_step}"
    except Exception as e:
        logger.error(f"Error dispatching task {queue_id}: {str(e)}")
        update_task_status(queue_id, 'failed', error_message=str(e))
        return f"Error dispatching task {queue_id}: {str(e)}"

# Import task functions after defining the core functions
# This avoids circular imports
from .image_tasks import generate_images
from .speech_tasks import generate_speech
from .slideshow_tasks import create_slideshow
from .subtitle_tasks import create_subtitles
from .video_tasks import mix_components
from .media_tasks import generate_media, run_pipeline_step

# Now populate the task mapping
TASK_MAPPING.update({
    'generate_images': generate_images,
    'generate_speech': generate_speech,
    'create_slideshow': create_slideshow,
    'create_subtitles': create_subtitles,
    'mix_components': mix_components,
    'generate_media': generate_media,
    'run_pipeline_step': run_pipeline_step
})
