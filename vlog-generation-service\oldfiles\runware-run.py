import logging
import os
import asyncio
import json
import sys
import platform
from datetime import datetime
from dotenv import load_dotenv
from runware import Runware, IImageInference

# Configure UTF-8 encoding for Windows console
if sys.platform.startswith('win'):
    import locale
    if locale.getpreferredencoding() != 'UTF-8':
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')

# Configure logging to show in both console and file
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),  # Console handler with explicit stdout
        logging.FileHandler('runware_generation.log', encoding='utf-8')  # File handler with UTF-8
    ]
)

class APIResponseFormatter:
    @staticmethod
    def format_response(response):
        """Format API response for logging"""
        try:
            if isinstance(response, (dict, list)):
                return json.dumps(response, indent=2)
            return str(response)
        except Exception as e:
            return f"Could not format response: {str(e)}"

async def main() -> None:
    start_time = datetime.now()
    logging.info("-" * 50)
    logging.info(f"Starting new generation session at {start_time}")
    logging.info("-" * 50)

    # Load environment variables
    load_dotenv()
    RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

    if not RUNWARE_API_KEY:
        logging.error("[ERROR] RUNWARE_API_KEY is missing. Please check your .env file.")
        return

    logging.info("[INFO] Initializing Runware client...")
    runware = Runware(api_key=RUNWARE_API_KEY)  # FIXED HERE

    try:
        logging.info("[INFO] Attempting to connect to Runware API...")
        await runware.connect()
        logging.info("[SUCCESS] Successfully connected to Runware API")
    except Exception as e:
        logging.error(f"[ERROR] Failed to connect to Runware API: {str(e)}")
        return

    # Define the image inference request
    request_params = {
        "positivePrompt": "A couple sitting together on a couch, one holding a laptop showing a video call interface, symbolizing regular communication during travel.",
        "negativePrompt": "blurry, low quality, deformed",
        "model": "runware:100@1",  # Use appropriate model name per RunWare docs
        "numberResults": 1,
        "height": 2048,
        "width": 1152,
        "steps": 10,
        "CFGScale": 7.5,
        "outputFormat": "JPEG"
    }

    logging.info("[INFO] Image Generation Parameters:")
    logging.info(json.dumps(request_params, indent=2))

    request_image = IImageInference(**request_params)

    logging.info("[INFO] Sending image inference request...")

    try:
        logging.info("[INFO] Waiting for image generation...")
        images = await runware.imageInference(requestImage=request_image)
        logging.info(f"[SUCCESS] Successfully received {len(images)} images")
        
        # Log detailed response
        logging.info("[INFO] API Response Details:")
        for idx, image in enumerate(images, start=1):
            logging.info(f"\nImage {idx} Details:")
            logging.info(f"  URL: {image.imageURL}")
            logging.info(f"  Size: {image.width}x{image.height}")
            if hasattr(image, 'metadata'):
                logging.info(f"  Metadata: {APIResponseFormatter.format_response(image.metadata)}")

    except Exception as e:
        logging.error(f"[ERROR] Image inference request failed: {str(e)}")
        if hasattr(e, 'response'):
            logging.error(f"Response details: {APIResponseFormatter.format_response(e.response)}")
        return

    end_time = datetime.now()
    duration = end_time - start_time
    logging.info("-" * 50)
    logging.info(f"[SUCCESS] Generation session completed at {end_time}")
    logging.info(f"[INFO] Total duration: {duration}")
    logging.info("-" * 50)

if __name__ == "__main__":
    try:
        if platform.system() == "Windows":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())  # FIX for Windows
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("\n[WARNING] Process interrupted by user")
    except Exception as e:
        logging.error(f"[ERROR] Unexpected error: {str(e)}")
    finally:
        logging.info("[INFO] Script execution ended")
