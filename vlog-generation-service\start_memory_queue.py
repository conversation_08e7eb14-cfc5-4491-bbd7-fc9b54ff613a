"""
Simplified startup script for the VisionFrame AI queue system using memory broker.
This script starts all components of the queue system without requiring Redis.
"""

import os
import time
import logging
import subprocess
import threading
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def update_celery_config_for_memory_broker():
    """Update Celery config to use memory broker for development."""
    try:
        with open('celery_config.py', 'r') as f:
            content = f.read()
        
        # Replace Redis broker with memory broker
        content = content.replace(
            "broker='redis://localhost:6379/0',", 
            "broker='memory://',"
        )
        content = content.replace(
            "backend='redis://localhost:6379/1',", 
            "backend='memory://',"
        )
        
        with open('celery_config.py', 'w') as f:
            f.write(content)
        
        logger.info("Updated Celery config to use memory broker")
        return True
    except Exception as e:
        logger.error(f"Error updating Celery config: {str(e)}")
        logger.error("Please manually update celery_config.py to use memory broker")
        return False

def start_worker():
    """Start Celery worker."""
    logger.info("Starting Celery worker...")
    
    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'worker', 
             '--loglevel=INFO', '--concurrency=4', '-n', 'visionframe_worker@%h',
             '-Q', 'default,image_generation,speech_generation,slideshow_creation,subtitle_creation,video_mixing'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        
        logger.info("Celery worker started")
        return process
    except Exception as e:
        logger.error(f"Error starting Celery worker: {str(e)}")
        logger.error("Make sure Celery is installed: pip install celery")
        return None

def start_beat():
    """Start Celery beat."""
    logger.info("Starting Celery beat...")
    
    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'beat', '--loglevel=INFO'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        
        logger.info("Celery beat started")
        return process
    except Exception as e:
        logger.error(f"Error starting Celery beat: {str(e)}")
        logger.error("Make sure Celery is installed: pip install celery")
        return None

def start_flower():
    """Start Flower monitoring."""
    logger.info("Starting Flower monitoring...")
    
    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'flower', '--port=5555'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        
        logger.info("Flower monitoring started at http://localhost:5555")
        return process
    except Exception as e:
        logger.error(f"Error starting Flower monitoring: {str(e)}")
        logger.error("Make sure Flower is installed: pip install flower")
        return None

def monitor_process(process, name):
    """Monitor a process and log its output."""
    if process is None:
        return
        
    while True:
        output = process.stdout.readline()
        if output:
            logger.info(f"{name}: {output.strip().decode('utf-8')}")
        
        error = process.stderr.readline()
        if error:
            logger.error(f"{name} error: {error.strip().decode('utf-8')}")
        
        # Check if process is still running
        if process.poll() is not None:
            logger.warning(f"{name} process has terminated with code {process.returncode}")
            break
        
        time.sleep(0.1)

if __name__ == '__main__':
    logger.info("Starting VisionFrame AI queue system with memory broker...")
    
    # Update Celery config to use memory broker
    if not update_celery_config_for_memory_broker():
        logger.error("Failed to update Celery config. Exiting.")
        exit(1)
    
    # Start Celery worker
    worker_process = start_worker()
    
    # Start Celery beat
    beat_process = start_beat()
    
    # Start Flower monitoring
    flower_process = start_flower()
    
    # Monitor processes
    if worker_process:
        threading.Thread(target=monitor_process, args=(worker_process, "Worker"), daemon=True).start()
    
    if beat_process:
        threading.Thread(target=monitor_process, args=(beat_process, "Beat"), daemon=True).start()
    
    if flower_process:
        threading.Thread(target=monitor_process, args=(flower_process, "Flower"), daemon=True).start()
    
    logger.info("All components started. Press Ctrl+C to stop.")
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping all components...")
        
        # Stop processes
        if worker_process:
            worker_process.terminate()
        
        if beat_process:
            beat_process.terminate()
        
        if flower_process:
            flower_process.terminate()
        
        logger.info("All components stopped.")
