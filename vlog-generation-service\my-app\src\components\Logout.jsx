import React, { useEffect } from 'react';
import { Container, Row, Col, Card, Spinner } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const Logout = () => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Simulate logout process
    const timer = setTimeout(() => {
      // Redirect to home page after "logout"
      navigate('/');
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [navigate]);
  
  return (
    <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '80vh' }}>
      <Row>
        <Col>
          <Card className="text-center p-5" style={{ width: '400px' }}>
            <Spinner animation="border" variant="primary" className="mx-auto mb-4" />
            <Card.Title>Logging Out</Card.Title>
            <Card.Text>
              Please wait while we log you out...
            </Card.Text>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Logout;
