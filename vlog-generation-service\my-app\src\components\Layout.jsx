import React, { useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import { Container, Row, Col } from 'react-bootstrap';
import '../App.css';

const Layout = () => {
  const location = useLocation();

  useEffect(() => {
    console.log('Layout rendered with path:', location.pathname);
  }, [location.pathname]);

  return (
    <Container fluid className="p-0">
      <Row className="m-0">
        <Col xs="auto" className="p-0">
          <Sidebar />
        </Col>
        <Col className="p-0">
          <div className="main-content" style={{ backgroundColor: '#fff', minHeight: '100vh', padding: '20px' }}>
            <Outlet />
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default Layout;
