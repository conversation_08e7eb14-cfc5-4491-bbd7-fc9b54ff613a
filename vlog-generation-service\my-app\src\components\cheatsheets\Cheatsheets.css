.cheatsheet-container {
  max-width: 100%;
  margin-bottom: 2rem;
}

.cheatsheet-container .card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.cheatsheet-container .card-header {
  padding: 1rem 1.25rem;
  font-weight: 600;
}

.cheatsheet-container .card-header.bg-dark {
  background-color: #000 !important;
}

.cheatsheet-container pre {
  white-space: pre-wrap;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
  color: #333;
}

.cheatsheet-container blockquote {
  border-left: 4px solid #2541b8;
  padding-left: 1rem;
  color: #555;
}

.cheatsheet-container .table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.cheatsheet-container .list-group-item {
  border-left: 3px solid #2541b8;
}

.cheatsheet-container .bg-light {
  background-color: #f8f9fa !important;
}

@media (max-width: 768px) {
  .cheatsheet-container .card-body {
    padding: 1rem;
  }
  
  .cheatsheet-container pre {
    font-size: 0.8rem;
  }
}
