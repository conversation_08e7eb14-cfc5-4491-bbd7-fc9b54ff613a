"""
Slideshow creation tasks for the VisionFrame AI queue system.
"""

import os
import json
import logging
import random
import subprocess
from celery import shared_task
from .db_utils import get_chunk_data, update_task_status, get_task_by_id
from .task_utils import task_completed, task_failed
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@shared_task(
    name='tasks.slideshow_tasks.create_slideshow',
    bind=True,
    max_retries=3,
    default_retry_delay=60
)
def create_slideshow(self, queue_id):
    """Create a slideshow with transitions for a content chunk."""
    logger.info(f"Creating slideshow for task {queue_id}")

    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the chunk data
        chunk_id = task['chunk_id']
        chunk_data = get_chunk_data(chunk_id)

        if not chunk_data:
            error_msg = f"Chunk data not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the previous task result (image generation)
        image_task = get_task_by_id(queue_id - 1)  # Assuming sequential IDs

        if not image_task or image_task['process_step'] != 'generate_images':
            error_msg = f"Image generation task not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the image paths from the previous task
        image_result_data = json.loads(image_task['result_data'])

        if not image_result_data or 'generated_images' not in image_result_data:
            error_msg = f"No images found in previous task result for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Create the slideshow
        slideshow_path = create_slideshow_with_ffmpeg(
            image_result_data['generated_images'],
            f"chunk_{chunk_id}_slideshow"
        )

        if not slideshow_path:
            error_msg = f"Failed to create slideshow for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Update the result data
        result_data = {
            'slideshow_path': slideshow_path,
            'num_images': len(image_result_data['generated_images']),
            'duration': calculate_slideshow_duration(len(image_result_data['generated_images']))
        }

        # Mark the task as completed
        task_completed.delay(queue_id, result_data)

        return f"Created slideshow for chunk {chunk_id}"

    except Exception as e:
        logger.error(f"Error creating slideshow for task {queue_id}: {str(e)}")

        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            # If max retries exceeded, mark the task as failed
            task_failed.delay(queue_id, str(e))

        return f"Error creating slideshow for task {queue_id}: {str(e)}"

def create_slideshow_with_ffmpeg(images, slideshow_name):
    """Create a slideshow with transitions using FFmpeg."""
    try:
        # Create the slideshows directory if it doesn't exist
        os.makedirs('slideshows', exist_ok=True)

        # Sort images by order
        sorted_images = sorted(images, key=lambda x: x['order'])
        image_paths = [img['image_path'] for img in sorted_images]

        if not image_paths:
            logger.error("No image paths provided")
            return None

        # Create a temporary file with the list of images
        list_file_path = f"slideshows/{slideshow_name}_list.txt"
        with open(list_file_path, 'w') as f:
            for img_path in image_paths:
                # Each image should be shown for 5 seconds
                f.write(f"file '{img_path}'\n")
                f.write(f"duration 5\n")
            # Add the last image again to ensure it's shown for the full duration
            f.write(f"file '{image_paths[-1]}'\n")

        # Output path for the slideshow
        slideshow_path = f"slideshows/{slideshow_name}.mp4"

        # FFmpeg command to create the slideshow with zoom and pan effects
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output file if it exists
            '-f', 'concat',
            '-safe', '0',
            '-i', list_file_path,
            '-filter_complex', f"[0:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,zoompan=z='min(zoom+0.0015,1.5)':d=125:s=1920x1080,fps=30[v]",
            '-map', '[v]',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-r', '30',
            '-shortest',
            slideshow_path
        ]

        # Execute the FFmpeg command
        subprocess.run(cmd, check=True)

        # Clean up the temporary file
        os.remove(list_file_path)

        logger.info(f"Slideshow created at {slideshow_path}")

        return slideshow_path

    except Exception as e:
        logger.error(f"Error creating slideshow with FFmpeg: {str(e)}")
        return None

def calculate_slideshow_duration(num_images):
    """Calculate the duration of the slideshow in seconds."""
    # Each image is shown for 5 seconds
    return num_images * 5
