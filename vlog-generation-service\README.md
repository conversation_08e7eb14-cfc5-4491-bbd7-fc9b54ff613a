# VisionFrame AI - Vlog Generation Service

## Overview

This service provides API endpoints for content generation, image creation, speech synthesis, and video production for the VisionFrame AI platform.

## Setup and Installation

### Prerequisites

- Python 3.9+
- MySQL Server
- Required API keys (see Environment Variables section)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
python -m pip install -r requirements.txt
```

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_DATABASE=vlog_generator

# API Keys
XAI_API_KEY=your_xai_api_key
DEEKSEEK_API_KEY=your_deepseek_api_key
GEMINI_API_KEY=your_gemini_api_key
RUNWARE_API_KEY=your_runware_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
```

## Running the Application

Start the consolidated API server:

```bash
python consolidated_api.py
```

The API will be available at http://localhost:5000/

## Troubleshooting

### MySQL Authentication Error

If you encounter the following error:

```
Error fetching categories: 'cryptography' package is required for sha256_password or caching_sha2_password auth methods
```

This means your MySQL server is using a secure authentication method that requires the 'cryptography' Python package. To fix this:

1. Install the cryptography package:

```bash
python -m pip install cryptography
```

2. Update your requirements.txt file to include this dependency:

```
cryptography>=44.0.2
```

### NoneType Object Has No Attribute 'execute' Error

If you encounter the following error:

```
Error in create_generated_content: 'NoneType' object has no attribute 'execute'
```

This error occurs when trying to use a database cursor before it's been initialized. The issue has been fixed in the latest version of the code, but if you're using an older version, make sure that you initialize the database connection and cursor before using them in any function.

### Content Prompt ID Does Not Exist Error

If you encounter the following error:

```
Content prompt ID X does not exist in the content_prompt table
```

This error occurs when you're trying to create generated content with a content prompt ID that doesn't exist in the content_prompt table. The application now checks both the content_prompt and content_prompt_claude tables for the content prompt ID, and if the ID exists in the content_prompt_claude table but not in the content_prompt table, it will automatically create a copy in the content_prompt table.

If you're using an older version of the code, you'll need to modify the `create_generated_content` function to check both tables for the content prompt ID and create a copy in the content_prompt table if needed.

### Foreign Key Constraint Error

If you encounter the following error:

```
Error creating generated content: (1452, 'Cannot add or update a child row: a foreign key constraint fails (`vlog_generator`.`generated_content`, CONSTRAINT `generated_content_ibfk_1` FOREIGN KEY (`content_prompt_id`) REFERENCES `content_prompt` (`id`) ON DELETE CASCADE)')
```

This error occurs because the `generated_content` table has a foreign key constraint that requires the `content_prompt_id` to exist in the `content_prompt` table. The application now automatically creates a copy of the content prompt from the `content_prompt_claude` table in the `content_prompt` table if needed.

## API Endpoints

The API provides endpoints for:

- Content generation
- Image creation
- Speech synthesis
- Video production

For detailed API documentation, refer to the API documentation section.
