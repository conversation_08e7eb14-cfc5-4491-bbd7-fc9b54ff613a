import React, { useState } from 'react';
import { Container, Row, Col, Nav, Tab, Alert, Card } from 'react-bootstrap';
import CategoryManagement from './settings/CategoryManagement';
import ApiSettings from './settings/ApiSettings';
import {
  Settings as SettingsIcon,
  Category as CategoryIcon,
  Api as ApiIcon
} from '@mui/icons-material';

const Settings = () => {
  const [activeKey, setActiveKey] = useState('category-management');
  const [error, setError] = useState(null);

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="d-flex align-items-center">
            <SettingsIcon className="me-2" fontSize="large" />
            Settings
          </h1>
        </Col>
      </Row>

      {error && (
        <Row className="mb-4">
          <Col>
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row>
        <Col md={3} lg={2} className="mb-4">
          <Nav variant="pills" className="flex-column" activeKey={activeKey} onSelect={(k) => setActiveKey(k)}>
            <Nav.Item>
              <Nav.Link eventKey="category-management" className="d-flex align-items-center">
                <CategoryIcon className="me-2" />
                Category Management
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="api-settings" className="d-flex align-items-center">
                <ApiIcon className="me-2" />
                API Settings
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="app-settings" className="d-flex align-items-center">
                <SettingsIcon className="me-2" />
                Application Settings
              </Nav.Link>
            </Nav.Item>
          </Nav>
        </Col>
        <Col md={9} lg={10}>
          <Tab.Content>
            <Tab.Pane eventKey="category-management" active={activeKey === 'category-management'}>
              <CategoryManagement setError={setError} />
            </Tab.Pane>
            <Tab.Pane eventKey="api-settings" active={activeKey === 'api-settings'}>
              <ApiSettings setError={setError} />
            </Tab.Pane>
            <Tab.Pane eventKey="app-settings" active={activeKey === 'app-settings'}>
              <Card>
                <Card.Header>
                  <h5 className="mb-0">Application Settings</h5>
                </Card.Header>
                <Card.Body>
                  <Alert variant="info">
                    <strong>Coming Soon!</strong> Application settings functionality is currently under development.
                  </Alert>
                </Card.Body>
              </Card>
            </Tab.Pane>
          </Tab.Content>
        </Col>
      </Row>
    </Container>
  );
};

export default Settings;
