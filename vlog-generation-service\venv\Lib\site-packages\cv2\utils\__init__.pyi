__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


from cv2.utils import fs as fs
from cv2.utils import nested as nested


# Classes
class ClassWithKeywordProperties:
    lambda_: int
    @property
    def except_(self) -> int: ...

    # Functions
    def __init__(self, lambda_arg: int = ..., except_arg: int = ...) -> None: ...



# Functions
@_typing.overload
def copyMatAndDumpNamedArguments(src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ..., lambda_: int = ..., sigma: float = ...) -> tuple[str, cv2.typing.MatLike]: ...
@_typing.overload
def copyMatAndDumpNamedArguments(src: cv2.UMat, dst: cv2.UMat | None = ..., lambda_: int = ..., sigma: float = ...) -> tuple[str, cv2.UMat]: ...

def dumpBool(argument: bool) -> str: ...

def dumpCString(argument: str) -> str: ...

def dumpDouble(argument: float) -> str: ...

def dumpFloat(argument: float) -> str: ...

@_typing.overload
def dumpInputArray(argument: cv2.typing.MatLike) -> str: ...
@_typing.overload
def dumpInputArray(argument: cv2.UMat) -> str: ...

@_typing.overload
def dumpInputArrayOfArrays(argument: _typing.Sequence[cv2.typing.MatLike]) -> str: ...
@_typing.overload
def dumpInputArrayOfArrays(argument: _typing.Sequence[cv2.UMat]) -> str: ...

@_typing.overload
def dumpInputOutputArray(argument: cv2.typing.MatLike) -> tuple[str, cv2.typing.MatLike]: ...
@_typing.overload
def dumpInputOutputArray(argument: cv2.UMat) -> tuple[str, cv2.UMat]: ...

@_typing.overload
def dumpInputOutputArrayOfArrays(argument: _typing.Sequence[cv2.typing.MatLike]) -> tuple[str, _typing.Sequence[cv2.typing.MatLike]]: ...
@_typing.overload
def dumpInputOutputArrayOfArrays(argument: _typing.Sequence[cv2.UMat]) -> tuple[str, _typing.Sequence[cv2.UMat]]: ...

def dumpInt(argument: int) -> str: ...

def dumpInt64(argument: int) -> str: ...

def dumpRange(argument: cv2.typing.Range) -> str: ...

def dumpRect(argument: cv2.typing.Rect) -> str: ...

def dumpRotatedRect(argument: cv2.typing.RotatedRect) -> str: ...

def dumpSizeT(argument: int) -> str: ...

def dumpString(argument: str) -> str: ...

def dumpTermCriteria(argument: cv2.typing.TermCriteria) -> str: ...

def dumpVec2i(value: cv2.typing.Vec2i = ...) -> str: ...

def dumpVectorOfDouble(vec: _typing.Sequence[float]) -> str: ...

def dumpVectorOfInt(vec: _typing.Sequence[int]) -> str: ...

def dumpVectorOfRect(vec: _typing.Sequence[cv2.typing.Rect]) -> str: ...

def generateVectorOfInt(len: int) -> _typing.Sequence[int]: ...

def generateVectorOfMat(len: int, rows: int, cols: int, dtype: int, vec: _typing.Sequence[cv2.typing.MatLike] | None = ...) -> _typing.Sequence[cv2.typing.MatLike]: ...

def generateVectorOfRect(len: int) -> _typing.Sequence[cv2.typing.Rect]: ...

@_typing.overload
def testAsyncArray(argument: cv2.typing.MatLike) -> cv2.AsyncArray: ...
@_typing.overload
def testAsyncArray(argument: cv2.UMat) -> cv2.AsyncArray: ...

def testAsyncException() -> cv2.AsyncArray: ...

@_typing.overload
def testOverloadResolution(value: int, point: cv2.typing.Point = ...) -> str: ...
@_typing.overload
def testOverloadResolution(rect: cv2.typing.Rect) -> str: ...

def testOverwriteNativeMethod(argument: int) -> int: ...

def testRaiseGeneralException() -> None: ...

def testReservedKeywordConversion(positional_argument: int, lambda_: int = ..., from_: int = ...) -> str: ...

def testRotatedRect(x: float, y: float, w: float, h: float, angle: float) -> cv2.typing.RotatedRect: ...

def testRotatedRectVector(x: float, y: float, w: float, h: float, angle: float) -> _typing.Sequence[cv2.typing.RotatedRect]: ...


