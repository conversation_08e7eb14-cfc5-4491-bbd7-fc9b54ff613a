import sqlite3
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    for column in columns:
        if column[1] == column_name:
            return True
    return False

def main():
    """Main function to update the database schema."""
    try:
        # Connect to the database
        conn = sqlite3.connect('vlog_content.db')
        cursor = conn.cursor()
        
        # Check if content_prompt_claude table exists
        if not check_table_exists(cursor, 'content_prompt_claude'):
            logger.info("content_prompt_claude table does not exist. Creating it...")
            cursor.execute("""
                CREATE TABLE content_prompt_claude (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    category_id INTEGER,
                    content_type TEXT DEFAULT 'blog_post',
                    purpose TEXT DEFAULT 'inform',
                    audience TEXT DEFAULT 'general',
                    tone TEXT DEFAULT 'conversational',
                    style TEXT DEFAULT 'descriptive',
                    word_count TEXT DEFAULT '500',
                    persona TEXT DEFAULT 'expert',
                    content_prompt TEXT,
                    call_to_action TEXT,
                    output_format TEXT DEFAULT 'standard',
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
            logger.info("content_prompt_claude table created successfully!")
        else:
            logger.info("content_prompt_claude table exists. Checking for content_prompt column...")
            
            # Check if content_prompt column exists
            if not check_column_exists(cursor, 'content_prompt_claude', 'content_prompt'):
                logger.info("content_prompt column does not exist. Adding it...")
                cursor.execute("ALTER TABLE content_prompt_claude ADD COLUMN content_prompt TEXT")
                conn.commit()
                logger.info("content_prompt column added successfully!")
            else:
                logger.info("content_prompt column already exists.")
        
        # Close the connection
        conn.close()
        logger.info("Database schema update completed successfully!")
        
    except Exception as e:
        logger.error(f"Error updating database schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    main()
