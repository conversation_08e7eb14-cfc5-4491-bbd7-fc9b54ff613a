"""
Setup script for the Media Generation Pipeline Service

This script:
1. Tests the database connection
2. Creates required tables if they don't exist
3. Validates the environment configuration
"""

import os
import sys
import pymysql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'db': os.getenv('DB_DATABASE', 'vlog_generator'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
}

# Required tables and their creation SQL
REQUIRED_TABLES = {
    'content_chunk': """
        CREATE TABLE IF NOT EXISTS content_chunk (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_id INT NOT NULL,
            chunk_order INT NOT NULL,
            text TEXT NOT NULL,
            processing_status VARCHAR(20) DEFAULT 'pending',
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (content_id) REFERENCES generated_content(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """,
    'event_logs': """
        CREATE TABLE IF NOT EXISTS event_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            event_type VARCHAR(100) NOT NULL,
            status VARCHAR(20) DEFAULT 'INFO',
            message TEXT,
            error_details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """,
    'process_queue': """
        CREATE TABLE IF NOT EXISTS process_queue (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_id INT,
            chunk_id INT,
            process_step VARCHAR(50) NOT NULL,
            step_order INT NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            error_message TEXT,
            result_data TEXT,
            start_time DATETIME,
            end_time DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """
}

# Required directories
REQUIRED_DIRS = [
    'content-csv',
    'content-images',
    'content-speech',
    'content-video',
    'content-thumbnail'
]

def test_db_connection():
    """Test the database connection"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"✅ Successfully connected to database '{DB_CONFIG['db']}' on {DB_CONFIG['host']}")
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def create_required_tables():
    """Create required tables if they don't exist"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Get existing tables
        cursor.execute("SHOW TABLES")
        existing_tables = [table[f"Tables_in_{DB_CONFIG['db']}"] for table in cursor.fetchall()]
        
        # Create missing tables
        for table_name, create_sql in REQUIRED_TABLES.items():
            if table_name in existing_tables:
                print(f"✅ Table '{table_name}' already exists")
            else:
                print(f"⚠️ Creating table '{table_name}'...")
                cursor.execute(create_sql)
                print(f"✅ Table '{table_name}' created successfully")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Failed to create tables: {str(e)}")
        return False

def check_required_columns():
    """Check if required columns exist in tables"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check content_chunk table for processing_status column
        cursor.execute("SHOW COLUMNS FROM content_chunk LIKE 'processing_status'")
        if not cursor.fetchone():
            print("⚠️ Adding processing_status column to content_chunk table...")
            cursor.execute("""
                ALTER TABLE content_chunk 
                ADD COLUMN processing_status VARCHAR(20) DEFAULT 'pending' AFTER text
            """)
            print("✅ Added processing_status column")
        else:
            print("✅ processing_status column exists in content_chunk table")
            
        # Check process_queue table for start_time and end_time columns
        cursor.execute("SHOW COLUMNS FROM process_queue LIKE 'start_time'")
        if not cursor.fetchone():
            print("⚠️ Adding start_time column to process_queue table...")
            cursor.execute("""
                ALTER TABLE process_queue 
                ADD COLUMN start_time DATETIME AFTER result_data
            """)
            print("✅ Added start_time column")
        else:
            print("✅ start_time column exists in process_queue table")
            
        cursor.execute("SHOW COLUMNS FROM process_queue LIKE 'end_time'")
        if not cursor.fetchone():
            print("⚠️ Adding end_time column to process_queue table...")
            cursor.execute("""
                ALTER TABLE process_queue 
                ADD COLUMN end_time DATETIME AFTER start_time
            """)
            print("✅ Added end_time column")
        else:
            print("✅ end_time column exists in process_queue table")
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Failed to check/add required columns: {str(e)}")
        return False

def create_required_directories():
    """Create required directories if they don't exist"""
    for directory in REQUIRED_DIRS:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"✅ Created directory: {directory}")
            except Exception as e:
                print(f"❌ Failed to create directory {directory}: {str(e)}")
        else:
            print(f"✅ Directory exists: {directory}")

def check_environment_variables():
    """Check if required environment variables are set"""
    required_vars = [
        'DB_HOST',
        'DB_USER',
        'DB_PASSWORD',
        'DB_DATABASE',
        'RUNWARE_API_KEY',
        'FISHAUDIO_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"  - {var}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def main():
    """Main function to run all setup checks"""
    print("\n=== Media Generation Pipeline Setup ===\n")
    
    # Test database connection
    print("\n--- Database Connection ---")
    if not test_db_connection():
        print("❌ Setup failed: Database connection error")
        return False
    
    # Create required tables
    print("\n--- Database Tables ---")
    if not create_required_tables():
        print("❌ Setup failed: Could not create required tables")
        return False
    
    # Check required columns
    print("\n--- Database Columns ---")
    if not check_required_columns():
        print("❌ Setup failed: Could not verify/add required columns")
        return False
    
    # Create required directories
    print("\n--- Directories ---")
    create_required_directories()
    
    # Check environment variables
    print("\n--- Environment Variables ---")
    env_check = check_environment_variables()
    
    # Final status
    print("\n--- Setup Summary ---")
    if env_check:
        print("✅ Setup completed successfully!")
        return True
    else:
        print("⚠️ Setup completed with warnings. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
