"""
Celery Configuration for VisionFrame AI

This file contains the configuration for <PERSON><PERSON><PERSON>, including broker and backend settings,
task routes, and scheduled tasks.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Broker settings
broker_url = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
result_backend = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# Task serialization format
task_serializer = 'json'
accept_content = ['json']
result_serializer = 'json'

# Task execution settings
task_acks_late = True
worker_prefetch_multiplier = 1
task_ignore_result = False
task_store_errors_even_if_ignored = True

# Task time limits
task_time_limit = 3600  # 1 hour
task_soft_time_limit = 3000  # 50 minutes

# Task routes
task_routes = {
    'tasks.image_tasks.*': {'queue': 'image_tasks'},
    'tasks.speech_tasks.*': {'queue': 'speech_tasks'},
    'tasks.slideshow_tasks.*': {'queue': 'slideshow_tasks'},
    'tasks.subtitle_tasks.*': {'queue': 'subtitle_tasks'},
    'tasks.video_tasks.*': {'queue': 'video_tasks'},
    'tasks.media_tasks.*': {'queue': 'media_tasks'},
    'tasks.queue_manager.*': {'queue': 'queue_manager'},
}

# Beat schedule
beat_schedule = {
    'check-pending-tasks-every-10-seconds': {
        'task': 'tasks.queue_manager.check_pending_tasks',
        'schedule': 10.0,
    },
}

# Logging
worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'
