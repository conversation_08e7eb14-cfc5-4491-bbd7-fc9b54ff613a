import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Table, Spinner, Alert } from 'react-bootstrap';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

const ContentManagementNew = () => {
  const [contentPrompts, setContentPrompts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchContentPrompts();
  }, []);

  const fetchContentPrompts = async () => {
    try {
      console.log('Fetching content prompts...');
      setLoading(true);

      try {
        const response = await fetch(`${API_BASE_URL}/api/content-prompts`);

        // Even if we get a 500 error, try to parse the response
        const data = await response.json();
        console.log('Fetched data:', data);

        if (!response.ok) {
          // If the server returned an error message, display it
          if (data && data.error) {
            throw new Error(data.error);
          } else {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        }

        // If we got here, the response was successful
        setContentPrompts(Array.isArray(data) ? data : []);
      } catch (fetchError) {
        console.error('Error fetching content:', fetchError);

        // If the API is not available, show a message and use empty data
        console.log('API might not be available, using empty data');
        setContentPrompts([]);
        setError('API is not available. Please make sure the backend server is running.');
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1>Content Management</h1>
        </Col>
      </Row>

      {loading ? (
        <div className="text-center my-5">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      ) : error ? (
        <>
          <Alert variant="danger">
            <Alert.Heading>Error</Alert.Heading>
            <p>{error}</p>
          </Alert>
          <div className="mt-4">
            <h5>Troubleshooting Steps:</h5>
            <ol>
              <li>Make sure the backend API server is running</li>
              <li>Check if the database is properly configured</li>
              <li>Verify that the required tables exist in the database</li>
              <li>Check the API logs for more detailed error information</li>
            </ol>
          </div>
        </>
      ) : (
        <Table striped bordered hover responsive>
          <thead>
            <tr>
              <th>ID</th>
              <th>Title</th>
              <th>Category</th>
              <th>Generated At</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(contentPrompts) && contentPrompts.length > 0 ? (
              contentPrompts.map((prompt) => (
                <tr key={prompt.id}>
                  <td>{prompt.id}</td>
                  <td>{prompt.title}</td>
                  <td>{prompt.category_name}</td>
                  <td>
                    {prompt.generated_at ? new Date(prompt.generated_at).toLocaleDateString() : 'N/A'}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4} className="text-center">
                  No content prompts found
                </td>
              </tr>
            )}
          </tbody>
        </Table>
      )}
    </Container>
  );
};

export default ContentManagementNew;
