#!/bin/bash

echo "Starting VisionFrame AI Queue System..."

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    echo "Redis is not installed."
    echo "Please install Redis using your package manager:"
    echo "  Ubuntu/Debian: sudo apt-get install redis-server"
    echo "  CentOS/RHEL: sudo yum install redis"
    echo "  macOS: brew install redis"
    exit 1
fi

# Start Redis if not already running
echo "Checking if Redis is running..."
if ! redis-cli ping &> /dev/null; then
    echo "Starting Redis..."
    redis-server &
    sleep 3
fi

# Start the queue system
echo "Starting the queue system..."
python3 start_all.py
