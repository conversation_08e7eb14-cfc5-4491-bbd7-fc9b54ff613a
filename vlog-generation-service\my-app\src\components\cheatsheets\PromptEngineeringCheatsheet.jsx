import React from 'react';
import { <PERSON>, <PERSON>, Row, Col, Badge, Alert } from 'react-bootstrap';
import GrokPromptGenerator from './GrokPromptGenerator';
import './Cheatsheets.css';

const PromptEngineeringCheatsheet = () => {
  return (
    <div className="cheatsheet-container">
      <GrokPromptGenerator promptType="content" />
      <Card className="mb-4">
        <Card.Header className="bg-dark text-white">
          <h4 className="mb-0">📚 Prompt Engineering Cheatsheet for Content Writing, Blogging & Vlogging (2025 Pro Edition)</h4>
        </Card.Header>
        <Card.Body>
          <p className="lead">
            Maximize your content creation workflow using this AI prompt engineering cheatsheet—custom-crafted for writers, vloggers, bloggers, and content strategists. From short-form video scripts to long-form blog planning, this guide helps you communicate effectively with AI tools like ChatGPT to get consistent, accurate, and engaging outputs.
          </p>
        </Card.Body>
      </Card>

      {/* Purpose Section */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🔍 Purpose</h5>
        </Card.Header>
        <Card.Body>
          <p>
            This cheatsheet is designed to help content creators harness the full potential of AI tools for content planning, scripting, storytelling, and optimization. Whether you're creating TikTok reels, YouTube vlogs, or SEO-friendly blog posts, this guide empowers you to prompt smarter and produce faster.
          </p>
        </Card.Body>
      </Card>

      {/* Content Goal Framework */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎯 Content Goal Framework (C.A.T.S.)</h5>
        </Card.Header>
        <Card.Body>
          <Alert variant="info">
            <strong>C.A.T.S. = Content Type + Audience + Tone + Style</strong>
          </Alert>

          <h6 className="mt-3">Example:</h6>
          <ul>
            <li><strong>Content:</strong> Short-form vlog script</li>
            <li><strong>Audience:</strong> Millennials struggling with burnout</li>
            <li><strong>Tone:</strong> Empathetic and motivational</li>
            <li><strong>Style:</strong> Conversational, with storytelling</li>
          </ul>
        </Card.Body>
      </Card>

      {/* Universal Prompt Template */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🧩 Universal Prompt Template</h5>
        </Card.Header>
        <Card.Body>
          <div className="bg-light p-3 rounded mb-3">
            <pre className="mb-0">
              Act as a professional [role].<br />
              Create a [content type] about [topic] for [audience].<br />
              Keep the tone [tone], and structure it with [format/rules].
            </pre>
          </div>

          <h6>Example:</h6>
          <blockquote className="blockquote">
            <p className="mb-0">
              Act as a mental health coach.<br />
              Create a 1-minute vlog script about daily habits to reduce stress for college students.<br />
              Keep it calming and warm, with a 3-point structure.
            </p>
          </blockquote>
        </Card.Body>
      </Card>

      {/* Scriptwriting Prompt Structures */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">📜 Scriptwriting Prompt Structures</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>🎬 Short-Form Script</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  Write a [duration] script with:<br />
                  - Hook (0–3s)<br />
                  - 2–3 Value Points<br />
                  - Clear CTA
                </pre>
              </div>
            </Col>
            <Col md={6}>
              <h6>🎥 Long-Form Script</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  Structure:<br />
                  - Hook<br />
                  - Introduction<br />
                  - 3–5 Key Points<br />
                  - Summary/Takeaway<br />
                  - CTA
                </pre>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Content Planning Prompts */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🗓 Content Planning Prompts</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>Monthly/Weekly Calendar:</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  Create a [monthly/weekly] content calendar on [topic].<br />
                  Include titles, formats (reel, blog, script), and goals.
                </pre>
              </div>
            </Col>
            <Col md={6}>
              <h6>Persona-Specific Ideas:</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  Generate content ideas for [persona] interested in [topic].<br />
                  Include pain points, desires, and keywords.
                </pre>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Vlog-Specific Prompt Examples */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎥 Vlog-Specific Prompt Examples</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>Storytelling Prompt</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  Write a heartfelt 90-second vlog script about [experience/topic].<br />
                  Make the audience feel [emotion].<br />
                  Include personal reflection and a lesson.
                </pre>
              </div>
            </Col>
            <Col md={6}>
              <h6>Explainer Prompt (F.I.R.E. Method)</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
                  F = Fast hook<br />
                  I = Insight<br />
                  R = Real example<br />
                  E = End with CTA
                </pre>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Blogging Prompt Enhancers */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">✍️ Blogging Prompt Enhancers</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Goal</th>
                <th>Prompt Example</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>SEO Optimized</td>
                <td>"Write a blog about [topic] targeting '[keyword]'."</td>
              </tr>
              <tr>
                <td>Personal Voice</td>
                <td>"Rewrite this blog in a casual, friend-like tone."</td>
              </tr>
              <tr>
                <td>Data-Based</td>
                <td>"Summarize latest studies about [topic]."</td>
              </tr>
              <tr>
                <td>Step-by-Step Guide</td>
                <td>"Create a how-to blog with clear steps for [task]."</td>
              </tr>
              <tr>
                <td>Listicle Format</td>
                <td>"Write a Top 7 listicle about [niche topic]."</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Hooks & CTAs */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">📣 Hooks & CTAs</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>Hook Templates:</h6>
              <ul>
                <li>"What no one tells you about [topic]..."</li>
                <li>"If you're [pain point], this is for you…"</li>
                <li>"Here's how I [achieved X] without [obstacle]"</li>
              </ul>
            </Col>
            <Col md={6}>
              <h6>CTA Templates:</h6>
              <ul>
                <li>"Try this today and comment how it works."</li>
                <li>"Follow for more tips on [goal]."</li>
                <li>"Tag someone who needs this."</li>
              </ul>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Refinement Loops */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🧠 Refinement Loops</h5>
        </Card.Header>
        <Card.Body>
          <p>Improve your AI output in steps:</p>
          <ol>
            <li><strong>First Draft</strong> – General script or outline</li>
            <li><strong>Improve Hook</strong> – Make it punchy and emotional</li>
            <li><strong>Enhance Structure</strong> – Add sections or transitions</li>
            <li><strong>Tone Shift</strong> – Adjust for humor, formality, etc.</li>
            <li><strong>Final CTA</strong> – Add a compelling close or action step</li>
          </ol>
        </Card.Body>
      </Card>

      {/* Bonus Prompt Starters */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🛠 Bonus Prompt Starters</h5>
        </Card.Header>
        <Card.Body>
          <ul className="list-group">
            <li className="list-group-item">
              <strong>Idea Generator</strong><br />
              <em>"Give me 10 vlog ideas on [topic] with hooks and outlines."</em>
            </li>
            <li className="list-group-item">
              <strong>Viral Script Format</strong><br />
              <em>"Create a TikTok script in the style of viral storytelling."</em>
            </li>
            <li className="list-group-item">
              <strong>Visual Script Prompt</strong><br />
              <em>"Pair this script with footage of [location/action]. Add narrator lines."</em>
            </li>
            <li className="list-group-item">
              <strong>Before & After Story</strong><br />
              <em>"Write a transformation script showing results of [habit/lesson]."</em>
            </li>
          </ul>
        </Card.Body>
      </Card>

      {/* Credits */}
      <Card className="mb-4">
        <Card.Footer className="text-center">
          <p className="mb-0">
            <small>
              Created by <strong>PromptGPT with 10x iteration logic</strong><br />
              Optimized for creators, storytellers, marketers, and growth hackers in the age of AI.
            </small>
          </p>
        </Card.Footer>
      </Card>
    </div>
  );
};

export default PromptEngineeringCheatsheet;
