import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>, Row, Col, Card, <PERSON>, Button, Modal, <PERSON>,
  Spinner, <PERSON><PERSON>, Badge, Pagination, InputGroup, FormControl, Accordion
} from 'react-bootstrap';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ContentCut as ContentCutIcon
} from '@mui/icons-material';
import { formatDate } from '../utils/formatters';
import './SpeechContent.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';
console.log('API_BASE_URL:', API_BASE_URL); // Debug log

// Default form data for a new generated content
const DEFAULT_GENERATED_CONTENT = {
  content_prompt_id: '',
  title: '',
  content_text: '',
  speech_duration: '',
  speech_location: '',
  num_images: ''
};

const ContentDetails = () => {
  // State for generated content data
  const [generatedContents, setGeneratedContents] = useState([]);
  // State for storing multiple generated responses from Grok
  const [generatedResponses, setGeneratedResponses] = useState([]);
  const [contentPrompts, setContentPrompts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for CRUD operations
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showChunkModal, setShowChunkModal] = useState(false);
  const [currentContent, setCurrentContent] = useState(null);
  const [formData, setFormData] = useState({ ...DEFAULT_GENERATED_CONTENT });
  const [formErrors, setFormErrors] = useState({});
  const [actionSuccess, setActionSuccess] = useState(null);
  const [actionError, setActionError] = useState(null);
  const [isGeneratingChunks, setIsGeneratingChunks] = useState(false);
  const [chunkResult, setChunkResult] = useState(null);

  // State for selected content prompt details
  const [selectedPromptDetails, setSelectedPromptDetails] = useState(null);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);

  // State for pagination and search
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch generated content data
  const fetchGeneratedContents = async () => {
    try {
      console.log('Fetching generated contents from:', `${API_BASE_URL}/api/generated-content`);
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/api/generated-content`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch generated content: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Fetched data:', data);
      setGeneratedContents(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching generated content:', error);
      setError(`Failed to load generated content: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch content prompts for dropdown
  const fetchContentPrompts = async () => {
    try {
      console.log('Fetching content prompts from:', `${API_BASE_URL}/api/content-prompts`);

      const response = await fetch(`${API_BASE_URL}/api/content-prompts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Content prompts response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Content prompts error response:', errorText);
        throw new Error(`Failed to fetch content prompts: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Fetched content prompts:', data);
      const prompts = Array.isArray(data) ? data : [];
      setContentPrompts(prompts);

      if (prompts.length === 0) {
        console.warn('No content prompts found. Content prompt selection is required.');
      } else {
        console.log(`Loaded ${prompts.length} content prompts successfully`);
      }

      return prompts;
    } catch (error) {
      console.error('Error fetching content prompts:', error);
      // We don't set the error state here to avoid blocking the main functionality
      return [];
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }

    // If content_prompt_id changes, fetch the prompt details
    if (name === 'content_prompt_id' && value) {
      fetchContentPromptDetails(value);
    } else if (name === 'content_prompt_id' && !value) {
      setSelectedPromptDetails(null);
    }
  };

  // Fetch content prompt details
  const fetchContentPromptDetails = async (promptId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/content-prompts/${promptId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch content prompt details');
      }

      const data = await response.json();
      setSelectedPromptDetails(data);

      // Store the prompt details but don't update the title field
      // We want to keep the Grok-generated title if it exists
      setFormData(prev => ({
        ...prev,
        // Don't update the title field here to preserve the Grok-generated title
      }));
    } catch (error) {
      console.error('Error fetching content prompt details:', error);
      setActionError('Failed to load content prompt details');
      setSelectedPromptDetails(null);
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = {};

    // Validate that a content prompt is selected
    if (!formData.content_prompt_id) {
      errors.content_prompt_id = 'Please select a content prompt';
    }

    // Validate that a title exists (should be from Grok)
    if (!formData.title) {
      errors.title = 'A title is required. Please generate content to get a title';
    }

    // When saving, validate that content has been generated
    if (!formData.content_text) {
      errors.content_text = 'Please generate content before saving';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Generate content using Grok API
  const generateContent = async () => {
    if (!formData.content_prompt_id) {
      setActionError('Please select a content prompt');
      return;
    }

    try {
      setActionError(null);
      setIsGeneratingContent(true);

      // Get the content prompt details if not already loaded
      if (!selectedPromptDetails) {
        await fetchContentPromptDetails(formData.content_prompt_id);
      }

      // Ensure we have the content prompt text
      if (!selectedPromptDetails?.content_prompt) {
        setActionError('Failed to load content prompt details. Please try again.');
        setIsGeneratingContent(false);
        return;
      }

      const contentPromptText = selectedPromptDetails.content_prompt;

      // Get the number of responses to generate (default to 1)
      const numResponses = formData.num_responses || 1;
      console.log(`Generating ${numResponses} response(s)`);

      // Call Grok API to generate content using regular HTTP request
      const response = await fetch(`${API_BASE_URL}/api/grok/generate-content-from-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content_prompt: contentPromptText,
          num_responses: numResponses
        })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to generate content';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        throw new Error(errorMessage);
      }

      let data;
      try {
        data = await response.json();
        console.log('Response data from Grok API:', data);
      } catch (e) {
        console.error('Error parsing response JSON:', e);
        throw new Error('Failed to parse response from server');
      }

      // Check if we have valid data
      if (!data) {
        console.error('No response data from Grok API');
        throw new Error('No response data from server. Please try again.');
      }

      // Check if we received an array (multiple responses) or a single object
      const isMultipleResponses = Array.isArray(data);
      console.log(`Received ${isMultipleResponses ? 'multiple' : 'single'} response format`);

      if (isMultipleResponses) {
        // For multiple responses
        if (data.length === 0) {
          console.error('Received empty array from Grok API');
          throw new Error('No content was generated. Please try again.');
        }

        // Log the number of responses received
        console.log(`Received ${data.length} responses from Grok API`);

        // Validate each response in the array
        data.forEach((item, index) => {
          if (!item.title && !item.Title) {
            console.warn(`Response ${index + 1} is missing a title`);
          }
          if (!item.content && !item.Content) {
            console.warn(`Response ${index + 1} is missing content`);
          }

          // Normalize the response format (Title -> title, Content -> content)
          if (item.Title && !item.title) {
            item.title = item.Title;
          }
          if (item.Content && !item.content) {
            item.content = item.Content;
          }
        });

        // Use the first response for the form data
        const firstResponse = data[0];
        console.log('Using first response for form data:');
        console.log('Title:', firstResponse.title);
        console.log('Content length:', firstResponse.content?.length || 0);

        // Store all responses for later use
        setGeneratedResponses(data);
      } else {
        // For single response
        // Ensure we have both title and content
        if (!data.title && !data.Title) {
          console.error('Missing title in Grok response:', data);
          throw new Error('The generated content is missing a title. Please try again.');
        }

        if (!data.content && !data.Content) {
          console.error('Missing content in Grok response:', data);
          throw new Error('The generated content is empty. Please try again.');
        }

        // Normalize the response format (Title -> title, Content -> content)
        if (data.Title && !data.title) {
          data.title = data.Title;
        }
        if (data.Content && !data.content) {
          data.content = data.Content;
        }

        console.log('Successfully received title and content from Grok API');
        console.log('Title:', data.title);
        console.log('Content length:', data.content.length);

        // Store the single response as an array with one item
        setGeneratedResponses([data]);
      }

      // Get the data to use for the form (first response if multiple)
      const responseToUse = isMultipleResponses ? data[0] : data;

      // Get the title from Grok response and truncate if needed
      let title = responseToUse.title; // We've already validated and normalized this
      if (title.length > 250) {
        console.log(`Title too long (${title.length} chars), truncating to 250 chars`);
        title = title.substring(0, 247) + '...';
      }

      // Update form with generated content from Grok API
      const updatedFormData = {
        ...formData,
        title: title, // This will be inserted into the title column and displayed in the input field
        content_text: responseToUse.content, // This will be inserted into the content_text column
        thumbnail_prompt: responseToUse.thumbnail_prompt || '', // This will be inserted into the thumbnail_prompt column
        num_responses: numResponses // Store the number of responses requested
      };

      console.log('Updated form data with Grok response:');
      console.log('- Title (for title column):', updatedFormData.title);
      console.log('- Content (for content_text column):', updatedFormData.content_text.substring(0, 100) + '...');
      console.log('- Thumbnail prompt:', updatedFormData.thumbnail_prompt ? updatedFormData.thumbnail_prompt.substring(0, 100) + '...' : 'None');
      console.log('- Total responses generated:', generatedResponses.length);

      // Set the form data with the updated values
      setFormData(updatedFormData);

      // Force a re-render to show the updated content
      setTimeout(() => {
        const textArea = document.querySelector('textarea[name="content_text"]');
        if (textArea) {
          textArea.style.height = 'auto';
          textArea.style.height = textArea.scrollHeight + 'px';
        }
      }, 100);

      // Show success message
      setActionSuccess('Content generated successfully! You can now save it by clicking the "Save Generated Content" button.');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating content:', error);
      setActionError(error.message);

      // Check if we received a structured error response with content
      if (error.response && error.response.data && error.response.data.content) {
        // Still update the form with the error content so the user can see what went wrong
        setFormData(prev => ({
          ...prev,
          title: error.response.data.title || 'Error Generating Content',
          content_text: error.response.data.content || error.message,
          thumbnail_prompt: ''
        }));
      }
    } finally {
      setIsGeneratingContent(false);
    }
  };

  // Handle add content - now just saves the already generated content
  const handleAddContent = async () => {
    if (!validateForm()) {
      return;
    }

    // Double-check that content_prompt_id is provided
    if (!formData.content_prompt_id) {
      setActionError('Content prompt is required. Please select a content prompt.');
      return;
    }

    try {
      setActionError(null);
      setIsGeneratingContent(true);

      // Prepare data for saving to database
      // Since we're now using React's state management properly, formData.title will reflect any edits the user made
      const contentToSave = {
        content_prompt_id: formData.content_prompt_id,
        title: formData.title, // This is the Grok-generated title that may have been edited by the user
        content_text: formData.content_text, // This is the Grok-generated content that will go into the content_text column
        thumbnail_prompt: formData.thumbnail_prompt || '', // This is the Grok-generated thumbnail prompt
        speech_duration: 0,
        speech_location: '',
        num_images: 0
      };

      console.log('Saving content with title:', contentToSave.title);

      // Log the data that will be saved to the database
      console.log('Saving Grok-generated data to database:');
      console.log('- Title (for title column):', contentToSave.title);
      console.log('- Content (for content_text column):', contentToSave.content_text.substring(0, 100) + '...');
      console.log('- Thumbnail prompt:', contentToSave.thumbnail_prompt ? contentToSave.thumbnail_prompt.substring(0, 100) + '...' : 'None');

      // First, check if the title already exists in the database
      console.log('Checking if title already exists:', contentToSave.title);
      const checkTitleResponse = await fetch(`${API_BASE_URL}/api/generated-content/check-title`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title: contentToSave.title })
      });

      if (!checkTitleResponse.ok) {
        const errorData = await checkTitleResponse.json();
        throw new Error(errorData.error || 'Failed to check title existence');
      }

      const titleCheckData = await checkTitleResponse.json();

      if (titleCheckData.exists) {
        // Title already exists, show a confirmation dialog
        if (!window.confirm(`A content with the title "${titleCheckData.title}" already exists. Do you want to create a duplicate?`)) {
          // User chose not to create a duplicate
          setIsGeneratingContent(false);
          return;
        }
        // User confirmed to create a duplicate, proceed with saving
        console.log('User confirmed to create duplicate title:', contentToSave.title);
      }

      console.log('Saving content to database:', contentToSave);
      console.log('Content text length:', contentToSave.content_text.length);
      console.log('Title:', contentToSave.title);
      console.log('Content prompt ID:', contentToSave.content_prompt_id);

      // Save the generated content to the database
      const saveResponse = await fetch(`${API_BASE_URL}/api/generated-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contentToSave)
      });

      if (!saveResponse.ok) {
        const errorData = await saveResponse.json();
        throw new Error(errorData.error || 'Failed to save generated content');
      }

      // Refresh the data
      await fetchGeneratedContents();

      // Reset form and close modal
      setFormData({ ...DEFAULT_GENERATED_CONTENT });
      setSelectedPromptDetails(null);
      setShowAddModal(false);

      // Show success message with details about what was saved
      setActionSuccess(`Content saved successfully! The title "${contentToSave.title}" and its content have been inserted into the database.`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating/saving content:', error);
      setActionError(error.message);
    } finally {
      setIsGeneratingContent(false);
    }
  };

  // Handle edit content
  const handleEditContent = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setActionError(null);
      setIsGeneratingContent(true);

      // Check if the title has been changed
      if (formData.title !== currentContent.title) {
        // Check if the new title already exists in the database
        const checkTitleResponse = await fetch(`${API_BASE_URL}/api/generated-content/check-title`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ title: formData.title })
        });

        if (!checkTitleResponse.ok) {
          const errorData = await checkTitleResponse.json();
          throw new Error(errorData.error || 'Failed to check title existence');
        }

        const titleCheckData = await checkTitleResponse.json();

        if (titleCheckData.exists) {
          // Title already exists, show a confirmation dialog
          if (!window.confirm(`A content with the title "${titleCheckData.title}" already exists. Do you want to use this title anyway?`)) {
            // User chose not to use the duplicate title
            setIsGeneratingContent(false);
            return;
          }
          // User confirmed to use the duplicate title, proceed with updating
          console.log('User confirmed to use duplicate title:', formData.title);
        }
      }

      const response = await fetch(`${API_BASE_URL}/api/generated-content/${currentContent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update content');
      }

      // Refresh the data
      await fetchGeneratedContents();

      // Reset form and close modal
      setFormData({ ...DEFAULT_GENERATED_CONTENT });
      setShowEditModal(false);

      // Show success message
      setActionSuccess('Content updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating content:', error);
      setActionError(error.message);
    } finally {
      setIsGeneratingContent(false);
    }
  };

  // Handle delete content
  const handleDeleteContent = async () => {
    try {
      setActionError(null);

      const response = await fetch(`${API_BASE_URL}/api/generated-content/${currentContent.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete content');
      }

      // Refresh the data
      await fetchGeneratedContents();

      // Close modal
      setShowDeleteModal(false);

      // Show success message
      setActionSuccess('Content deleted successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error deleting content:', error);
      setActionError(error.message);
    }
  };

  // Open edit modal
  const openEditModal = (content) => {
    setCurrentContent(content);
    setFormData({
      content_prompt_id: content.content_prompt_id || '',
      title: content.title || '',
      content_text: content.content_text || '',
      thumbnail_prompt: content.thumbnail_prompt || '',
      speech_duration: content.speech_duration || '',
      speech_location: content.speech_location || '',
      num_images: content.num_images || ''
    });
    setShowEditModal(true);
  };

  // Open view modal
  const openViewModal = (content) => {
    setCurrentContent(content);
    setShowViewModal(true);
  };

  // Open delete modal
  const openDeleteModal = (content) => {
    setCurrentContent(content);
    setShowDeleteModal(true);
  };

  // State for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [existingChunksCount, setExistingChunksCount] = useState(0);

  // Open generate chunks modal
  const openChunkModal = async (content) => {
    setCurrentContent(content);
    setChunkResult(null);
    setActionError(null);
    setActionSuccess(null);

    // Check if chunks already exist for this content
    try {
      const response = await fetch(`${API_BASE_URL}/api/check-chunks/${content.id}`);
      if (!response.ok) {
        throw new Error('Failed to check existing chunks');
      }

      const result = await response.json();
      if (result.exists) {
        setExistingChunksCount(result.count);
        setShowConfirmDialog(true);
      } else {
        // No existing chunks, proceed to show the modal
        setShowChunkModal(true);
      }
    } catch (error) {
      console.error('Error checking chunks:', error);
      // Show the modal anyway in case of error
      setShowChunkModal(true);
    }
  };

  // Handle confirmation dialog result
  const handleConfirmDialogResult = (confirmed) => {
    setShowConfirmDialog(false);

    if (confirmed) {
      // User confirmed to overwrite, show the modal
      setShowChunkModal(true);
    }
    // If not confirmed, do nothing
  };

  // Generate chunks for content
  const generateChunks = async (overwrite = false) => {
    if (!currentContent || !currentContent.id) return;

    try {
      setIsGeneratingChunks(true);
      setActionError(null);
      setActionSuccess(null);

      console.log(`Generating chunks for content ID: ${currentContent.id} with overwrite=${overwrite}`);

      console.log('Sending request with body:', JSON.stringify({ overwrite }));

      const response = await fetch(`${API_BASE_URL}/api/generate-chunks/${currentContent.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ overwrite })
      });

      const result = await response.json();
      console.log('Chunk generation result:', result);

      if (result.success) {
        setChunkResult(result);
        const overwriteMsg = result.overwritten ? ' (overwritten existing chunks)' : '';
        setActionSuccess(`Successfully generated ${result.chunks.length} chunks for content ID: ${currentContent.id}${overwriteMsg}`);
      } else if (result.chunks_exist) {
        // Chunks exist but overwrite was false
        setActionError(`Found ${result.count} existing chunks. Click 'Overwrite Existing Chunks' to replace them.`);
      } else {
        throw new Error(result.error || result.message || 'Unknown error generating chunks');
      }
    } catch (error) {
      console.error('Error generating chunks:', error);
      setActionError(error.message);
    } finally {
      setIsGeneratingChunks(false);
    }
  };

  // Filter content based on search term
  const filteredContent = generatedContents.filter(content =>
    content.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    String(content.content_prompt_id)?.includes(searchTerm.toLowerCase()) ||
    content.content_text?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredContent.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredContent.length / itemsPerPage);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Format speech content with proper styling
  const formatSpeechContent = (content) => {
    if (!content) return '';

    // Format emphasis (*word*)
    let formattedContent = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Format pause markers
    formattedContent = formattedContent.replace(/\(pause\)/gi, '<span class="pause">(pause)</span>');

    // Format phonetic pronunciations [word]
    formattedContent = formattedContent.replace(/\[(.*?)\]/g, '<span class="phonetic">[$1]</span>');

    // Identify likely transitions
    const transitions = ['first', 'second', 'third', 'next', 'finally', 'moreover', 'furthermore', 'in conclusion', 'to summarize'];
    transitions.forEach(transition => {
      const regex = new RegExp(`\\b${transition}\\b`, 'gi');
      formattedContent = formattedContent.replace(regex, `<span class="transition">${transition}</span>`);
    });

    // Identify likely call-to-action in the last paragraph
    const paragraphs = formattedContent.split('\n\n');
    if (paragraphs.length > 1) {
      const lastParagraph = paragraphs[paragraphs.length - 1];
      if (lastParagraph.toLowerCase().includes('subscribe') ||
          lastParagraph.toLowerCase().includes('follow') ||
          lastParagraph.toLowerCase().includes('like') ||
          lastParagraph.toLowerCase().includes('share') ||
          lastParagraph.toLowerCase().includes('comment') ||
          lastParagraph.toLowerCase().includes('visit')) {
        paragraphs[paragraphs.length - 1] = `<div class="cta">${lastParagraph}</div>`;
      }
      formattedContent = paragraphs.join('\n\n');
    }

    return formattedContent;
  };

  // Load data on component mount
  useEffect(() => {
    console.log('ContentDetails component mounted');
    fetchGeneratedContents();

    // Fetch content prompts and log the result
    fetchContentPrompts().then(() => {
      console.log('Content prompts loaded:', contentPrompts);
      if (contentPrompts.length === 0) {
        console.warn('No content prompts found. Content prompt selection is required.');
      }
    });

    // Debug log to check if component is rendering
    console.log('ContentDetails render state:', {
      loading,
      error,
      contentPrompts: contentPrompts.length,
      generatedContents: generatedContents.length
    });
  }, []);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Add debug log before rendering
  console.log('ContentDetails rendering with state:', {
    loading,
    error,
    contentPromptsCount: contentPrompts.length,
    generatedContentsCount: generatedContents.length,
    currentPage,
    filteredContentCount: filteredContent.length,
    currentItemsCount: currentItems.length
  });

  // Fallback UI in case of critical errors
  if (error && !loading && generatedContents.length === 0) {
    return (
      <Container fluid>
        <Row className="mb-4">
          <Col>
            <h1>Generate Content</h1>
            <p className="text-muted">Generate and manage content for your vlogs</p>
          </Col>
        </Row>
        <Row>
          <Col>
            <Alert variant="danger">
              <Alert.Heading>Error Loading Content</Alert.Heading>
              <p>{error}</p>
              <hr />
              <div className="d-flex justify-content-end">
                <Button onClick={fetchGeneratedContents} variant="outline-danger">
                  Try Again
                </Button>
              </div>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1>Generate Content</h1>
          <p className="text-muted">Generate and manage content for your vlogs</p>
        </Col>
      </Row>

      {error && (
        <Row className="mb-4">
          <Col>
            <Alert variant="danger">
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      {actionSuccess && (
        <Row className="mb-4">
          <Col>
            <Alert variant="success" onClose={() => setActionSuccess(null)} dismissible>
              {actionSuccess}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-4">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text>
              <SearchIcon />
            </InputGroup.Text>
            <FormControl
              placeholder="Search by title, prompt, or category..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={6} className="d-flex justify-content-end">
          <Button
            variant="primary"
            className="me-2"
            onClick={() => {
              setFormData({ ...DEFAULT_GENERATED_CONTENT });
              setFormErrors({});
              setShowAddModal(true);
            }}
          >
            <AddIcon className="me-1" />
            Generate Content
          </Button>
          <Button
            variant="outline-secondary"
            onClick={fetchGeneratedContents}
          >
            <RefreshIcon />
          </Button>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Body>
              {loading ? (
                <div className="text-center my-5">
                  <Spinner animation="border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </Spinner>
                </div>
              ) : (
                <>
                  <div className="table-responsive" style={{ overflowX: 'auto' }}>
                    <Table striped bordered hover responsive>
                      <colgroup>
                        <col style={{ width: '5%' }} /> {/* ID */}
                        <col style={{ width: '10%' }} /> {/* Content Prompt ID */}
                        <col style={{ width: '30%' }} /> {/* Title */}
                        <col style={{ width: '10%' }} /> {/* Speech Duration */}
                        <col style={{ width: '5%' }} /> {/* Images */}
                        <col style={{ width: '15%' }} /> {/* Generated At */}
                        <col style={{ width: '10%' }} /> {/* Actions */}
                      </colgroup>
                      <thead>
                        <tr>
                          <th style={{ width: '5%' }}>ID</th>
                          <th style={{ width: '10%' }}>Prompt ID</th>
                          <th style={{ width: '30%' }}>Title</th>
                          <th style={{ width: '10%' }}>Speech Duration</th>
                          <th style={{ width: '5%' }}>Images</th>
                          <th style={{ width: '15%' }}>Generated At</th>
                          <th style={{ width: '10%' }}>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {currentItems.length > 0 ? (
                          currentItems.map(content => (
                            <tr key={content.id}>
                              <td style={{ width: '5%' }}>{content.id}</td>
                              <td style={{ width: '10%' }}>{content.content_prompt_id || 'N/A'}</td>
                              <td style={{ width: '30%', maxWidth: '300px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }} title={content.title}>
                                {content.title}
                              </td>
                              <td style={{ width: '10%' }}>{content.speech_duration ? `${content.speech_duration}s` : 'N/A'}</td>
                              <td style={{ width: '5%' }}>{content.num_images || 'N/A'}</td>
                              <td style={{ width: '15%' }}>{formatDate(content.generated_at)}</td>
                              <td style={{ width: '10%' }}>
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  className="me-1"
                                  onClick={() => openViewModal(content)}
                                  title="View Content"
                                >
                                  <VisibilityIcon fontSize="small" />
                                </Button>
                                <Button
                                  variant="outline-secondary"
                                  size="sm"
                                  className="me-1"
                                  onClick={() => openEditModal(content)}
                                  title="Edit Content"
                                >
                                  <EditIcon fontSize="small" />
                                </Button>
                                <Button
                                  variant="outline-success"
                                  size="sm"
                                  className="me-1"
                                  onClick={() => openChunkModal(content)}
                                  title="Generate Chunks"
                                >
                                  <ContentCutIcon fontSize="small" />
                                </Button>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => openDeleteModal(content)}
                                  title="Delete Content"
                                >
                                  <DeleteIcon fontSize="small" />
                                </Button>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan="8" className="text-center">
                              {searchTerm ? 'No matching content found' : 'No content available'}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </Table>
                  </div>

                  {filteredContent.length > itemsPerPage && (
                    <div className="d-flex justify-content-center mt-4">
                      <Pagination>
                        <Pagination.First
                          onClick={() => paginate(1)}
                          disabled={currentPage === 1}
                        />
                        <Pagination.Prev
                          onClick={() => paginate(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                        />

                        {Array.from({ length: totalPages }, (_, i) => i + 1)
                          .filter(page => (
                            page === 1 ||
                            page === totalPages ||
                            Math.abs(page - currentPage) <= 1
                          ))
                          .map(page => (
                            <Pagination.Item
                              key={page}
                              active={page === currentPage}
                              onClick={() => paginate(page)}
                            >
                              {page}
                            </Pagination.Item>
                          ))}

                        <Pagination.Next
                          onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                        />
                        <Pagination.Last
                          onClick={() => paginate(totalPages)}
                          disabled={currentPage === totalPages}
                        />
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Add Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Generate & Save Content</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" onClose={() => setActionError(null)} dismissible>
              {actionError}
            </Alert>
          )}

          {actionSuccess && (
            <Alert variant="success" onClose={() => setActionSuccess(null)} dismissible>
              {actionSuccess}
            </Alert>
          )}

          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Content Prompt <span className="text-danger">*</span></Form.Label>
              <Form.Select
                name="content_prompt_id"
                value={formData.content_prompt_id}
                onChange={handleInputChange}
              >
                <option value="">Select a Content Prompt</option>
                {contentPrompts.map(prompt => (
                  <option key={prompt.id} value={prompt.id}>
                    {prompt.title}
                  </option>
                ))}
              </Form.Select>
              <Form.Text className="text-muted">
                Select a content prompt to generate content
              </Form.Text>
            </Form.Group>

            {selectedPromptDetails && (
              <div className="mb-4">
                <Card className="border-primary">
                  <Card.Header className="bg-primary text-white">
                    <h5 className="mb-0">Content Prompt Details</h5>
                  </Card.Header>
                  <Card.Body>
                    <h6 className="mb-3">Topic: <span className="text-primary">{selectedPromptDetails.title || 'N/A'}</span></h6>
                    <h6 className="mb-3">Category: <span className="text-primary">{selectedPromptDetails.category_name || 'N/A'}</span></h6>

                    <div className="mb-3">
                      <strong>Prompt:</strong>
                      <div className="p-2 bg-light rounded mt-2" style={{ whiteSpace: 'pre-wrap' }}>
                        {selectedPromptDetails.content_prompt}
                      </div>
                    </div>

                    {selectedPromptDetails.call_to_action && (
                      <div className="mb-3">
                        <strong>Call to Action:</strong>
                        <div className="p-2 bg-light rounded mt-2" style={{ whiteSpace: 'pre-wrap' }}>
                          {selectedPromptDetails.call_to_action}
                        </div>
                      </div>
                    )}


                  </Card.Body>
                  <Card.Footer className="bg-light">
                    <Button
                      variant="success"
                      onClick={() => generateContent()}
                      disabled={isGeneratingContent}
                      className="w-100"
                    >
                      {isGeneratingContent ? (
                        <>
                          <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                          Generating Content...
                        </>
                      ) : (
                        'Generate Content'
                      )}
                    </Button>
                  </Card.Footer>
                </Card>
              </div>
            )}

            {/* Display generated content if available */}
            {formData.content_text && (
              <div className="mt-4">
                <Card className="border-success">
                  <Card.Header className="bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">Generated Content</h5>
                    {generatedResponses.length > 1 && (
                      <Badge bg="light" text="dark" pill>
                        {generatedResponses.length} Variations
                      </Badge>
                    )}
                  </Card.Header>
                  <Card.Body>
                    <Form.Group className="mb-3">
                      <Form.Label>Title <span className="text-danger">*</span> <span className="text-muted">(Generated by AI, max 250 characters)</span></Form.Label>
                      <Form.Control
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={(e) => {
                          // Limit title to 250 characters
                          if (e.target.value.length <= 250) {
                            handleInputChange(e);
                          }
                        }}
                        maxLength={250}
                        placeholder="AI-generated title"
                      />
                      <Form.Text className="text-muted">
                        This title was automatically generated by AI. You can edit it if needed. {formData.title ? `${formData.title.length}/250 characters` : '0/250 characters'}
                      </Form.Text>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>Content</Form.Label>
                      <Form.Control
                        as="textarea"
                        name="content_text"
                        value={formData.content_text}
                        onChange={handleInputChange}
                        style={{ minHeight: '300px' }}
                        placeholder="Generated content will appear here"
                      />
                    </Form.Group>

                    {/* Display all generated responses if there are multiple */}
                    {generatedResponses.length > 1 && (
                      <div className="mt-4">
                        <h6>All Generated Variations:</h6>
                        <Accordion>
                          {generatedResponses.map((response, index) => (
                            <Accordion.Item key={index} eventKey={index.toString()}>
                              <Accordion.Header>
                                <div className="d-flex justify-content-between w-100 align-items-center">
                                  <span>Variation {index + 1}: {response.title}</span>
                                  {index === 0 && <Badge bg="success" className="ms-2">Selected</Badge>}
                                </div>
                              </Accordion.Header>
                              <Accordion.Body>
                                <div className="mb-3">
                                  <Button
                                    variant="outline-primary"
                                    size="sm"
                                    onClick={() => {
                                      // Use this variation
                                      const updatedFormData = {
                                        ...formData,
                                        title: response.title,
                                        content_text: response.content,
                                        thumbnail_prompt: response.thumbnail_prompt || ''
                                      };

                                      console.log('Updating form data with selected variation:');
                                      console.log('- Title:', updatedFormData.title);
                                      console.log('- Content length:', updatedFormData.content_text.length);

                                      // Update the form data with the selected variation
                                      setFormData(updatedFormData);
                                    }}
                                  >
                                    Use This Variation
                                  </Button>
                                </div>
                                <div className="p-3 bg-light rounded">
                                  <h6>Title:</h6>
                                  <p>{response.title}</p>
                                  <h6>Content:</h6>
                                  <div style={{ whiteSpace: 'pre-wrap' }}>{response.content}</div>
                                </div>
                              </Accordion.Body>
                            </Accordion.Item>
                          ))}
                        </Accordion>
                      </div>
                    )}

                    {formData.thumbnail_prompt && (
                      <Form.Group className="mb-3">
                        <Form.Label>Thumbnail Prompt</Form.Label>
                        <Form.Control
                          as="textarea"
                          name="thumbnail_prompt"
                          value={formData.thumbnail_prompt}
                          onChange={handleInputChange}
                          style={{ minHeight: '100px' }}
                          placeholder="Thumbnail prompt"
                        />
                      </Form.Group>
                    )}
                  </Card.Body>
                </Card>
              </div>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddContent}
            disabled={isGeneratingContent || !formData.content_prompt_id || !formData.content_text}
          >
            {isGeneratingContent ? (
              <>
                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                Saving...
              </>
            ) : (
              'Save Generated Content'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Content</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" onClose={() => setActionError(null)} dismissible>
              {actionError}
            </Alert>
          )}

          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Content Prompt <span className="text-danger">*</span></Form.Label>
              <Form.Select
                name="content_prompt_id"
                value={formData.content_prompt_id || ''}
                onChange={handleInputChange}
                isInvalid={!!formErrors.content_prompt_id}
              >
                <option value="">Select a Content Prompt</option>
                {contentPrompts.map(prompt => (
                  <option key={prompt.id} value={prompt.id}>
                    {prompt.title}
                  </option>
                ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                {formErrors.content_prompt_id}
              </Form.Control.Feedback>
              <Form.Text className="text-muted">
                Select a content prompt to generate content. The content will be linked to this prompt.
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Number of Responses to Generate</Form.Label>
              <Form.Select
                name="num_responses"
                value={formData.num_responses || 1}
                onChange={handleInputChange}
              >
                <option value="1">1 (Single Response)</option>
                <option value="2">2 Responses</option>
                <option value="3">3 Responses</option>
                <option value="4">4 Responses</option>
                <option value="5">5 Responses</option>
              </Form.Select>
              <Form.Text className="text-muted">
                Generate multiple variations of content for the same prompt. The first response will be used by default.
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Title <span className="text-danger">*</span> <span className="text-muted">(Generated by AI)</span></Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                isInvalid={!!formErrors.title}
                placeholder="This will be filled automatically when you generate content"
              />
              <Form.Control.Feedback type="invalid">
                {formErrors.title}
              </Form.Control.Feedback>
              <Form.Text className="text-muted">
                The title is automatically generated by AI when you click the "Generate Content" button.
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Speech-Ready Content <span className="text-danger">*</span></Form.Label>
              <Form.Control
                as="textarea"
                rows={10}
                name="content_text"
                value={formData.content_text}
                onChange={handleInputChange}
                isInvalid={!!formErrors.content_text}
                className="speech-content-editor"
                style={{ lineHeight: '1.6', fontSize: '1.1rem' }}
              />
              <Form.Control.Feedback type="invalid">
                {formErrors.content_text}
              </Form.Control.Feedback>
              <div className="d-flex justify-content-between align-items-start mt-2 mb-2">
                <Form.Text className="text-muted">
                  <strong>Speech Formatting Tips:</strong><br />
                  • Use *asterisks* around words for emphasis<br />
                  • Add (pause) markers where natural pauses should occur<br />
                  • Include [phonetic-spelling] for uncommon words<br />
                  • Use short paragraphs (2-3 sentences each)<br />
                  • End with a clear call-to-action
                </Form.Text>
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => {
                    setShowPreviewModal(true);
                  }}
                >
                  Preview Speech Format
                </Button>
              </div>
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Speech Duration (seconds)</Form.Label>
                  <Form.Control
                    type="number"
                    name="speech_duration"
                    value={formData.speech_duration}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.speech_duration}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formErrors.speech_duration}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Number of Images</Form.Label>
                  <Form.Control
                    type="number"
                    name="num_images"
                    value={formData.num_images}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.num_images}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formErrors.num_images}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Speech Location</Form.Label>
              <Form.Control
                type="text"
                name="speech_location"
                value={formData.speech_location}
                onChange={handleInputChange}
                placeholder="Path or URL to speech file"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleEditContent}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>View Content</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentContent && (
            <div>
              <h4>{currentContent.title}</h4>

              <div className="mb-3">
                <Badge bg="primary" className="me-2">ID: {currentContent.id}</Badge>
                <Badge bg="info" className="me-2">Prompt ID: {currentContent.content_prompt_id || 'N/A'}</Badge>
              </div>

              <h5>Speech-Ready Content</h5>
              <div
                className="p-3 bg-light rounded mb-3 speech-content"
                style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6', fontSize: '1.1rem' }}
                dangerouslySetInnerHTML={{ __html: formatSpeechContent(currentContent.content_text) }}
              />

              <Row className="mb-3">
                <Col md={4}>
                  <strong>Speech Duration:</strong> {currentContent.speech_duration ? `${currentContent.speech_duration}s` : 'N/A'}
                </Col>
                <Col md={4}>
                  <strong>Number of Images:</strong> {currentContent.num_images || 'N/A'}
                </Col>
                <Col md={4}>
                  <strong>Generated At:</strong> {formatDate(currentContent.generated_at)}
                </Col>
              </Row>

              {currentContent.speech_location && (
                <div className="mb-3">
                  <strong>Speech Location:</strong> {currentContent.speech_location}
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              setShowViewModal(false);
              openEditModal(currentContent);
            }}
          >
            Edit
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Delete Content</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" onClose={() => setActionError(null)} dismissible>
              {actionError}
            </Alert>
          )}

          {currentContent && (
            <p>
              Are you sure you want to delete the content <strong>"{currentContent.title}"</strong>?
              This action cannot be undone.
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteContent}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Preview Modal */}
      <Modal show={showPreviewModal} onHide={() => setShowPreviewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Speech Format Preview</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="speech-content p-4 bg-light rounded">
            <h4>{formData.title}</h4>
            <div
              className="mt-3"
              dangerouslySetInnerHTML={{ __html: formatSpeechContent(formData.content_text) }}
            />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPreviewModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Delete Content</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" className="mb-3">
              {actionError}
            </Alert>
          )}
          <p>Are you sure you want to delete this content?</p>
          <p><strong>Title:</strong> {currentContent?.title}</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeleteContent}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Confirmation Dialog for Existing Chunks */}
      <Modal show={showConfirmDialog} onHide={() => handleConfirmDialogResult(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Existing Chunks Found</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>This content already has {existingChunksCount} existing chunks.</p>
          <p>Do you want to proceed and potentially overwrite these chunks?</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => handleConfirmDialogResult(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={() => handleConfirmDialogResult(true)}>
            Proceed
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Generate Chunks Modal */}
      <Modal show={showChunkModal} onHide={() => setShowChunkModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Generate Content Chunks</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" className="mb-3">
              {actionError}
            </Alert>
          )}
          {actionSuccess && (
            <Alert variant="success" className="mb-3">
              {actionSuccess}
            </Alert>
          )}

          {currentContent && (
            <div>
              <h5>Content Details</h5>
              <p><strong>Title:</strong> {currentContent.title}</p>
              <p><strong>Content Length:</strong> {currentContent.content_text ? currentContent.content_text.split(' ').length : 0} words</p>

              <p className="mt-3 mb-1">This will generate chunks of content based on a rate of 146 words per minute. Each chunk will have its own image generated using Grok AI.</p>

              {!chunkResult ? (
                <div className="d-grid gap-2 mt-3">
                  <Button
                    variant="primary"
                    onClick={() => generateChunks(false)}
                    disabled={isGeneratingChunks}
                    className="mb-2"
                  >
                    {isGeneratingChunks ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Generating Chunks...
                      </>
                    ) : (
                      'Generate Chunks'
                    )}
                  </Button>

                  {actionError && actionError.includes('existing chunks') && (
                    <Button
                      variant="warning"
                      onClick={() => generateChunks(true)}
                      disabled={isGeneratingChunks}
                    >
                      {isGeneratingChunks ? (
                        <>
                          <Spinner
                            as="span"
                            animation="border"
                            size="sm"
                            role="status"
                            aria-hidden="true"
                            className="me-2"
                          />
                          Overwriting Chunks...
                        </>
                      ) : (
                        'Overwrite Existing Chunks'
                      )}
                    </Button>
                  )}
                </div>
              ) : (
                <div className="mt-3">
                  <h5>Generated Chunks</h5>
                  <p>Successfully generated {chunkResult.chunks.length} chunks:</p>

                  <div className="table-responsive mt-3">
                    <Table bordered hover size="sm">
                      <thead>
                        <tr>
                          <th>Chunk ID</th>
                          <th>Order</th>
                          <th>Image</th>
                        </tr>
                      </thead>
                      <tbody>
                        {chunkResult.chunks.map(chunk => (
                          <tr key={chunk.chunk_id}>
                            <td>{chunk.chunk_id}</td>
                            <td>{chunk.chunk_order}</td>
                            <td>
                              {chunk.image_url ? (
                                <img
                                  src={chunk.image_url}
                                  alt={`Chunk ${chunk.chunk_order}`}
                                  style={{ width: '100px', height: 'auto' }}
                                />
                              ) : 'No image generated'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowChunkModal(false)}>
            Close
          </Button>
          {chunkResult && (
            <Button
              variant="primary"
              onClick={() => window.location.href = '/content-chunks'}
            >
              View All Chunks
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ContentDetails;
