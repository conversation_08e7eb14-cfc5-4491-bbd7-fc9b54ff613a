from typing import List, Literal, Optional

import httpx
import ormsgpack
from pydantic import BaseModel, conint
from dotenv import load_dotenv
import os
import asyncio

load_dotenv()
FISHAUDIO_API_KEY = os.getenv("FISHAUDIO_API_KEY")
FISHAUDIO_OUTPUT= os.getenv("FISHAUDIO_OUTPUT")

class ReferenceAudio(BaseModel):
    audio: bytes
    text: str

class ProsodySettings(BaseModel):
    speed: float  # Speed range: 0.5 (slow) to 2.0 (fast), default is 1.0

class TTSRequest(BaseModel):
    text: str
    chunk_length: conint(ge=100, le=300) = 200
    format: Literal["wav", "pcm", "mp3"] = "mp3"
    mp3_bitrate: Literal[64, 128, 192] = 128
    references: List[ReferenceAudio] = []
    reference_id: Optional[str] = None  # Model selection
    normalize: bool = True
    latency: Literal["normal", "balanced"] = "normal"
    prosody: ProsodySettings  # Controls speed

async def text_to_speech(
    api_key: str,
    text: str,
    output_file: str,
    reference_audio_path: str = None,
    reference_text: str = None,
    speed: float = 1.0,  # Default speed is 1.0
    reference_id: str = None  # Allows selecting a model
):
    # Concatenate folder and filename
    folder=FISHAUDIO_OUTPUT
    loc_output = os.path.join(folder, output_file)
    headers = {
        "authorization": f"Bearer {api_key}",
        "content-type": "application/msgpack",
        "developer-id": "1c9a7c8cf51f458ab298c48e81b29bfc",
    }

    references = []
    if reference_audio_path and reference_text:
        with open(reference_audio_path, "rb") as audio_file:
            audio_data = audio_file.read()
            references.append(ReferenceAudio(audio=audio_data, text=reference_text))

    tts_request = TTSRequest(
        text=text,
        references=references,
        reference_id=reference_id,  # Use selected model
        prosody=ProsodySettings(speed=speed)  # Set speech speed
    )

    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "https://api.fish.audio/v1/tts",
            content=ormsgpack.packb(tts_request.model_dump(), option=ormsgpack.OPT_SERIALIZE_PYDANTIC),
            headers=headers,
            timeout=None,
        ) as response:
            response.raise_for_status()
            
            with open(loc_output, "wb") as f:
                async for chunk in response.aiter_bytes():
                    f.write(chunk)

async def main():
    api_key = FISHAUDIO_API_KEY
    text = "It must be exhausting to constantly compare your relationship to others, feeling like you're never quite measuring up. I understand how this can make you doubt your own happiness. You're not alone in this struggle; many feel the pressure of these comparisons..."
    output_file="outmusic.mp3"
    reference_audio_path = ""  # Optional
    reference_text = ""  # Optional
    speed = .9  # Adjust speed (0.5 = slower, 1.0 = normal, 2.0 = faster)
    reference_id = "d8c7af3779494549a975bdb8015e8a8b"  # Set your model ID
    #JM - 0f5dbddf903d4bd4be22d3f40648851b

    await text_to_speech(api_key, text, output_file,reference_audio_path, reference_text, speed, reference_id)

if __name__ == "__main__":
    asyncio.run(main())
