import React, { useState, useEffect } from 'react';
import {
  Card, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ner, <PERSON><PERSON>,
  Badge, InputGroup, FormControl, Modal, Tabs, Tab,
  Pagination
} from 'react-bootstrap';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

const CategoryManagement = ({ setError }) => {
  // Tab state
  const [activeTab, setActiveTab] = useState('categories');

  // Categories state
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categorySearchTerm, setCategorySearchTerm] = useState('');
  const [showCategoryConfirmModal, setShowCategoryConfirmModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categoryActionInProgress, setCategoryActionInProgress] = useState(false);
  const [categorySuccessMessage, setCategorySuccessMessage] = useState(null);
  const [categoryPage, setCategoryPage] = useState(1);
  const [categoryTotalPages, setCategoryTotalPages] = useState(1);

  // Titles state
  const [titles, setTitles] = useState([]);
  const [titlesLoading, setTitlesLoading] = useState(true);
  const [titleSearchTerm, setTitleSearchTerm] = useState('');
  const [showTitleConfirmModal, setShowTitleConfirmModal] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState(null);
  const [titleActionInProgress, setTitleActionInProgress] = useState(false);
  const [titleSuccessMessage, setTitleSuccessMessage] = useState(null);
  const [titlePage, setTitlePage] = useState(1);
  const [titleTotalPages, setTitleTotalPages] = useState(1);
  const [showAddTitleModal, setShowAddTitleModal] = useState(false);
  const [newTitleData, setNewTitleData] = useState({ category_id: '', title: '' });
  const [titleFormErrors, setTitleFormErrors] = useState({});

  // Fetch all categories including inactive ones with pagination
  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);
      // In a real implementation, you would pass page and limit parameters to the API
      // For now, we'll simulate pagination on the client side
      const response = await fetch(`${API_BASE_URL}/api/categories?status=all`);

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();

      // Simulate pagination (10 items per page)
      const itemsPerPage = 10;
      const totalPages = Math.ceil(data.length / itemsPerPage);
      setCategoryTotalPages(totalPages);

      // Get current page data
      const startIndex = (categoryPage - 1) * itemsPerPage;
      const paginatedData = data.slice(startIndex, startIndex + itemsPerPage);

      setCategories(data); // Store all categories for search functionality
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError('Failed to load categories. Please try again later.');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Fetch titles with pagination
  const fetchTitles = async () => {
    try {
      setTitlesLoading(true);
      // In a real implementation, you would have an endpoint to fetch all titles
      // For now, we'll simulate by fetching titles for each category

      const categoriesResponse = await fetch(`${API_BASE_URL}/api/categories?status=all`);

      if (!categoriesResponse.ok) {
        throw new Error('Failed to fetch categories');
      }

      const categoriesData = await categoriesResponse.json();

      // Fetch titles for each category
      let allTitles = [];
      for (const category of categoriesData) {
        const titlesResponse = await fetch(`${API_BASE_URL}/api/category-titles/${category.id}`);

        if (titlesResponse.ok) {
          const titlesData = await titlesResponse.json();
          // Add category name to each title
          const titlesWithCategory = titlesData.map(title => ({
            ...title,
            category_name: category.name || 'Unknown Category'
          }));
          allTitles = [...allTitles, ...titlesWithCategory];
        }
      }

      // Simulate pagination (10 items per page)
      const itemsPerPage = 10;
      const totalPages = Math.ceil(allTitles.length / itemsPerPage);
      setTitleTotalPages(totalPages);

      // Get current page data
      const startIndex = (titlePage - 1) * itemsPerPage;
      const paginatedData = allTitles.slice(startIndex, startIndex + itemsPerPage);

      setTitles(allTitles); // Store all titles for search functionality
    } catch (error) {
      console.error('Error fetching titles:', error);
      setError('Failed to load titles. Please try again later.');
    } finally {
      setTitlesLoading(false);
    }
  };

  // Toggle category status
  const toggleCategoryStatus = async (categoryId, newStatus) => {
    try {
      setCategoryActionInProgress(true);

      const response = await fetch(`${API_BASE_URL}/api/categories/${categoryId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update category status');
      }

      // Update local state
      setCategories(prevCategories =>
        prevCategories.map(category =>
          category.id === categoryId
            ? { ...category, status: newStatus }
            : category
        )
      );

      setCategorySuccessMessage(`Category status updated to ${newStatus}`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setCategorySuccessMessage(null);
      }, 3000);

    } catch (error) {
      console.error('Error updating category status:', error);
      setError(error.message);
    } finally {
      setCategoryActionInProgress(false);
      setShowCategoryConfirmModal(false);
    }
  };

  // Open category confirmation modal
  const openCategoryConfirmModal = (category) => {
    setSelectedCategory(category);
    setShowCategoryConfirmModal(true);
  };

  // Add new title
  const addNewTitle = async () => {
    try {
      // Validate form
      const errors = {};
      if (!newTitleData.category_id) errors.category_id = 'Category is required';
      if (!newTitleData.title) errors.title = 'Title is required';

      if (Object.keys(errors).length > 0) {
        setTitleFormErrors(errors);
        return;
      }

      setTitleActionInProgress(true);

      const response = await fetch(`${API_BASE_URL}/api/category-titles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTitleData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add new title');
      }

      // Refresh titles
      await fetchTitles();

      setTitleSuccessMessage('New title added successfully');

      // Clear form and close modal
      setNewTitleData({ category_id: '', title: '' });
      setShowAddTitleModal(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setTitleSuccessMessage(null);
      }, 3000);

    } catch (error) {
      console.error('Error adding new title:', error);
      setError(error.message);
    } finally {
      setTitleActionInProgress(false);
    }
  };

  // Delete title
  const deleteTitle = async (titleId) => {
    try {
      setTitleActionInProgress(true);

      // In a real implementation, you would have an endpoint to delete a title
      // For now, we'll just simulate success

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      setTitles(prevTitles => prevTitles.filter(title => title.id !== titleId));

      setTitleSuccessMessage('Title deleted successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setTitleSuccessMessage(null);
      }, 3000);

    } catch (error) {
      console.error('Error deleting title:', error);
      setError(error.message);
    } finally {
      setTitleActionInProgress(false);
      setShowTitleConfirmModal(false);
    }
  };

  // Open title confirmation modal
  const openTitleConfirmModal = (title) => {
    setSelectedTitle(title);
    setShowTitleConfirmModal(true);
  };

  // Handle title form input changes
  const handleTitleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTitleData({
      ...newTitleData,
      [name]: value
    });

    // Clear error for this field
    if (titleFormErrors[name]) {
      setTitleFormErrors({
        ...titleFormErrors,
        [name]: null
      });
    }
  };

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(categorySearchTerm.toLowerCase())
  );

  // Filter titles based on search term
  const filteredTitles = titles.filter(title =>
    title.title?.toLowerCase().includes(titleSearchTerm.toLowerCase()) ||
    (title.category_name && title.category_name.toLowerCase().includes(titleSearchTerm.toLowerCase()))
  );

  // Get paginated data
  const getPaginatedCategories = () => {
    const itemsPerPage = 10;
    const startIndex = (categoryPage - 1) * itemsPerPage;
    return filteredCategories.slice(startIndex, startIndex + itemsPerPage);
  };

  const getPaginatedTitles = () => {
    const itemsPerPage = 10;
    const startIndex = (titlePage - 1) * itemsPerPage;
    return filteredTitles.slice(startIndex, startIndex + itemsPerPage);
  };

  // Load data on component mount and when tabs change
  useEffect(() => {
    if (activeTab === 'categories') {
      fetchCategories();
    } else if (activeTab === 'titles') {
      fetchTitles();
    }
  }, [activeTab]);

  // Update pagination when page changes or search terms change
  useEffect(() => {
    if (activeTab === 'categories') {
      // Reset to first page when search term changes
      setCategoryPage(1);
      // Update total pages based on filtered data
      const totalPages = Math.max(1, Math.ceil(filteredCategories.length / 10));
      setCategoryTotalPages(totalPages);
    }
  }, [activeTab, categorySearchTerm, filteredCategories.length]);

  useEffect(() => {
    if (activeTab === 'titles') {
      // Reset to first page when search term changes
      setTitlePage(1);
      // Update total pages based on filtered data
      const totalPages = Math.max(1, Math.ceil(filteredTitles.length / 10));
      setTitleTotalPages(totalPages);
    }
  }, [activeTab, titleSearchTerm, filteredTitles.length]);

  return (
    <Card>
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h5 className="mb-0">Category Management</h5>
      </Card.Header>
      <Card.Body>
        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-4"
        >
          <Tab eventKey="categories" title="Categories">
            {categorySuccessMessage && (
              <Alert variant="success" onClose={() => setCategorySuccessMessage(null)} dismissible>
                {categorySuccessMessage}
              </Alert>
            )}

            <div className="d-flex justify-content-between mb-3">
              <InputGroup style={{ maxWidth: '400px' }}>
                <InputGroup.Text>
                  <SearchIcon />
                </InputGroup.Text>
                <FormControl
                  placeholder="Search categories..."
                  value={categorySearchTerm}
                  onChange={(e) => setCategorySearchTerm(e.target.value)}
                />
              </InputGroup>

              <Button
                variant="outline-secondary"
                size="sm"
                onClick={fetchCategories}
                disabled={categoriesLoading}
              >
                <RefreshIcon fontSize="small" />
              </Button>
            </div>

            {categoriesLoading ? (
              <div className="text-center my-4">
                <Spinner animation="border" role="status">
                  <span className="visually-hidden">Loading...</span>
                </Spinner>
              </div>
            ) : (
              <>
                <div className="table-responsive">
                  <Table striped bordered hover>
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getPaginatedCategories().length > 0 ? (
                        getPaginatedCategories().map(category => (
                          <tr key={category.id}>
                            <td>{category.id}</td>
                            <td>{category.name}</td>
                            <td>{category.description || 'No description'}</td>
                            <td>
                              <Badge bg={category.status === 'active' ? 'success' : 'secondary'}>
                                {category.status}
                              </Badge>
                            </td>
                            <td>
                              <Button
                                variant={category.status === 'active' ? 'outline-danger' : 'outline-success'}
                                size="sm"
                                onClick={() => openCategoryConfirmModal(category)}
                                disabled={categoryActionInProgress}
                              >
                                {category.status === 'active' ? (
                                  <>
                                    <ToggleOffIcon fontSize="small" className="me-1" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <ToggleOnIcon fontSize="small" className="me-1" />
                                    Activate
                                  </>
                                )}
                              </Button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="text-center">
                            {categorySearchTerm ? 'No categories match your search' : 'No categories found'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>

                {/* Pagination */}
                {filteredCategories.length > 0 && (
                  <div className="d-flex justify-content-center mt-4">
                    <Pagination>
                      <Pagination.First
                        onClick={() => setCategoryPage(1)}
                        disabled={categoryPage === 1}
                      />
                      <Pagination.Prev
                        onClick={() => setCategoryPage(prev => Math.max(prev - 1, 1))}
                        disabled={categoryPage === 1}
                      />

                      {Array.from({ length: categoryTotalPages }, (_, i) => i + 1)
                        .filter(page => (
                          page === 1 ||
                          page === categoryTotalPages ||
                          Math.abs(page - categoryPage) <= 1
                        ))
                        .map(page => (
                          <Pagination.Item
                            key={page}
                            active={page === categoryPage}
                            onClick={() => setCategoryPage(page)}
                          >
                            {page}
                          </Pagination.Item>
                        ))}

                      <Pagination.Next
                        onClick={() => setCategoryPage(prev => Math.min(prev + 1, categoryTotalPages))}
                        disabled={categoryPage === categoryTotalPages}
                      />
                      <Pagination.Last
                        onClick={() => setCategoryPage(categoryTotalPages)}
                        disabled={categoryPage === categoryTotalPages}
                      />
                    </Pagination>
                  </div>
                )}
              </>
            )}
          </Tab>

          <Tab eventKey="titles" title="Category Titles">
            {titleSuccessMessage && (
              <Alert variant="success" onClose={() => setTitleSuccessMessage(null)} dismissible>
                {titleSuccessMessage}
              </Alert>
            )}

            <div className="d-flex justify-content-between mb-3">
              <InputGroup style={{ maxWidth: '400px' }}>
                <InputGroup.Text>
                  <SearchIcon />
                </InputGroup.Text>
                <FormControl
                  placeholder="Search titles..."
                  value={titleSearchTerm}
                  onChange={(e) => setTitleSearchTerm(e.target.value)}
                />
              </InputGroup>

              <div>
                <Button
                  variant="primary"
                  size="sm"
                  className="me-2"
                  onClick={() => setShowAddTitleModal(true)}
                >
                  <AddIcon fontSize="small" className="me-1" />
                  Add Title
                </Button>

                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={fetchTitles}
                  disabled={titlesLoading}
                >
                  <RefreshIcon fontSize="small" />
                </Button>
              </div>
            </div>

            {titlesLoading ? (
              <div className="text-center my-4">
                <Spinner animation="border" role="status">
                  <span className="visually-hidden">Loading...</span>
                </Spinner>
              </div>
            ) : (
              <>
                <div className="table-responsive">
                  <Table striped bordered hover>
                    <thead>
                      <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Category</th>
                        <th>Used</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getPaginatedTitles().length > 0 ? (
                        getPaginatedTitles().map(title => (
                          <tr key={title.id}>
                            <td>{title.id}</td>
                            <td>{title.title}</td>
                            <td>{title.category_name}</td>
                            <td>
                              <Badge bg={title.is_used ? 'success' : 'secondary'}>
                                {title.is_used ? 'Yes' : 'No'}
                              </Badge>
                            </td>
                            <td>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => openTitleConfirmModal(title)}
                                disabled={titleActionInProgress}
                              >
                                <DeleteIcon fontSize="small" className="me-1" />
                                Delete
                              </Button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="text-center">
                            {titleSearchTerm ? 'No titles match your search' : 'No titles found'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>

                {/* Pagination */}
                {filteredTitles.length > 0 && (
                  <div className="d-flex justify-content-center mt-4">
                    <Pagination>
                      <Pagination.First
                        onClick={() => setTitlePage(1)}
                        disabled={titlePage === 1}
                      />
                      <Pagination.Prev
                        onClick={() => setTitlePage(prev => Math.max(prev - 1, 1))}
                        disabled={titlePage === 1}
                      />

                      {Array.from({ length: titleTotalPages }, (_, i) => i + 1)
                        .filter(page => (
                          page === 1 ||
                          page === titleTotalPages ||
                          Math.abs(page - titlePage) <= 1
                        ))
                        .map(page => (
                          <Pagination.Item
                            key={page}
                            active={page === titlePage}
                            onClick={() => setTitlePage(page)}
                          >
                            {page}
                          </Pagination.Item>
                        ))}

                      <Pagination.Next
                        onClick={() => setTitlePage(prev => Math.min(prev + 1, titleTotalPages))}
                        disabled={titlePage === titleTotalPages}
                      />
                      <Pagination.Last
                        onClick={() => setTitlePage(titleTotalPages)}
                        disabled={titlePage === titleTotalPages}
                      />
                    </Pagination>
                  </div>
                )}
              </>
            )}
          </Tab>
        </Tabs>
      </Card.Body>

      {/* Category Confirmation Modal */}
      <Modal show={showCategoryConfirmModal} onHide={() => setShowCategoryConfirmModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Status Change</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedCategory && (
            <p>
              Are you sure you want to {selectedCategory.status === 'active' ? 'deactivate' : 'activate'} the category <strong>{selectedCategory.name}</strong>?
              {selectedCategory.status === 'active' && (
                <Alert variant="warning" className="mt-2">
                  <strong>Warning:</strong> Deactivating this category will hide it from dropdown menus in the application.
                </Alert>
              )}
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCategoryConfirmModal(false)}>
            Cancel
          </Button>
          {selectedCategory && (
            <Button
              variant={selectedCategory.status === 'active' ? 'danger' : 'success'}
              onClick={() => toggleCategoryStatus(
                selectedCategory.id,
                selectedCategory.status === 'active' ? 'inactive' : 'active'
              )}
              disabled={categoryActionInProgress}
            >
              {categoryActionInProgress ? (
                <>
                  <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                  Processing...
                </>
              ) : (
                selectedCategory.status === 'active' ? 'Deactivate' : 'Activate'
              )}
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Title Confirmation Modal */}
      <Modal show={showTitleConfirmModal} onHide={() => setShowTitleConfirmModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTitle && (
            <p>
              Are you sure you want to delete the title <strong>{selectedTitle.title}</strong>?
              <Alert variant="warning" className="mt-2">
                <strong>Warning:</strong> This action cannot be undone.
              </Alert>
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowTitleConfirmModal(false)}>
            Cancel
          </Button>
          {selectedTitle && (
            <Button
              variant="danger"
              onClick={() => deleteTitle(selectedTitle.id)}
              disabled={titleActionInProgress}
            >
              {titleActionInProgress ? (
                <>
                  <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                  Processing...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Add Title Modal */}
      <Modal show={showAddTitleModal} onHide={() => setShowAddTitleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add New Title</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Category</Form.Label>
              <Form.Select
                name="category_id"
                value={newTitleData.category_id}
                onChange={handleTitleInputChange}
                isInvalid={!!titleFormErrors.category_id}
              >
                <option value="">Select Category</option>
                {categories
                  .filter(category => category.status === 'active')
                  .map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                {titleFormErrors.category_id}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={newTitleData.title}
                onChange={handleTitleInputChange}
                isInvalid={!!titleFormErrors.title}
                placeholder="Enter a title"
              />
              <Form.Control.Feedback type="invalid">
                {titleFormErrors.title}
              </Form.Control.Feedback>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddTitleModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={addNewTitle}
            disabled={titleActionInProgress}
          >
            {titleActionInProgress ? (
              <>
                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                Processing...
              </>
            ) : (
              'Add Title'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default CategoryManagement;
