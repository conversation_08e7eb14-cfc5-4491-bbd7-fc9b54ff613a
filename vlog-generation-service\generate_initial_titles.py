import os
import sys
import logging
import requests
import json
import pymysql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

# API setup for Grok
XAI_API_KEY = os.getenv('XAI_API_KEY')
GROK_API_URL = "https://api.x.ai/v1/chat/completions"

def generate_titles_for_category(category_id, category_name):
    """Generate titles for a specific category using Grok API"""
    conn = None
    cursor = None
    try:
        logger.info(f"Generating titles for category: {category_name} (ID: {category_id})")
        
        # Generate titles using Grok
        if not XAI_API_KEY:
            logger.error("Grok API key not configured")
            return False
            
        # Create prompt for Grok
        prompt = f"""
        Generate 10 engaging and specific title ideas for vlogs about {category_name}.
        Return only a JSON array of strings, with each string being a title.
        The titles should be specific, not generic, and should appeal to viewers.
        For example, instead of "Health Tips", use "5 Morning Habits That Boost Your Metabolism".
        
        Return the response in this exact JSON format:
        {{"titles": ["Title 1", "Title 2", "Title 3", ... ]}}        
        """
        
        # Call Grok API
        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "grok-2-vision-latest",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=30)
            
            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Grok API error: {error_text}")
                return False
                
            # Process the response
            data = response.json()
            generated_text = data["choices"][0]["message"]["content"]
            
            # Clean the response text
            generated_text = generated_text.strip()
            if generated_text.startswith('```json'):
                generated_text = generated_text[7:]  # Remove ```json prefix
            elif generated_text.startswith('```'):
                generated_text = generated_text[3:]  # Remove ``` prefix
                
            if generated_text.endswith('```'):
                generated_text = generated_text[:-3]  # Remove ``` suffix
                
            # Parse the JSON response
            try:
                response_json = json.loads(generated_text)
                titles = response_json.get("titles", [])
                
                if not titles:
                    logger.error("No titles generated")
                    return False
                    
                # Save titles to database
                conn = pymysql.connect(**DB_CONFIG)
                cursor = conn.cursor()
                
                saved_count = 0
                for title in titles:
                    # Check if title already exists
                    cursor.execute(
                        "SELECT id FROM category_title WHERE category_id = %s AND title = %s",
                        (category_id, title)
                    )
                    if not cursor.fetchone():
                        cursor.execute(
                            "INSERT INTO category_title (category_id, title) VALUES (%s, %s)",
                            (category_id, title)
                        )
                        saved_count += 1
                
                conn.commit()
                logger.info(f"Successfully generated and saved {saved_count} titles for category: {category_name}")
                
                return True
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON. Raw text: {generated_text[:100]}...")
                logger.error(f"JSON parse error: {str(e)}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"Error generating titles: {str(e)}")
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Generate titles for all categories"""
    conn = None
    cursor = None
    try:
        logger.info("Starting title generation for all categories")
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Get all categories
        cursor.execute("SELECT id, name FROM category")
        categories = cursor.fetchall()
        
        if not categories:
            logger.error("No categories found")
            return
            
        logger.info(f"Found {len(categories)} categories")
        
        # Generate titles for each category
        for category in categories:
            generate_titles_for_category(category['id'], category['name'])
            
        logger.info("Title generation complete")
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main()
