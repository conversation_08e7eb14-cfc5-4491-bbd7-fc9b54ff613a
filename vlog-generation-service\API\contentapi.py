import os
import csv
import pymysql
from flask import Flask, jsonify, request, send_from_directory, current_app
from werkzeug.utils import secure_filename
import sys
import logging
import asyncio
import aiomysql
from flask_cors import CORS
from dotenv import load_dotenv
import re
import subprocess
import datetime
from runware import IImageInference

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,  # This will now use 'vlog_generator' from .env
    'cursorclass': pymysql.cursors.DictCursor
}

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from _3content_generate_images import generate_thumbnail, DB_CONFIG, RUNWARE_API_KEY, Runware

app = Flask(__name__)
# Enable CORS for all routes
CORS(app, resources={
    r"/api/*": {"origins": "*"},
    r"/content-images/*": {"origins": "*"},
    r"/content-thumbnail/*": {"origins": "*"}
})

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_scenario(scenario):
    """Remove numbering from the beginning of the scenario."""
    return re.sub(r'^\d+\.\s*', '', scenario.strip())

def scenario_exists(cursor, scenario):
    """Check if a scenario already exists in the database."""
    cursor.execute(
        "SELECT COUNT(*) as count FROM generated_content WHERE scenario = %s",
        (scenario,)
    )
    result = cursor.fetchone()
    return result['count'] > 0

@app.before_request
def log_request_info():
    current_app.logger.info('Headers: %s', request.headers)
    current_app.logger.info('Body: %s', request.get_data())
    current_app.logger.info('Base Directory: %s', CONTENT_DIR)

@app.route('/')
def index():
    return jsonify({
        'status': 'running',
        'endpoints': {
            'get_content': '/api/content',
            'regenerate_thumbnail': '/api/regenerate/thumbnail/<content_id>',
            'upload_content': '/api/upload/content'
        }
    })

async def fetch_content():
    try:
        pool = await aiomysql.create_pool(**DB_CONFIG)

        async with pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # First get the main content
                await cursor.execute('''
                    SELECT
                        id,
                        scenario,
                        empathetic_advice,
                        practical_advice,
                        thumbnail_url,
                        thumbnail_prompt,
                        created_at
                    FROM generated_content
                    ORDER BY id DESC
                ''')

                results = await cursor.fetchall()

                content_list = []
                for row in results:
                    # For each content, fetch its associated images
                    await cursor.execute('''
                        SELECT
                            id,
                            image_prompt,
                            url
                        FROM image_prompts
                        WHERE content_id = %s
                        AND url IS NOT NULL
                    ''', (row['id'],))

                    images = await cursor.fetchall()

                    # Convert row data to snake_case to camelCase
                    content_item = {
                        'id': row['id'],
                        'scenario': row['scenario'],
                        'empatheticAdvice': row['empathetic_advice'],
                        'practicalAdvice': row['practical_advice'],
                        'thumbnail_url': row['thumbnail_url'],  # Keep as snake_case to match existing code
                        'thumbnailPrompt': row['thumbnail_prompt'],
                        'createdAt': row['created_at'].isoformat() if row['created_at'] else None,
                        'images': [
                            {
                                'id': img['id'],
                                'imagePrompt': img['image_prompt'],
                                'url': img['url']
                            } for img in images
                        ]
                    }
                    content_list.append(content_item)

                return content_list

    except Exception as e:
        logger.error(f"Error fetching content: {e}")
        raise e
    finally:
        pool.close()
        await pool.wait_closed()

@app.route('/api/content', methods=['GET'])
def get_content():
    conn = None
    cursor = None
    try:
        logger.info("Attempting to connect to database with config: %s", {k: v for k, v in DB_CONFIG.items() if k != 'password'})
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        logger.info("Executing query to fetch content")
        cursor.execute("""
            SELECT
                id,
                scenario,
                empathetic_advice,
                practical_advice,
                thumbnail_prompt,
                thumbnail_url
            FROM generated_content
            ORDER BY id DESC
        """)

        content = cursor.fetchall()
        logger.info(f"Successfully fetched {len(content)} content items")

        # Convert datetime objects to strings to ensure JSON serialization works
        for item in content:
            for key, value in item.items():
                if isinstance(value, (datetime.date, datetime.datetime)):
                    item[key] = value.isoformat()

        return jsonify(content)
    except pymysql.MySQLError as e:
        error_msg = f"Database error in get_content: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Unexpected error in get_content: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def run_async(coro):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        pending = asyncio.all_tasks(loop)
        for task in pending:
            task.cancel()
        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        loop.close()

@app.errorhandler(404)
def not_found(e):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def server_error(e):
    return jsonify({'error': 'Internal server error'}), 500

# Synchronous wrapper for the thumbnail endpoint
@app.route('/api/regenerate/thumbnail/<int:content_id>', methods=['POST'])
def regenerate_thumbnail(content_id):
    try:
        return run_async(regenerate_thumbnail_async(content_id))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

async def regenerate_thumbnail_async(content_id):
    pool = await aiomysql.create_pool(**DB_CONFIG)
    try:
        runware = Runware(api_key=RUNWARE_API_KEY)
        await runware.connect()

        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    'SELECT thumbnail_prompt FROM generated_content WHERE id = %s',
                    (content_id,)
                )

                result = await cursor.fetchone()
                if not result:
                    return jsonify({'error': 'Content not found'}), 404

                thumbnail_prompt = result[0]

                await generate_thumbnail(pool, thumbnail_prompt, content_id, runware)

                await cursor.execute(
                    'SELECT thumbnail_url FROM generated_content WHERE id = %s',
                    (content_id,)
                )

                result = await cursor.fetchone()
                return {
                    'success': True,
                    'thumbnail_url': result[0]
                }

    except Exception as e:
        logger.error(f"Error regenerating thumbnail: {e}")
        raise e
    finally:
        pool.close()
        await pool.wait_closed()

@app.route('/api/regenerate/content-image/<int:prompt_id>', methods=['POST'])
def regenerate_content_image(prompt_id):
    try:
        logger.info(f"Starting content image regeneration for prompt_id: {prompt_id}")
        result = run_async(regenerate_content_image_async(prompt_id))
        logger.info(f"Successfully completed regeneration for prompt_id: {prompt_id}")
        return result
    except Exception as e:
        error_msg = f"Error in regenerate_content_image for prompt_id {prompt_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500

async def regenerate_content_image_async(prompt_id):
    pool = None
    runware = None
    try:
        logger.info(f"Creating database pool for prompt_id: {prompt_id}")
        pool = await aiomysql.create_pool(**DB_CONFIG)

        logger.info("Initializing Runware connection")
        runware = Runware(api_key=RUNWARE_API_KEY)
        logger.info("Connecting to Runware...")
        await runware.connect()
        logger.info("Successfully connected to Runware")

        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                logger.info(f"Fetching image prompt data for prompt_id: {prompt_id}")
                await cursor.execute(
                    'SELECT content_id, image_prompt FROM image_prompts WHERE id = %s',
                    (prompt_id,)
                )
                result = await cursor.fetchone()

                if not result:
                    error_msg = f"Image prompt not found for ID: {prompt_id}"
                    logger.error(error_msg)
                    return jsonify({'error': error_msg}), 404

                content_id, image_prompt = result
                logger.info(f"Found content_id: {content_id} and image_prompt: {image_prompt[:50]}... for prompt_id: {prompt_id}")

                try:
                    logger.info("Creating image inference request...")
                    request_image = IImageInference(
                        positivePrompt=image_prompt,
                        negativePrompt="blurry, low quality, deformed, text, watermark",
                        model="runware:101@1",
                        numberResults=1,
                        height=2048,  # Standardized height
                        width=1152,   # Standardized width
                        CFGScale=7.5,
                    )

                    logger.info("Sending request to Runware for image generation...")
                    images = await runware.imageInference(requestImage=request_image)
                    logger.info("Received response from Runware")

                    if not images or len(images) == 0:
                        raise ValueError("No images returned from Runware")

                    image_url = images[0].imageURL
                    logger.info(f"Got image URL: {image_url[:50]}...")

                    logger.info("Updating database with new image URL...")
                    await cursor.execute(
                        'UPDATE image_prompts SET url = %s WHERE id = %s',
                        (image_url, prompt_id)
                    )
                    await conn.commit()
                    logger.info("Database updated successfully")

                    response_data = {
                        'success': True,
                        'image': {
                            'id': prompt_id,
                            'url': image_url,
                            'imagePrompt': image_prompt
                        }
                    }
                    logger.info("Preparing successful response")
                    return jsonify(response_data)

                except Exception as e:
                    error_msg = f"Error in image generation process: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    raise Exception(error_msg) from e

    except Exception as e:
        error_msg = f"Error in regenerate_content_image_async: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise Exception(error_msg) from e
    finally:
        logger.info("Entering cleanup phase...")
        if runware:
            try:
                logger.info("Disconnecting from Runware...")
                await runware.disconnect()
                logger.info("Successfully disconnected from Runware")
            except Exception as e:
                logger.error(f"Error disconnecting from Runware: {str(e)}", exc_info=True)

        if pool:
            try:
                logger.info("Closing database pool...")
                pool.close()
                await pool.wait_closed()
                logger.info("Successfully closed database pool")
            except Exception as e:
                logger.error(f"Error closing database pool: {str(e)}", exc_info=True)

def read_csv_with_fallback_encoding(file_path):
    """Try different encodings to read the CSV file"""
    encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']

    for encoding in encodings:
        try:
            with open(file_path, encoding=encoding) as file:
                content = list(csv.reader(file))
                return content  # Return the entire content as a list
        except UnicodeDecodeError:
            continue

    raise UnicodeDecodeError(
        "Unable to read the CSV file. Please ensure it's saved with UTF-8 encoding."
    )

@app.route('/api/upload/content', methods=['POST'])
def upload_content():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if not file.filename.endswith('.csv'):
        return jsonify({'error': 'File must be a CSV'}), 400

    try:
        # Create content-csv directory if it doesn't exist
        upload_dir = 'content-csv'
        os.makedirs(upload_dir, exist_ok=True)

        # Secure the filename and save the file
        filename = secure_filename(file.filename)
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)

        # Process the uploaded CSV file
        try:
            # Database connection
            conn = pymysql.connect(
                host=DB_HOST,
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME,
                charset="utf8mb4",
                cursorclass=pymysql.cursors.DictCursor,
            )
            cursor = conn.cursor()

            rows_processed = 0
            rows_successful = 0
            rows_skipped = 0
            rows_failed = 0

            # Read and process the CSV file using fallback encodings
            try:
                csv_content = read_csv_with_fallback_encoding(file_path)
                if not csv_content:
                    raise Exception("CSV file is empty")

                # Skip header row
                header = csv_content[0]
                rows = csv_content[1:]

                for row in rows:
                    rows_processed += 1
                    try:
                        if len(row) < 4:  # Ensure we have all required columns
                            rows_failed += 1
                            logger.error(f"Row {rows_processed} has insufficient columns: {len(row)}")
                            continue

                        scenario = clean_scenario(row[0])

                        # Skip if scenario already exists
                        if scenario_exists(cursor, scenario):
                            rows_skipped += 1
                            continue

                        # Insert the new content
                        cursor.execute(
                            """INSERT INTO generated_content
                               (scenario, empathetic_advice, practical_advice, thumbnail_prompt, thumbnail_url)
                               VALUES (%s, %s, %s, %s, NULL)""",
                            (scenario, row[1], row[2], row[3])
                        )
                        conn.commit()
                        rows_successful += 1

                    except Exception as e:
                        rows_failed += 1
                        logger.error(f"Error processing row {rows_processed}: {str(e)}")
                        continue

            finally:
                # Clean up the uploaded file
                if os.path.exists(file_path):
                    os.remove(file_path)

            return jsonify({
                'message': 'File processed successfully',
                'statistics': {
                    'processed': rows_processed,
                    'successful': rows_successful,
                    'skipped': rows_skipped,
                    'failed': rows_failed
                }
            })

        except Exception as e:
            return jsonify({'error': f'Error processing CSV: {str(e)}'}), 500
        finally:
            if 'conn' in locals():
                conn.close()

    except Exception as e:
        return jsonify({'error': f'Error uploading file: {str(e)}'}), 500

@app.route('/api/execute/script', methods=['POST', 'OPTIONS'])
def execute_script():
    # Handle preflight requests
    if request.method == 'OPTIONS':
        response = app.make_default_options_response()
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        return response

    try:
        # Get the absolute path to the script
        script_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            '_7runscript_untilspeech.py'
        )

        # Verify script exists
        if not os.path.exists(script_path):
            return jsonify({
                'status': 'error',
                'message': f'Script not found at {script_path}'
            }), 404

        python_executable = sys.executable

        # Execute the script using subprocess
        process = subprocess.Popen(
            [python_executable, script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.path.dirname(script_path)  # Set working directory to script location
        )

        # Get output and errors
        stdout, stderr = process.communicate()

        if process.returncode == 0:
            return jsonify({
                'status': 'success',
                'message': 'Script execution completed',
                'output': stdout
            }), 200
        else:
            return jsonify({
                'status': 'error',
                'message': 'Script execution failed',
                'error': stderr
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    conn = None
    cursor = None
    try:
        logger.info("Fetching categories")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)  # Use DictCursor for consistent return format

        cursor.execute('''
            SELECT id, name, description
            FROM category
            ORDER BY name ASC
        ''')

        categories = cursor.fetchall()
        logger.info(f"Successfully fetched {len(categories)} categories")

        # Convert datetime objects to strings to ensure JSON serialization works
        for item in categories:
            for key, value in item.items():
                if isinstance(value, (datetime.date, datetime.datetime)):
                    item[key] = value.isoformat()

        return jsonify(categories)
    except pymysql.MySQLError as e:
        error_msg = f"Database error in get_categories: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Unexpected error in get_categories: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

@app.route('/api/content-prompts', methods=['GET'])
def get_content_prompts():
    conn = None
    cursor = None
    try:
        logger.info("Fetching content prompts")
        conn = pymysql.connect(**DB_CONFIG)  # Use the standard DB_CONFIG
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        category_exists = check_table_exists(cursor, 'category')

        if not content_prompt_exists:
            logger.warning("content_prompt table does not exist")
            return jsonify([])  # Return empty array instead of error

        if category_exists:
            # Both tables exist, use the original query
            cursor.execute('''
                SELECT
                    cp.id,
                    cp.title,
                    cp.category_id,
                    cp.content_prompt,
                    cp.call_to_action,
                    cp.output_format,
                    cp.voice_id,
                    cp.video_format,
                    cp.multiple_topics,
                    cp.num_sentences,
                    cp.generated_at,
                    c.name as category_name,
                    c.description as category_description
                FROM content_prompt cp
                LEFT JOIN category c ON cp.category_id = c.id
                ORDER BY cp.generated_at DESC
            ''')
        else:
            # Only content_prompt exists, don't join with category
            logger.warning("category table does not exist, querying without join")
            cursor.execute('''
                SELECT
                    id,
                    title,
                    category_id,
                    content_prompt,
                    call_to_action,
                    output_format,
                    voice_id,
                    video_format,
                    multiple_topics,
                    num_sentences,
                    generated_at,
                    NULL as category_name,
                    NULL as category_description
                FROM content_prompt
                ORDER BY generated_at DESC
            ''')

        results = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for item in results:
            if item.get('generated_at') and isinstance(item['generated_at'], (datetime.date, datetime.datetime)):
                item['generated_at'] = item['generated_at'].isoformat()

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error fetching content prompts: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts', methods=['POST'])
def create_content_prompt():
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Received data for content prompt creation: {data}")

        # Convert category_id to integer
        try:
            category_id = int(data['category_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid category_id: {data.get('category_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Ensure num_sentences is an integer
        try:
            num_sentences = int(data['num_sentences'])
        except (ValueError, TypeError):
            error_msg = f"Invalid num_sentences: {data.get('num_sentences', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Convert multiple_topics to integer (0 or 1) for MySQL
        multiple_topics = 1 if data.get('multiple_topics') else 0

        # Use the standard DB_CONFIG
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        try:
            logger.info("Executing query to insert content prompt")
            cursor.execute(
                '''
                INSERT INTO content_prompt
                    (title, category_id, content_prompt, call_to_action,
                    output_format, voice_id, video_format, multiple_topics,
                    num_sentences)
                VALUES
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''',
                (
                    data.get('title', ''),
                    category_id,
                    data.get('content_prompt', ''),
                    data.get('call_to_action', ''),
                    data.get('output_format', 'short'),
                    data.get('voice_id', ''),
                    data.get('video_format', '1min'),
                    multiple_topics,
                    num_sentences
                )
            )
            conn.commit()
            new_id = cursor.lastrowid
            logger.info(f"Successfully created content prompt with ID: {new_id}")
            return jsonify({'success': True, 'id': new_id})

        except pymysql.Error as e:
            error_msg = f"Database error in create_content_prompt: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Unexpected error in create_content_prompt: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts/<int:prompt_id>', methods=['PUT'])
def update_content_prompt(prompt_id):
    try:
        data = request.json
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        cursor.execute(
            '''
            UPDATE content_prompt
            SET title = %s,
                category_id = %s,
                content_prompt = %s,
                call_to_action = %s,
                output_format = %s,
                voice_id = %s,
                video_format = %s,
                multiple_topics = %s,
                num_sentences = %s
            WHERE id = %s
            ''',
            (data['title'], data['category_id'], data['content_prompt'],
             data['call_to_action'], data['output_format'], data['voice_id'],
             data['video_format'], data['multiple_topics'], data['num_sentences'],
             prompt_id)
        )
        conn.commit()

        return jsonify({'message': 'Prompt updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/content-prompts/<int:prompt_id>', methods=['DELETE'])
def delete_content_prompt(prompt_id):
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM content_prompts WHERE id = %s', (prompt_id,))
        conn.commit()

        return jsonify({'message': 'Prompt deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

# Define the base directory for content
CONTENT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

@app.route('/api/content-images/<content_id>')
def get_content_images(content_id):
    try:
        images_dir = os.path.join(CONTENT_DIR, 'content-images', str(content_id))
        if not os.path.exists(images_dir):
            return jsonify({'images': []})

        # Get all image files from the directory
        images = [f for f in os.listdir(images_dir)
                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        return jsonify({'images': images})
    except Exception as e:
        current_app.logger.error(f"Error getting content images: {str(e)}")
        return jsonify({'images': []})

@app.route('/content-images/<content_id>/<path:filename>')
def serve_content_image(content_id, filename):
    try:
        images_dir = os.path.join(CONTENT_DIR, 'content-images', str(content_id))
        return send_from_directory(images_dir, filename)
    except Exception as e:
        current_app.logger.error(f"Error serving content image: {str(e)}")
        return send_from_directory(
            os.path.join(CONTENT_DIR, 'static'),
            'placeholder.jpg'
        )

@app.route('/content-thumbnail/<content_id>/<path:filename>')
def serve_thumbnail(content_id, filename):
    try:
        thumbnail_dir = os.path.join(CONTENT_DIR, 'content-thumbnail', str(content_id))
        # Create directory if it doesn't exist
        os.makedirs(thumbnail_dir, exist_ok=True)

        # Log the full path for debugging
        full_path = os.path.join(thumbnail_dir, filename)
        current_app.logger.info(f"Attempting to serve thumbnail from: {full_path}")

        if not os.path.exists(full_path):
            # If thumbnail doesn't exist, return a default image
            return send_from_directory(
                os.path.join(CONTENT_DIR, 'static'),
                'placeholder.jpg'
            )

        return send_from_directory(thumbnail_dir, filename)
    except Exception as e:
        current_app.logger.error(f"Error serving thumbnail: {str(e)}")
        # Return default image on error
        return send_from_directory(
            os.path.join(CONTENT_DIR, 'static'),
            'placeholder.jpg'
        )

if __name__ == '__main__':
    app.run(debug=True)
