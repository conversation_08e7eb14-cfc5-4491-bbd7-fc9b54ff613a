import React from 'react';
import { Modal } from 'react-bootstrap';

export const Dialog = ({ children, open, onOpenChange }) => {
  return (
    <Modal show={open} onHide={() => onOpenChange(false)}>
      {children}
    </Modal>
  );
};

export const DialogTrigger = ({ children, asChild, onClick }) => {
  return React.cloneElement(children, { onClick });
};

export const DialogContent = ({ children, className }) => {
  return <Modal.Body className={className}>{children}</Modal.Body>;
};

export const DialogHeader = ({ children }) => {
  return <Modal.Header closeButton>{children}</Modal.Header>;
};

export const DialogTitle = ({ children }) => {
  return <Modal.Title>{children}</Modal.Title>;
};