import os
import sys
import logging
from flask import Flask
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Create a new Flask app
app = Flask(__name__)

# Enable CORS for all routes
CORS(app, resources={
    r"/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"]
    }
})

# Import the Grok blueprint
from API.grok_api import grok_bp

# Register the blueprint
app.register_blueprint(grok_bp)

# Add a root endpoint
@app.route('/')
def index():
    return {
        'message': 'Grok API server is running',
        'endpoints': [
            '/api/grok/test',
            '/api/grok/status',
            '/api/grok/generate-prompt'
        ]
    }

if __name__ == '__main__':
    logger.info("Starting standalone Grok API server...")
    logger.info("API endpoints available at http://localhost:5001/api/grok/")

    # Run the Flask app on a different port
    app.run(debug=True, host='0.0.0.0', port=5001)
