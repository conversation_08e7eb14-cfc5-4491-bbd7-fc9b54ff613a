"""
Celery worker for the VisionFrame AI queue system.
This script starts the Celery worker process.
"""

import os
import logging
from celery_config import celery_app
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    logger.info("Starting VisionFrame AI worker...")
    
    # Import all task modules to register them with Celery
    import tasks.image_tasks
    import tasks.speech_tasks
    import tasks.slideshow_tasks
    import tasks.subtitle_tasks
    import tasks.video_tasks
    import tasks.queue_manager
    
    # Start the worker
    celery_app.worker_main(
        argv=[
            'worker',
            '--loglevel=INFO',
            '--concurrency=4',  # Number of worker processes
            '-n', 'visionframe_worker@%h',  # Worker name
            '-Q', 'default,image_generation,speech_generation,slideshow_creation,subtitle_creation,video_mixing',  # Queues to consume from
        ]
    )
