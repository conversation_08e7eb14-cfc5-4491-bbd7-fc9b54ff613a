"""
Celery Application for VisionFrame AI

This module initializes the Celery application and imports all task modules.
"""

import os
import sys
from celery import Celery

# Add the parent directory to sys.path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Create the Celery application
app = Celery('visionframe_tasks')

# Load configuration from celeryconfig.py
app.config_from_object('celeryconfig')

# Auto-discover tasks in all modules
app.autodiscover_tasks(['tasks.queue_manager',
                        'tasks.image_tasks',
                        'tasks.speech_tasks',
                        'tasks.slideshow_tasks',
                        'tasks.subtitle_tasks',
                        'tasks.video_tasks',
                        'tasks.media_tasks',
                        'tasks.task_utils'])

# This ensures that the Celery app is properly initialized
# before any tasks are registered
if __name__ == '__main__':
    app.start()
