import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

const Profile = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    // Simulate successful profile update
    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 3000);
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1>My Profile</h1>
        </Col>
      </Row>
      
      {showSuccess && (
        <Row className="mb-4">
          <Col>
            <Alert variant="success" onClose={() => setShowSuccess(false)} dismissible>
              Profile updated successfully!
            </Alert>
          </Col>
        </Row>
      )}
      
      <Row>
        <Col md={4}>
          <Card className="mb-4 text-center p-4">
            <div className="d-flex justify-content-center mb-3">
              <AccountCircleIcon style={{ fontSize: 120, color: '#0066ff' }} />
            </div>
            <Card.Title>Admin User</Card.Title>
            <Card.Subtitle className="mb-2 text-muted">Administrator</Card.Subtitle>
            <div className="mt-3">
              <Button variant="outline-primary" size="sm">Change Avatar</Button>
            </div>
          </Card>
          
          <Card className="mb-4">
            <Card.Header>Account Information</Card.Header>
            <Card.Body>
              <div className="mb-2">
                <strong>Username:</strong> admin
              </div>
              <div className="mb-2">
                <strong>Email:</strong> <EMAIL>
              </div>
              <div className="mb-2">
                <strong>Role:</strong> Administrator
              </div>
              <div className="mb-2">
                <strong>Member Since:</strong> Jan 1, 2023
              </div>
              <div className="mb-2">
                <strong>Last Login:</strong> Today
              </div>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={8}>
          <Card className="mb-4">
            <Card.Header>Edit Profile</Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>First Name</Form.Label>
                      <Form.Control type="text" defaultValue="Admin" />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Last Name</Form.Label>
                      <Form.Control type="text" defaultValue="User" />
                    </Form.Group>
                  </Col>
                </Row>
                
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control type="email" defaultValue="<EMAIL>" />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Phone Number</Form.Label>
                  <Form.Control type="tel" defaultValue="+****************" />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Bio</Form.Label>
                  <Form.Control 
                    as="textarea" 
                    rows={3} 
                    defaultValue="Administrator of the VLog Generation system."
                  />
                </Form.Group>
                
                <Button variant="primary" type="submit">
                  Save Changes
                </Button>
              </Form>
            </Card.Body>
          </Card>
          
          <Card>
            <Card.Header>Change Password</Card.Header>
            <Card.Body>
              <Form>
                <Form.Group className="mb-3">
                  <Form.Label>Current Password</Form.Label>
                  <Form.Control type="password" />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>New Password</Form.Label>
                  <Form.Control type="password" />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Confirm New Password</Form.Label>
                  <Form.Control type="password" />
                </Form.Group>
                
                <Button variant="primary" type="submit">
                  Update Password
                </Button>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Profile;
