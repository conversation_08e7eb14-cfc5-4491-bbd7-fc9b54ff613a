import sqlite3
import logging
import sys
import os
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    for column in columns:
        if column[1] == column_name:
            return True
    return False

def add_field_to_table(table_name, field_name, field_type):
    """Add a field to a table."""
    db_path = 'vlog_content.db'
    
    # Check if database file exists
    if not os.path.exists(db_path):
        logger.error(f"Database file {db_path} does not exist.")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        if not check_table_exists(cursor, table_name):
            logger.error(f"{table_name} table does not exist.")
            return False
        
        # Check if column already exists
        if check_column_exists(cursor, table_name, field_name):
            logger.info(f"{field_name} column already exists in {table_name} table.")
            return True
        
        # Add column to table
        logger.info(f"Adding {field_name} column to {table_name} table...")
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type}")
        conn.commit()
        logger.info(f"{field_name} column added successfully!")
        
        # Verify the column was added
        if check_column_exists(cursor, table_name, field_name):
            logger.info(f"Verified {field_name} column exists in {table_name} table.")
            return True
        else:
            logger.error(f"Failed to add {field_name} column to {table_name} table.")
            return False
        
    except Exception as e:
        logger.error(f"Error adding {field_name} field: {str(e)}")
        return False
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def list_table_columns(table_name):
    """List all columns in a table."""
    db_path = 'vlog_content.db'
    
    # Check if database file exists
    if not os.path.exists(db_path):
        logger.error(f"Database file {db_path} does not exist.")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table exists
        if not check_table_exists(cursor, table_name):
            logger.error(f"{table_name} table does not exist.")
            return False
        
        # Get table schema
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Print column information
        logger.info(f"Columns in {table_name} table:")
        for column in columns:
            logger.info(f"  {column[0]}: {column[1]} ({column[2]}){' PRIMARY KEY' if column[5] else ''}{' NOT NULL' if column[3] else ''}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error listing columns: {str(e)}")
        return False
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Add a field to a table in the database.')
    parser.add_argument('--table', type=str, default='content_prompt_claude', help='Table name')
    parser.add_argument('--field', type=str, default='content_prompt', help='Field name')
    parser.add_argument('--type', type=str, default='TEXT', help='Field type')
    parser.add_argument('--list', action='store_true', help='List table columns')
    
    args = parser.parse_args()
    
    logger.info(f"Starting script to manage fields in {args.table} table...")
    
    # Print current working directory for debugging
    logger.info(f"Current working directory: {os.getcwd()}")
    
    if args.list:
        # List table columns
        success = list_table_columns(args.table)
    else:
        # Add field to table
        success = add_field_to_table(args.table, args.field, args.type)
    
    if success:
        logger.info("Script completed successfully!")
    else:
        logger.error("Script failed to complete successfully.")
        sys.exit(1)

if __name__ == "__main__":
    main()
