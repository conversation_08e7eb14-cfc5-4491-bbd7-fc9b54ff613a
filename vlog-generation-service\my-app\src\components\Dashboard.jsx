import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spinner, Table } from 'react-bootstrap';
import './Dashboard.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

function Dashboard() {
    const [contents, setContents] = useState([]);
    const [contentImages, setContentImages] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [regenerating, setRegenerating] = useState({});
    const [generatingImage, setGeneratingImage] = useState({});
    const [showUpload, setShowUpload] = useState(false);
    const [isExecutingScript, setIsExecutingScript] = useState(false);

    const fetchContent = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/content`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            setContents(data);

            // This part might cause unnecessary reloads
            // data.forEach(content => {
            //     fetchContentImages(content.id);
            // });

            // Instead, fetch all images at once
            const imagePromises = data.map(content => fetchContentImages(content.id));
            await Promise.all(imagePromises);
        } catch (error) {
            console.error('Error fetching content:', error);
            setError(error.message);
        }
    };

    const fetchContentImages = async (contentId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/content-images/${contentId}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            setContentImages(prev => ({
                ...prev,
                [contentId]: data.images
            }));
        } catch (error) {
            console.error(`Error fetching images for content ${contentId}:`, error);
        }
    };

    const getLocalImagePath = (contentId, filename) => {
        return `${API_BASE_URL}/content-images/${contentId}/${filename}`;
    };

    const getLocalThumbnailPath = (contentId) => {
        return `${API_BASE_URL}/content-thumbnail/${contentId}/${contentId}.jpg`;
    };

    useEffect(() => {
        const fetchAllData = async () => {
            setLoading(true);
            try {
                await fetchContent();
            } finally {
                setLoading(false);
            }
        };

        fetchAllData();

        // Add this empty dependency array to ensure it only runs once on mount
    }, []); // Empty dependency array

    const handleImageError = (e) => {
        e.target.src = '/placeholder-image.jpg';
        console.error('Failed to load image:', e.target.src);
    };

    const handleRegenerateThumbnail = async (contentId) => {
        try {
            setRegenerating(prev => ({ ...prev, [contentId]: true }));

            const response = await fetch(
                `${API_BASE_URL}/api/regenerate/thumbnail/${contentId}`,
                { method: 'POST' }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            const timestamp = new Date().getTime();
            const newThumbnailUrl = `${data.thumbnail_url}?t=${timestamp}`;

            setContents(prevContents =>
                prevContents.map(content =>
                    content.id === contentId
                        ? { ...content, thumbnail_url: newThumbnailUrl }
                        : content
                )
            );
        } catch (error) {
            console.error('Error regenerating thumbnail:', error);
            alert('Failed to regenerate thumbnail. Please try again.');
        } finally {
            setRegenerating(prev => ({ ...prev, [contentId]: false }));
        }
    };

    // Added from .js version
    const handleRegenerateContentImage = async (promptId) => {
        try {
            setGeneratingImage(prev => ({ ...prev, [promptId]: true }));

            const response = await fetch(
                `${API_BASE_URL}/api/regenerate/content-image/${promptId}`,
                { method: 'POST' }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            setContents(prevContents =>
                prevContents.map(content => ({
                    ...content,
                    images: content.images.map(img =>
                        img.id === promptId ? data.image : img
                    )
                }))
            );
        } catch (error) {
            console.error('Error regenerating content image:', error);
            alert('Failed to regenerate content image. Please try again.');
        } finally {
            setGeneratingImage(prev => ({ ...prev, [promptId]: false }));
        }
    };

    const handleExecuteScript = async () => {
        setIsExecutingScript(true);
        try {
            const response = await fetch(`${API_BASE_URL}/api/execute/script`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to execute script');
            }

            alert('Script execution completed successfully!');
            await fetchContent();

        } catch (error) {
            console.error('Error executing script:', error);
            alert('Failed to execute script: ' + error.message);
        } finally {
            setIsExecutingScript(false);
        }
    };

    return (
        <div className="dashboard-scroll-container">
            <div className="dashboard-container">
                {loading ? (
                    <div>Loading...</div>
                ) : error ? (
                    <div>Error: {error}</div>
                ) : (
                    <>
                        <div className="dashboard-header">
                            <h1 className="dashboard-title">Content Dashboard</h1>
                            <Button
                                variant="primary"
                                onClick={() => setShowUpload(!showUpload)}
                                className="upload-toggle-btn"
                            >
                                {showUpload ? 'Hide Upload Form' : 'Upload New Content'}
                            </Button>
                            <Button
                                variant="primary"
                                onClick={handleExecuteScript}
                                disabled={isExecutingScript}
                                className="mb-3"
                            >
                                {isExecutingScript ? (
                                    <>
                                        <Spinner
                                            as="span"
                                            animation="border"
                                            size="sm"
                                            role="status"
                                            aria-hidden="true"
                                            className="me-2"
                                        />
                                        Executing Script...
                                    </>
                                ) : (
                                    'Execute Generation Script'
                                )}
                            </Button>
                        </div>

                        {showUpload && (
                            <div className="upload-section">
                                <ContentUpload onUploadComplete={() => {
                                    fetchContent();
                                    setShowUpload(false);
                                }} />
                            </div>
                        )}

                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>ID</TableHead>
                                    <TableHead>Scenario</TableHead>
                                    <TableHead>Advice</TableHead>
                                    <TableHead className="images-column">Thumbnail</TableHead>
                                    <TableHead className="images-column">Content Images</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {contents.map((item) => (
                                    <TableRow key={item.id}>
                                        <TableCell>{item.id}</TableCell>
                                        <TableCell>{item.scenario}</TableCell>
                                        <TableCell>
                                            <div className="advice-container">
                                                <div className="advice-section">
                                                    <div className="advice-label">Empathetic Advice:</div>
                                                    <div className="advice-content">{item.empathetic_advice}</div>
                                                </div>
                                                <div className="advice-section">
                                                    <div className="advice-label">Practical Advice:</div>
                                                    <div className="advice-content">{item.practical_advice}</div>
                                                </div>
                                                <div className="advice-section">
                                                    <div className="advice-label">Thumbnail Prompt:</div>
                                                    <div className="advice-content">{item.thumbnail_prompt}</div>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="thumbnail-container">
                                                <img
                                                    src={getLocalThumbnailPath(item.id)}
                                                    alt="Thumbnail"
                                                    className="thumbnail-image"
                                                    onError={handleImageError}
                                                />
                                                <Button
                                                    variant="warning"
                                                    size="sm"
                                                    onClick={() => handleRegenerateThumbnail(item.id)}
                                                    disabled={regenerating[item.id]}
                                                    className="mt-2"
                                                >
                                                    {regenerating[item.id] ? (
                                                        <><Spinner size="sm" /> Regenerating...</>
                                                    ) : (
                                                        'Regenerate Thumbnail'
                                                    )}
                                                </Button>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="content-images-container">
                                                {contentImages[item.id] && contentImages[item.id].length > 0 ? (
                                                    contentImages[item.id].map((filename) => (
                                                        <div key={filename} className="image-item">
                                                            <img
                                                                src={getLocalImagePath(item.id, filename)}
                                                                alt={`Content image ${filename}`}
                                                                className="content-image"
                                                                onError={handleImageError}
                                                            />
                                                        </div>
                                                    ))
                                                ) : (
                                                    <div>No images available</div>
                                                )}
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </>
                )}
            </div>
        </div>
    );
}

export default Dashboard;














