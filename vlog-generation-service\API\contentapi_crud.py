import os
import csv
import pymysql
from flask import Flask, jsonify, request, send_from_directory, current_app
from werkzeug.utils import secure_filename
import sys
import logging
import asyncio
import aiomysql
from flask_cors import CORS
from dotenv import load_dotenv
import re
import subprocess
import datetime
from runware import IImageInference

# Import Grok blueprint
from API.grok_api import grok_bp

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,  # This will now use 'vlog_generator' from .env
    'cursorclass': pymysql.cursors.DictCursor
}

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from _3content_generate_images import generate_thumbnail, DB_CONFIG, RUNWARE_API_KEY, Runware

app = Flask(__name__)
# Enable CORS for all routes
CORS(app, resources={
    r"/*": {"origins": "*", "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allow_headers": ["Content-Type", "Authorization"]}
})

# Register blueprints
app.register_blueprint(grok_bp)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_scenario(scenario):
    """Remove numbering from the beginning of the scenario."""
    return re.sub(r'^\d+\.\s*', '', scenario.strip())

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

# Initialize database tables
def init_db():
    """Initialize database tables if they don't exist"""
    conn = None
    cursor = None
    try:
        logger.info("Initializing database tables...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if category table exists
        if not check_table_exists(cursor, 'category'):
            logger.info("Creating category table...")
            cursor.execute("""
                CREATE TABLE category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)

            # Insert some default categories
            cursor.execute("""
                INSERT INTO category (name, description) VALUES
                ('General', 'General purpose content'),
                ('Lifestyle', 'Lifestyle related content'),
                ('Health', 'Health and wellness content'),
                ('Technology', 'Technology related content')
            """)
            conn.commit()
            logger.info("Category table created and populated successfully")

        # Check if content_prompt table exists
        if not check_table_exists(cursor, 'content_prompt'):
            logger.info("Creating content_prompt table...")
            cursor.execute("""
                CREATE TABLE content_prompt (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    category_id INT,
                    content_prompt TEXT NOT NULL,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'standard',
                    voice_id VARCHAR(100),
                    video_format VARCHAR(50) DEFAULT '1min',
                    multiple_topics BOOLEAN DEFAULT FALSE,
                    num_sentences INT DEFAULT 5,
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES category(id)
                )
            """)
            conn.commit()
            logger.info("Content_prompt table created successfully")

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Initialize database on startup
init_db()

@app.route('/api/content-prompts', methods=['GET'])
def get_content_prompts():
    conn = None
    cursor = None
    try:
        logger.info("Fetching content prompts")
        conn = pymysql.connect(**DB_CONFIG)  # Use the standard DB_CONFIG
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        category_exists = check_table_exists(cursor, 'category')

        if not content_prompt_exists:
            logger.warning("content_prompt table does not exist")
            return jsonify([])  # Return empty array instead of error

        if category_exists:
            # Both tables exist, use the original query
            cursor.execute('''
                SELECT
                    cp.id,
                    cp.title,
                    cp.category_id,
                    cp.content_prompt,
                    cp.call_to_action,
                    cp.output_format,
                    cp.voice_id,
                    cp.video_format,
                    cp.multiple_topics,
                    cp.num_sentences,
                    cp.generated_at,
                    c.name as category_name,
                    c.description as category_description
                FROM content_prompt cp
                LEFT JOIN category c ON cp.category_id = c.id
                ORDER BY cp.generated_at DESC
            ''')
        else:
            # Only content_prompt exists, don't join with category
            logger.warning("category table does not exist, querying without join")
            cursor.execute('''
                SELECT
                    id,
                    title,
                    category_id,
                    content_prompt,
                    call_to_action,
                    output_format,
                    voice_id,
                    video_format,
                    multiple_topics,
                    num_sentences,
                    generated_at,
                    NULL as category_name,
                    NULL as category_description
                FROM content_prompt
                ORDER BY generated_at DESC
            ''')

        results = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for item in results:
            if item.get('generated_at') and isinstance(item['generated_at'], (datetime.date, datetime.datetime)):
                item['generated_at'] = item['generated_at'].isoformat()

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error fetching content prompts: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts/<int:prompt_id>', methods=['GET'])
def get_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Fetching content prompt with ID: {prompt_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        category_exists = check_table_exists(cursor, 'category')

        if not content_prompt_exists:
            return jsonify({'error': 'Content prompt table does not exist'}), 404

        if category_exists:
            cursor.execute('''
                SELECT
                    cp.id,
                    cp.title,
                    cp.category_id,
                    cp.content_prompt,
                    cp.call_to_action,
                    cp.output_format,
                    cp.voice_id,
                    cp.video_format,
                    cp.multiple_topics,
                    cp.num_sentences,
                    cp.generated_at,
                    c.name as category_name,
                    c.description as category_description
                FROM content_prompt cp
                LEFT JOIN category c ON cp.category_id = c.id
                WHERE cp.id = %s
            ''', (prompt_id,))
        else:
            cursor.execute('''
                SELECT
                    id,
                    title,
                    category_id,
                    content_prompt,
                    call_to_action,
                    output_format,
                    voice_id,
                    video_format,
                    multiple_topics,
                    num_sentences,
                    generated_at,
                    NULL as category_name,
                    NULL as category_description
                FROM content_prompt
                WHERE id = %s
            ''', (prompt_id,))

        result = cursor.fetchone()

        if not result:
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

        # Convert datetime objects to strings for JSON serialization
        if result.get('generated_at') and isinstance(result['generated_at'], (datetime.date, datetime.datetime)):
            result['generated_at'] = result['generated_at'].isoformat()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error fetching content prompt: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts', methods=['POST'])
def create_content_prompt():
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Received data for content prompt creation: {data}")

        # Convert category_id to integer
        try:
            category_id = int(data['category_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid category_id: {data.get('category_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Ensure num_sentences is an integer
        try:
            num_sentences = int(data['num_sentences'])
        except (ValueError, TypeError):
            error_msg = f"Invalid num_sentences: {data.get('num_sentences', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Convert multiple_topics to integer (0 or 1) for MySQL
        multiple_topics = 1 if data.get('multiple_topics') else 0

        # Use the standard DB_CONFIG
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        category_exists = check_table_exists(cursor, 'category')

        if not content_prompt_exists:
            # Create content_prompt table if it doesn't exist
            logger.info("Creating content_prompt table...")
            cursor.execute("""
                CREATE TABLE content_prompt (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    category_id INT,
                    content_prompt TEXT NOT NULL,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'standard',
                    voice_id VARCHAR(100),
                    video_format VARCHAR(50) DEFAULT '1min',
                    multiple_topics BOOLEAN DEFAULT FALSE,
                    num_sentences INT DEFAULT 5,
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()

        if not category_exists:
            # Create category table if it doesn't exist
            logger.info("Creating category table...")
            cursor.execute("""
                CREATE TABLE category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)

            # Insert default category
            cursor.execute("""
                INSERT INTO category (name, description) VALUES
                ('General', 'General purpose content')
            """)
            conn.commit()

            # Set category_id to the default category
            cursor.execute("SELECT id FROM category LIMIT 1")
            result = cursor.fetchone()
            if result:
                category_id = result['id']

        # Add foreign key if both tables exist
        if content_prompt_exists and category_exists:
            # Check if foreign key exists
            cursor.execute("""
                SELECT COUNT(*) as count FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_NAME = 'content_prompt'
                AND REFERENCED_TABLE_NAME = 'category'
            """)
            result = cursor.fetchone()

            if result and result['count'] == 0:
                # Add foreign key constraint
                try:
                    cursor.execute("""
                        ALTER TABLE content_prompt
                        ADD CONSTRAINT fk_category
                        FOREIGN KEY (category_id) REFERENCES category(id)
                    """)
                    conn.commit()
                except Exception as e:
                    logger.warning(f"Could not add foreign key constraint: {str(e)}")

        try:
            logger.info("Executing query to insert content prompt")
            cursor.execute(
                '''
                INSERT INTO content_prompt
                    (title, category_id, content_prompt, call_to_action,
                    output_format, voice_id, video_format, multiple_topics,
                    num_sentences)
                VALUES
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''',
                (
                    data.get('title', ''),
                    category_id,
                    data.get('content_prompt', ''),
                    data.get('call_to_action', ''),
                    data.get('output_format', 'standard'),
                    data.get('voice_id', ''),
                    data.get('video_format', '1min'),
                    multiple_topics,
                    num_sentences
                )
            )
            conn.commit()
            new_id = cursor.lastrowid
            logger.info(f"Successfully created content prompt with ID: {new_id}")
            return jsonify({'success': True, 'id': new_id})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error creating content prompt: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in create_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts/<int:prompt_id>', methods=['PUT'])
def update_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Updating content prompt with ID {prompt_id}: {data}")

        # Convert category_id to integer
        try:
            category_id = int(data['category_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid category_id: {data.get('category_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Ensure num_sentences is an integer
        try:
            num_sentences = int(data['num_sentences'])
        except (ValueError, TypeError):
            error_msg = f"Invalid num_sentences: {data.get('num_sentences', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Convert multiple_topics to integer (0 or 1) for MySQL
        multiple_topics = 1 if data.get('multiple_topics') else 0

        # Use the standard DB_CONFIG
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if content prompt exists
        cursor.execute("SELECT id FROM content_prompt WHERE id = %s", (prompt_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

        try:
            logger.info("Executing query to update content prompt")
            cursor.execute(
                '''
                UPDATE content_prompt
                SET
                    title = %s,
                    category_id = %s,
                    content_prompt = %s,
                    call_to_action = %s,
                    output_format = %s,
                    voice_id = %s,
                    video_format = %s,
                    multiple_topics = %s,
                    num_sentences = %s
                WHERE id = %s
                ''',
                (
                    data.get('title', ''),
                    category_id,
                    data.get('content_prompt', ''),
                    data.get('call_to_action', ''),
                    data.get('output_format', 'standard'),
                    data.get('voice_id', ''),
                    data.get('video_format', '1min'),
                    multiple_topics,
                    num_sentences,
                    prompt_id
                )
            )
            conn.commit()

            if cursor.rowcount == 0:
                return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

            logger.info(f"Successfully updated content prompt with ID: {prompt_id}")
            return jsonify({'success': True, 'id': prompt_id})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error updating content prompt: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in update_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/content-prompts/<int:prompt_id>', methods=['DELETE'])
def delete_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Deleting content prompt with ID: {prompt_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if content prompt exists
        cursor.execute("SELECT id FROM content_prompt WHERE id = %s", (prompt_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

        try:
            cursor.execute("DELETE FROM content_prompt WHERE id = %s", (prompt_id,))
            conn.commit()

            if cursor.rowcount == 0:
                return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

            logger.info(f"Successfully deleted content prompt with ID: {prompt_id}")
            return jsonify({'success': True})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error deleting content prompt: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in delete_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/categories', methods=['GET'])
def get_categories():
    conn = None
    cursor = None
    try:
        logger.info("Fetching categories")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if category table exists
        if not check_table_exists(cursor, 'category'):
            # Create and populate category table
            logger.info("Creating category table...")
            cursor.execute("""
                CREATE TABLE category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)

            # Insert default categories
            cursor.execute("""
                INSERT INTO category (name, description) VALUES
                ('General', 'General purpose content'),
                ('Lifestyle', 'Lifestyle related content'),
                ('Health', 'Health and wellness content'),
                ('Technology', 'Technology related content')
            """)
            conn.commit()

        cursor.execute('''
            SELECT id, name, description
            FROM category
            ORDER BY name ASC
        ''')

        categories = cursor.fetchall()
        logger.info(f"Successfully fetched {len(categories)} categories")

        # Convert datetime objects to strings to ensure JSON serialization works
        for item in categories:
            for key, value in item.items():
                if isinstance(value, (datetime.date, datetime.datetime)):
                    item[key] = value.isoformat()

        return jsonify(categories)
    except Exception as e:
        error_msg = f"Error fetching categories: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == '__main__':
    # Initialize database tables
    init_db()

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
