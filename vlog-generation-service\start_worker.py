"""
Start Celery Worker Script

This script starts a Celery worker for processing tasks in the VisionFrame AI queue system.
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def start_worker():
    """Start a Celery worker"""
    print("Starting Celery worker...")
    
    # Determine the platform-specific command
    if sys.platform == 'win32':
        # Windows-specific command
        cmd = [
            'celery',
            '-A', 'tasks',
            'worker',
            '--loglevel=info',
            '--pool=solo',  # Use solo pool on Windows
            '--concurrency=1'  # Set concurrency to 1 for Windows
        ]
    else:
        # Linux/Mac command
        cmd = [
            'celery',
            '-A', 'tasks',
            'worker',
            '--loglevel=info',
            '--concurrency=4'  # Use higher concurrency on Linux/Mac
        ]
    
    # Start the worker
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("Worker stopped by user")
    except Exception as e:
        print(f"Error starting worker: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_worker()
