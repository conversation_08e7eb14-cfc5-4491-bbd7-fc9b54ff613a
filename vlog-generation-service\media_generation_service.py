"""
Media Generation Pipeline Service

This service orchestrates the content generation pipeline using existing scripts:
1. Import CSV data
2. Generate prompts
3. Generate images
4. Generate speech
5. Create videos with images and speech
6. Add subtitles to videos

Each step releases memory after completion to optimize resource usage.
"""

import os
import sys
import gc
import asyncio
import logging
import importlib
import traceback
import aiomysql
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vlog_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'db': os.getenv('DB_DATABASE', 'vlog_generator'),
}

# Pipeline steps configuration
PIPELINE_STEPS = [
    {
        'name': 'Import CSV',
        'module': '_1content_importcsv',
        'enabled': True,
        'async': False
    },
    {
        'name': 'Generate Prompts',
        'module': '_2content_generate_prompt',
        'enabled': True,
        'async': True,
        'main_function': 'process_pending_content'
    },
    {
        'name': 'Generate Images',
        'module': '_3content_generate_images',
        'enabled': True,
        'async': True,
        'main_function': 'process_pending_imageprompts'
    },
    {
        'name': 'Generate Speech',
        'module': '_4content_generate_speech',
        'enabled': True,
        'async': True,
        'main_function': 'main'
    },
    {
        'name': 'Create Videos',
        'module': '_5content_generate_videoimage_speech_moviepy',
        'enabled': True,
        'async': True,
        'main_function': 'main'
    },
    {
        'name': 'Add Subtitles',
        'module': '_6content_generate_video_subtitle',
        'enabled': True,
        'async': True,
        'main_function': 'main'
    }
]

async def create_db_pool():
    """Create a database connection pool"""
    return await aiomysql.create_pool(**DB_CONFIG)

async def log_event(pool, event_type, message, status="INFO", error=None):
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def run_async_step(pool, step):
    """Run an asynchronous pipeline step"""
    try:
        await log_event(pool, "STEP_START", f"Starting step: {step['name']}")
        
        # Import the module
        module = importlib.import_module(step['module'])
        
        # Get the main function
        main_func = getattr(module, step['main_function'])
        
        # Run the main function
        if asyncio.iscoroutinefunction(main_func):
            await main_func()
        else:
            # If it's not a coroutine function but returns a coroutine
            result = main_func()
            if asyncio.iscoroutine(result):
                await result
                
        await log_event(pool, "STEP_COMPLETE", f"Completed step: {step['name']}")
        
        # Clean up to release memory
        del module
        gc.collect()
        
        return True
    except Exception as e:
        await log_event(
            pool,
            "STEP_ERROR",
            f"Error in step {step['name']}: {str(e)}",
            "ERROR",
            e
        )
        logger.error(f"Error in step {step['name']}: {traceback.format_exc()}")
        return False

def run_sync_step(step):
    """Run a synchronous pipeline step"""
    try:
        logger.info(f"Starting step: {step['name']}")
        
        # Import and run the module
        __import__(step['module'])
        
        logger.info(f"Completed step: {step['name']}")
        
        # Clean up to release memory
        sys.modules.pop(step['module'], None)
        gc.collect()
        
        return True
    except Exception as e:
        logger.error(f"Error in step {step['name']}: {str(e)}")
        logger.error(traceback.format_exc())
        return False

async def process_content_chunk(pool, chunk_id):
    """Process a single content chunk through the entire pipeline"""
    try:
        await log_event(pool, "CHUNK_PROCESSING_START", f"Starting processing for chunk ID: {chunk_id}")
        
        # Run each enabled pipeline step for this chunk
        for step in PIPELINE_STEPS:
            if not step['enabled']:
                continue
                
            await log_event(pool, "CHUNK_STEP_START", f"Starting {step['name']} for chunk ID: {chunk_id}")
            
            if step['async']:
                success = await run_async_step(pool, step)
            else:
                success = run_sync_step(step)
                
            if not success:
                await log_event(
                    pool, 
                    "CHUNK_STEP_FAILED", 
                    f"Step {step['name']} failed for chunk ID: {chunk_id}",
                    "ERROR"
                )
                return False
                
            await log_event(pool, "CHUNK_STEP_COMPLETE", f"Completed {step['name']} for chunk ID: {chunk_id}")
            
            # Force garbage collection after each step to release memory
            gc.collect()
            
        await log_event(pool, "CHUNK_PROCESSING_COMPLETE", f"Completed processing for chunk ID: {chunk_id}")
        return True
        
    except Exception as e:
        await log_event(
            pool,
            "CHUNK_PROCESSING_ERROR",
            f"Error processing chunk ID {chunk_id}: {str(e)}",
            "ERROR",
            e
        )
        return False

async def get_pending_chunks(pool):
    """Get chunks that need processing"""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT id FROM content_chunk 
                    WHERE processing_status = 'pending' OR processing_status IS NULL
                    ORDER BY id
                """)
                results = await cursor.fetchall()
                return [row[0] for row in results]
    except Exception as e:
        logger.error(f"Error fetching pending chunks: {e}")
        return []

async def update_chunk_status(pool, chunk_id, status, error_message=None):
    """Update the processing status of a chunk"""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                if error_message:
                    await cursor.execute(
                        "UPDATE content_chunk SET processing_status = %s, error_message = %s WHERE id = %s",
                        (status, error_message, chunk_id)
                    )
                else:
                    await cursor.execute(
                        "UPDATE content_chunk SET processing_status = %s WHERE id = %s",
                        (status, chunk_id)
                    )
                await conn.commit()
    except Exception as e:
        logger.error(f"Error updating chunk status: {e}")

async def run_full_pipeline():
    """Run the full pipeline for all pending content chunks"""
    pool = None
    try:
        # Create database connection pool
        pool = await create_db_pool()
        
        await log_event(pool, "PIPELINE_START", "Starting media generation pipeline")
        
        # Get pending chunks
        pending_chunks = await get_pending_chunks(pool)
        
        if not pending_chunks:
            await log_event(pool, "NO_PENDING_CHUNKS", "No pending chunks found")
            return
            
        await log_event(pool, "PENDING_CHUNKS", f"Found {len(pending_chunks)} pending chunks")
        
        # Process each chunk
        for chunk_id in pending_chunks:
            try:
                # Update status to processing
                await update_chunk_status(pool, chunk_id, "processing")
                
                # Process the chunk
                success = await process_content_chunk(pool, chunk_id)
                
                # Update status based on result
                if success:
                    await update_chunk_status(pool, chunk_id, "completed")
                else:
                    await update_chunk_status(pool, chunk_id, "failed", "Failed to process chunk")
                    
                # Force garbage collection after each chunk
                gc.collect()
                
            except Exception as e:
                await log_event(
                    pool,
                    "CHUNK_ERROR",
                    f"Error processing chunk ID {chunk_id}: {str(e)}",
                    "ERROR",
                    e
                )
                await update_chunk_status(pool, chunk_id, "failed", str(e))
                
        await log_event(pool, "PIPELINE_COMPLETE", "Media generation pipeline completed")
        
    except Exception as e:
        if pool:
            await log_event(
                pool,
                "PIPELINE_ERROR",
                f"Error in pipeline: {str(e)}",
                "ERROR",
                e
            )
        logger.error(f"Error in pipeline: {traceback.format_exc()}")
    finally:
        if pool:
            pool.close()
            await pool.wait_closed()

async def run_single_step(step_name):
    """Run a single step of the pipeline"""
    pool = None
    try:
        # Create database connection pool
        pool = await create_db_pool()
        
        # Find the requested step
        step = next((s for s in PIPELINE_STEPS if s['name'].lower() == step_name.lower()), None)
        
        if not step:
            await log_event(pool, "STEP_NOT_FOUND", f"Step not found: {step_name}", "ERROR")
            return
            
        await log_event(pool, "SINGLE_STEP_START", f"Running single step: {step['name']}")
        
        if step['async']:
            success = await run_async_step(pool, step)
        else:
            success = run_sync_step(step)
            
        if success:
            await log_event(pool, "SINGLE_STEP_COMPLETE", f"Successfully completed step: {step['name']}")
        else:
            await log_event(pool, "SINGLE_STEP_FAILED", f"Failed to complete step: {step['name']}", "ERROR")
            
    except Exception as e:
        if pool:
            await log_event(
                pool,
                "SINGLE_STEP_ERROR",
                f"Error running step {step_name}: {str(e)}",
                "ERROR",
                e
            )
        logger.error(f"Error running step {step_name}: {traceback.format_exc()}")
    finally:
        if pool:
            pool.close()
            await pool.wait_closed()

def print_usage():
    """Print usage information"""
    print("Media Generation Pipeline Service")
    print("Usage:")
    print("  python media_generation_service.py [command]")
    print("")
    print("Commands:")
    print("  run                 Run the full pipeline")
    print("  step [step_name]    Run a single step")
    print("  list                List available steps")
    print("")
    print("Available steps:")
    for step in PIPELINE_STEPS:
        print(f"  - {step['name']}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print_usage()
        sys.exit(1)
        
    command = sys.argv[1].lower()
    
    if command == "run":
        asyncio.run(run_full_pipeline())
    elif command == "step" and len(sys.argv) >= 3:
        step_name = sys.argv[2]
        asyncio.run(run_single_step(step_name))
    elif command == "list":
        print("Available steps:")
        for step in PIPELINE_STEPS:
            print(f"  - {step['name']}")
    else:
        print_usage()
        sys.exit(1)
