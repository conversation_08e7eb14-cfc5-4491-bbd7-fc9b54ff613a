import React, { useState } from 'react';
import { Button, Form, Alert, ProgressBar } from 'react-bootstrap';
import './ContentUpload.css';

const ContentUpload = ({ onUploadComplete }) => {
    const [file, setFile] = useState(null);
    const [uploading, setUploading] = useState(false);
    const [uploadResult, setUploadResult] = useState(null);
    const [error, setError] = useState(null);

    const handleFileChange = (event) => {
        const selectedFile = event.target.files[0];
        if (selectedFile && !selectedFile.name.endsWith('.csv')) {
            setError('Please select a CSV file');
            setFile(null);
            event.target.value = null;
            return;
        }
        setError(null);
        setFile(selectedFile);
        setUploadResult(null);
    };

    const handleUpload = async () => {
        if (!file) {
            setError('Please select a file first');
            return;
        }

        setUploading(true);
        setError(null);
        setUploadResult(null);

        const formData = new FormData();
        formData.append('file', file);

        try {
            // Use the full URL of your Flask backend
            const response = await fetch('http://localhost:5000/api/upload/content', {
                method: 'POST',
                body: formData,
                // Remove any Content-Type header to let the browser handle it
                headers: {
                    'Accept': 'application/json'
                },
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            setUploadResult({
                message: data.message,
                summary: data.summary,
                details: {
                    processed: data.processed,
                    successful: data.successful,
                    skipped: data.skipped,
                    failed: data.failed
                }
            });

            if (onUploadComplete) {
                onUploadComplete();
            }
        } catch (err) {
            console.error('Upload error:', err);
            setError(err.message);
        } finally {
            setUploading(false);
            setFile(null);
            const fileInput = document.getElementById('csvFileInput');
            if (fileInput) {
                fileInput.value = '';
            }
        }
    };

    return (
        <div className="content-upload-container">
            <h2>Upload Content CSV</h2>
            
            <Form.Group className="mb-3">
                <Form.Label>Select CSV File</Form.Label>
                <Form.Control
                    id="csvFileInput"
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    disabled={uploading}
                />
                <Form.Text className="text-muted">
                    File must be a CSV with columns: scenario, empathetic_advice, practical_advice, thumbnail_prompt
                </Form.Text>
            </Form.Group>

            {error && (
                <Alert variant="danger" className="mb-3">
                    {error}
                </Alert>
            )}

            {uploading && (
                <div className="mb-3">
                    <ProgressBar animated now={100} />
                </div>
            )}

            {uploadResult && (
                <Alert variant="success" className="mb-3">
                    <p>{uploadResult.message}</p>
                    <p>{uploadResult.summary}</p>
                    <ul>
                        <li>Processed: {uploadResult.details.processed}</li>
                        <li>Successful: {uploadResult.details.successful}</li>
                        <li>Skipped: {uploadResult.details.skipped}</li>
                        <li>Failed: {uploadResult.details.failed}</li>
                    </ul>
                </Alert>
            )}

            <Button 
                variant="primary" 
                onClick={handleUpload}
                disabled={!file || uploading}
            >
                {uploading ? 'Uploading...' : 'Upload CSV'}
            </Button>
        </div>
    );
};

export default ContentUpload;
