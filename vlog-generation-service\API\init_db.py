import os
import pymysql
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_init.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def create_tables():
    """Create necessary tables if they don't exist"""
    conn = None
    cursor = None
    
    try:
        logger.info("Connecting to database...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check if category table exists
        cursor.execute("SHOW TABLES LIKE 'category'")
        if not cursor.fetchone():
            logger.info("Creating category table...")
            cursor.execute("""
                CREATE TABLE category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)
            
            # Insert some default categories
            logger.info("Inserting default categories...")
            cursor.execute("""
                INSERT INTO category (name, description) VALUES
                ('General', 'General purpose content'),
                ('Lifestyle', 'Lifestyle related content'),
                ('Health', 'Health and wellness content'),
                ('Technology', 'Technology related content')
            """)
            conn.commit()
            logger.info("Category table created and populated successfully")
        else:
            logger.info("Category table already exists")
        
        # Check if content_prompt table exists
        cursor.execute("SHOW TABLES LIKE 'content_prompt'")
        if not cursor.fetchone():
            logger.info("Creating content_prompt table...")
            cursor.execute("""
                CREATE TABLE content_prompt (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    category_id INT,
                    content_prompt TEXT NOT NULL,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'short',
                    voice_id VARCHAR(100),
                    video_format VARCHAR(50) DEFAULT '1min',
                    multiple_topics BOOLEAN DEFAULT FALSE,
                    num_sentences INT DEFAULT 5,
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES category(id)
                )
            """)
            conn.commit()
            logger.info("Content_prompt table created successfully")
        else:
            logger.info("Content_prompt table already exists")
            
    except Exception as e:
        logger.error(f"Error creating tables: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    create_tables()
    logger.info("Database initialization completed")
