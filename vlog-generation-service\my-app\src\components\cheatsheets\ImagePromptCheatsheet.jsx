import React from 'react';
import { <PERSON>, Table, Row, Col, Badge, Alert } from 'react-bootstrap';
import GrokPromptGenerator from './GrokPromptGenerator';
import './Cheatsheets.css';

const ImagePromptCheatsheet = () => {
  return (
    <div className="cheatsheet-container">
      <GrokPromptGenerator promptType="image" />
      <Card className="mb-4">
        <Card.Header className="bg-dark text-white">
          <h4 className="mb-0">🎨 Prompt Engineering Cheatsheet for AI Image Generation (Scene, Mood & Emotion Focused) – 2025 Edition</h4>
        </Card.Header>
        <Card.Body>
          <p className="lead">
            A refined guide built with 10x iterative logic to help you craft high-quality prompts for generating powerful, emotional, and visually accurate AI-generated images. Ideal for visual storytellers, artists, marketers, and content creators.
          </p>
        </Card.Body>
      </Card>

      {/* Core Formula Section */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🧠 Core Formula: S.M.A.R.T.A.R.T</h5>
        </Card.Header>
        <Card.Body>
          <Alert variant="info">
            <strong>S.M.A.R.T.A.R.T = Subject + Mood + Action + Realism + Time + Atmosphere + Rendering + Tone</strong>
          </Alert>
        </Card.Body>
      </Card>

      {/* Repetition Loop */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🔁 Use This Repetition Loop (10x Method)</h5>
        </Card.Header>
        <Card.Body>
          <ol className="mb-0">
            <li>Start with a basic idea</li>
            <li>Add <strong>Subject</strong></li>
            <li>Layer in <strong>Mood</strong></li>
            <li>Describe the <strong>Scene/Action</strong></li>
            <li>Add <strong>Time/Lighting</strong></li>
            <li>Define the <strong>Style or Render Type</strong></li>
            <li>Add <strong>Camera Details</strong> (if needed)</li>
            <li>Refine with <strong>Emotion or Symbolism</strong></li>
            <li>Ensure <strong>Consistency & Focus</strong></li>
            <li>Expand or simplify depending on result</li>
          </ol>
        </Card.Body>
      </Card>

      {/* Prompt Blueprint */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🧩 Prompt Blueprint</h5>
        </Card.Header>
        <Card.Body>
          <div className="bg-light p-3 rounded">
            <pre className="mb-0">
              "A [subject] is [doing something] in a [setting]. The atmosphere feels [mood/emotion], with [lighting/time of day] and [color palette]. Style: [art style or medium]. Rendered in [resolution or engine]."
            </pre>
          </div>
        </Card.Body>
      </Card>

      {/* Emotion Keywords */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎭 Emotion Keywords</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Emotion</th>
                <th>Descriptive Prompts</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Joy</td>
                <td>golden sunlight, wide smile, playful shadows</td>
              </tr>
              <tr>
                <td>Sadness</td>
                <td>overcast sky, tearful expression, soft blue tones</td>
              </tr>
              <tr>
                <td>Fear</td>
                <td>dim light, harsh shadows, eerie stillness</td>
              </tr>
              <tr>
                <td>Wonder</td>
                <td>glowing orbs, sparkles, starry sky, wide-eyed look</td>
              </tr>
              <tr>
                <td>Peace</td>
                <td>misty sunrise, still water, serene forest</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Scene Starters */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🌇 Scene Starters</h5>
        </Card.Header>
        <Card.Body>
          <ul className="mb-0">
            <li>"A lone traveler walks through a foggy forest at dawn…"</li>
            <li>"Two lovers stare at the stars from a rooftop in Tokyo…"</li>
            <li>"A child reaches for a balloon floating into the stormy sky…"</li>
          </ul>
        </Card.Body>
      </Card>

      {/* Mood & Visual Texture */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🖌 Mood & Visual Texture</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Mood</th>
                <th>Visual Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Nostalgic</td>
                <td>sepia tone, faded colors, retro lighting</td>
              </tr>
              <tr>
                <td>Hopeful</td>
                <td>bright light rays, soft edges, bloom effects</td>
              </tr>
              <tr>
                <td>Melancholic</td>
                <td>rainy window, grayscale palette, lonely setting</td>
              </tr>
              <tr>
                <td>Energetic</td>
                <td>vibrant colors, motion blur, high contrast</td>
              </tr>
              <tr>
                <td>Romantic</td>
                <td>candlelight, soft focus, warm hues</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Camera Effects */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">📷 Camera Effects (Optional Enhancements)</h5>
        </Card.Header>
        <Card.Body>
          <ul className="mb-0">
            <li><strong>Close-up</strong> — focus on emotion/eyes</li>
            <li><strong>Wide shot</strong> — full scene/mood</li>
            <li><strong>Tilted angle</strong> — instability, chaos</li>
            <li><strong>Cinematic ratio (16:9)</strong> — epic feel</li>
            <li><strong>Depth of field</strong> — realism, focus</li>
          </ul>
        </Card.Body>
      </Card>

      {/* Final Examples */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🛠 Final Examples</h5>
        </Card.Header>
        <Card.Body>
          <ol>
            <li className="mb-2">
              <strong>"A girl in a red dress dancing alone on a rainy street at night. Neon lights reflect off the puddles. Mood: nostalgic. Cinematic lighting. Style: digital painting."</strong>
            </li>
            <li className="mb-2">
              <strong>"An astronaut staring at Earth from a broken spaceship. The feeling is isolation and awe. Render in photorealism, with cold blue lighting."</strong>
            </li>
            <li>
              <strong>"A fox in a snowy forest during twilight. Peaceful mood. Pastel colors. Style: watercolor."</strong>
            </li>
          </ol>
        </Card.Body>
      </Card>

      {/* Tips */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">💡 Tips</h5>
        </Card.Header>
        <Card.Body>
          <ul className="mb-0">
            <li>Be specific with <strong>scene composition</strong></li>
            <li>Choose <strong>color palette</strong> to match emotion</li>
            <li>Always test and refine prompt after first output</li>
            <li>You can stack <strong>multiple moods</strong>: "hopeful yet lonely"</li>
          </ul>
        </Card.Body>
      </Card>

      {/* Optimized For */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">👨‍🎨 Optimized For</h5>
        </Card.Header>
        <Card.Body>
          <p className="mb-0">
            MidJourney, DALL·E, Firefly, Leonardo AI, Stable Diffusion, and any advanced AI image model.
          </p>
        </Card.Body>
      </Card>

      {/* Credits */}
      <Card className="mb-4">
        <Card.Footer className="text-center">
          <p className="mb-0">
            <small>
              Created by <strong>PromptGPT AI Repetition Framework™</strong>
            </small>
          </p>
        </Card.Footer>
      </Card>
    </div>
  );
};

export default ImagePromptCheatsheet;
