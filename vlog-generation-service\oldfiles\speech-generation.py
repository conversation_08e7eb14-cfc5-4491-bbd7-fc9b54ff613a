import os
import asyncio
import aiomysql
import logging
from datetime import datetime
import traceback
from elevenlabs import generate, Voice, VoiceSettings,api_base_url_v1
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='speech_generation.log'
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'db': os.getenv('DB_DATABASE'),
    'autocommit': True
}

# API Keys
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")

async def log_event(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def save_speech_content(pool, content_id: int, file_path: str, duration: float):
    """Save speech generation details to database"""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO speech_content (content_id, file_path, duration, created_at) "
                    "VALUES (%s, %s, %s, NOW())",
                    (content_id, file_path, duration)
                )
                return cursor.lastrowid
    except Exception as e:
        await log_event(pool, "SPEECH_SAVE_ERROR", "Failed to save speech content", "ERROR", e)
        raise

async def generate_speech(pool, content_id: int, text: str, save_directory: str = "content-speech"):
    """
    Generate speech from text and save it to file system and database
    
    Args:
        pool: Database connection pool
        content_id: ID of the content to generate speech for
        text: Text to convert to speech
        save_directory: Directory to save audio files
    """
    try:
        # Ensure save directory exists
        os.makedirs(save_directory, exist_ok=True)
        
        await log_event(pool, "SPEECH_GENERATION_START", f"Starting speech generation for content ID: {content_id}")
        
        # Generate filename
        filename = f"speech_{content_id}.mp3"
        file_path = os.path.join(save_directory, filename)
        
        # Generate speech
        audio = generate(
            api_key=ELEVENLABS_API_KEY,
            text=text,
            voice="George",
            model="eleven_monolingual_v1"
        )
        
        # Save audio file
        with open(file_path, 'wb') as f:
            f.write(audio)
        
        # Get audio duration (placeholder - you might want to use a proper audio library to get actual duration)
        duration = len(text) / 15  # Rough estimation: 15 characters per second
        
        # Save to database
        speech_id = await save_speech_content(pool, content_id, file_path, duration)
        
        await log_event(
            pool,
            "SPEECH_GENERATION_SUCCESS",
            f"Speech generated and saved with ID: {speech_id}"
        )
        
        return {
            'speech_id': speech_id,
            'file_path': file_path,
            'duration': duration
        }
        
    except Exception as e:
        await log_event(
            pool,
            "SPEECH_GENERATION_ERROR",
            f"Failed to generate speech for content ID: {content_id}",
            "ERROR",
            e
        )
        raise

async def main():
    """Main function to test speech generation"""
    pool = await aiomysql.create_pool(**DB_CONFIG)
    try:
        # Example usage
        test_text = "The first move is what sets everything in motion."
        result = await generate_speech(pool, content_id=1, text=test_text)
        print(f"Speech generated successfully: {result}")
        
    except Exception as e:
        logger.error("Main execution failed", exc_info=e)
    finally:
        pool.close()
        await pool.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())
