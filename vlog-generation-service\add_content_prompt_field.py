import sqlite3
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    for column in columns:
        if column[1] == column_name:
            return True
    return False

def add_content_prompt_field():
    """Add content_prompt field to content_prompt_claude table."""
    db_path = 'vlog_content.db'
    
    # Check if database file exists
    if not os.path.exists(db_path):
        logger.error(f"Database file {db_path} does not exist.")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if content_prompt_claude table exists
        if not check_table_exists(cursor, 'content_prompt_claude'):
            logger.error("content_prompt_claude table does not exist.")
            return False
        
        # Check if content_prompt column already exists
        if check_column_exists(cursor, 'content_prompt_claude', 'content_prompt'):
            logger.info("content_prompt column already exists in content_prompt_claude table.")
            return True
        
        # Add content_prompt column to content_prompt_claude table
        logger.info("Adding content_prompt column to content_prompt_claude table...")
        cursor.execute("ALTER TABLE content_prompt_claude ADD COLUMN content_prompt TEXT")
        conn.commit()
        logger.info("content_prompt column added successfully!")
        
        # Verify the column was added
        if check_column_exists(cursor, 'content_prompt_claude', 'content_prompt'):
            logger.info("Verified content_prompt column exists in content_prompt_claude table.")
            return True
        else:
            logger.error("Failed to add content_prompt column to content_prompt_claude table.")
            return False
        
    except Exception as e:
        logger.error(f"Error adding content_prompt field: {str(e)}")
        return False
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def main():
    """Main function."""
    logger.info("Starting script to add content_prompt field to content_prompt_claude table...")
    
    # Print current working directory for debugging
    logger.info(f"Current working directory: {os.getcwd()}")
    
    # Add content_prompt field
    success = add_content_prompt_field()
    
    if success:
        logger.info("Script completed successfully!")
    else:
        logger.error("Script failed to complete successfully.")
        sys.exit(1)

if __name__ == "__main__":
    main()
