from typing import List, Literal, Optional

import httpx
import ormsgpack
from pydantic import BaseModel, conint
from dotenv import load_dotenv
import os
import asyncio
import logging
from datetime import datetime
import aiomysql

load_dotenv()
FISHAUDIO_API_KEY = os.getenv("FISHAUDIO_API_KEY")
FISHAUDIO_OUTPUT= os.getenv("FISHAUDIO_OUTPUT")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReferenceAudio(BaseModel):
    audio: bytes
    text: str

class ProsodySettings(BaseModel):
    speed: float  # Speed range: 0.5 (slow) to 2.0 (fast), default is 1.0

class TTSRequest(BaseModel):
    text: str
    chunk_length: conint(ge=100, le=300) = 200
    format: Literal["wav", "pcm", "mp3"] = "mp3"
    mp3_bitrate: Literal[64, 128, 192] = 128
    references: List[ReferenceAudio] = []
    reference_id: Optional[str] = None  # Model selection
    normalize: bool = True
    latency: Literal["normal", "balanced"] = "normal"
    prosody: ProsodySettings  # Controls speed

async def text_to_speech(
    api_key: str,
    text: str,
    output_file: str,
    reference_audio_path: str = None,
    reference_text: str = None,
    speed: float = 1.0,  # Default speed is 1.0
    reference_id: str = None  # Allows selecting a model
):
    # Concatenate folder and filename
    folder=FISHAUDIO_OUTPUT
    loc_output = os.path.join(folder, output_file)
    headers = {
        "authorization": f"Bearer {api_key}",
        "content-type": "application/msgpack",
        "developer-id": "1c9a7c8cf51f458ab298c48e81b29bfc",
    }

    references = []
    if reference_audio_path and reference_text:
        with open(reference_audio_path, "rb") as audio_file:
            audio_data = audio_file.read()
            references.append(ReferenceAudio(audio=audio_data, text=reference_text))

    tts_request = TTSRequest(
        text=text,
        references=references,
        reference_id=reference_id,  # Use selected model
        prosody=ProsodySettings(speed=speed)  # Set speech speed
    )

    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "https://api.fish.audio/v1/tts",
            content=ormsgpack.packb(tts_request.model_dump(), option=ormsgpack.OPT_SERIALIZE_PYDANTIC),
            headers=headers,
            timeout=None,
        ) as response:
            response.raise_for_status()
            
            with open(loc_output, "wb") as f:
                async for chunk in response.aiter_bytes():
                    f.write(chunk)

async def log_event(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def main():
    # Database configuration
    DB_CONFIG = {
        'host': os.getenv('DB_HOST'),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'db': os.getenv('DB_DATABASE'),
    }

    # Create connection pool
    pool = await aiomysql.create_pool(**DB_CONFIG)

    try:
        await log_event(pool, "SPEECH_GENERATION_START", "Starting batch speech generation process")
        
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Select records without audio files
                await cursor.execute("""
                    SELECT id, scenario, empathetic_advice, practical_advice 
                    FROM generated_content 
                    WHERE audio_filename IS NULL
                """)
                records = await cursor.fetchall()
                
                await log_event(
                    pool,
                    "RECORDS_FOUND",
                    f"Found {len(records)} records requiring speech generation"
                )

                for record in records:
                    content_id, scenario, empathetic_advice, practical_advice = record
                    
                    # Concatenate scenario and empathetic advice
                    text = f"{scenario}\n{empathetic_advice}\n{practical_advice}"
                    
                    # Generate unique filename using content ID
                    output_file = f"{content_id}.mp3"
                    folder = FISHAUDIO_OUTPUT
                    loc_output = os.path.join(folder, output_file)
                    await log_event(pool, "SPEECH_GENERATION_START", loc_output)
                    try:
                        await log_event(
                            pool,
                            "SPEECH_GENERATION_ATTEMPT",
                            f"Attempting speech generation for content ID: {content_id}"
                        )
                        
                        # Generate speech
                        await text_to_speech(
                            api_key=FISHAUDIO_API_KEY,
                            text=text,
                            output_file=output_file,
                            reference_audio_path="",
                            reference_text="",
                            speed=0.85,
                            reference_id="d8c7af3779494549a975bdb8015e8a8b"
                        )
                        
                        # Update the database record with the audio file path
                        await cursor.execute("""
                            UPDATE generated_content 
                            SET audio_filename = %s 
                            WHERE id = %s
                        """, (loc_output, content_id))
                        await conn.commit()  # Added explicit commit
                        
        
                        await log_event(
                            pool,
                            "SPEECH_GENERATION_SUCCESS",
                            f"Successfully generated audio for content ID: {content_id}"
                        )

                    except Exception as e:
                        await log_event(
                            pool,
                            "SPEECH_GENERATION_ERROR",
                            f"Failed to generate speech for content ID: {content_id}",
                            "ERROR",
                            e
                        )
                        continue

        await log_event(pool, "SPEECH_GENERATION_COMPLETE", "Batch speech generation process completed")

    except Exception as e:
        await log_event(
            pool,
            "SPEECH_GENERATION_FATAL_ERROR",
            "Fatal error in speech generation process",
            "ERROR",
            e
        )
    finally:
        pool.close()
        await pool.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())
