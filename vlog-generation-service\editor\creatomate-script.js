// VisionFrame AI Professional Video Editor - Creatomate Style

class CreatomateEditor {
    constructor() {
        this.elements = [];
        this.selectedElement = null;
        this.currentTime = 0;
        this.isPlaying = false;
        this.duration = 10;
        this.animationFrame = null;
        this.canvasZoom = 1;
        this.timelineZoom = 1;
        this.canvasWidth = 1920;
        this.canvasHeight = 1080;
        this.mediaLibrary = [];
        this.undoStack = [];
        this.redoStack = [];
        this.activeTab = 'elements';
        this.gridVisible = false;
        this.snapEnabled = true;

        // Drag states
        this.dragState = {
            isDragging: false,
            element: null,
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0
        };

        this.timelineDragState = {
            isDragging: false,
            element: null,
            startTime: 0,
            isResizing: false,
            resizeHandle: null
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCanvasSize();
        this.renderCanvas();
        this.renderLayerNavigator();
        this.renderTimeline();
        this.updatePropertiesPanel();
    }

    setupEventListeners() {
        // Top toolbar
        document.getElementById('undoBtn').addEventListener('click', () => this.undo());
        document.getElementById('redoBtn').addEventListener('click', () => this.redo());
        document.getElementById('previewBtn').addEventListener('click', () => this.togglePlayback());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportProject());

        // Canvas controls
        document.getElementById('zoomOut').addEventListener('click', () => this.adjustCanvasZoom(-0.1));
        document.getElementById('zoomIn').addEventListener('click', () => this.adjustCanvasZoom(0.1));
        document.getElementById('zoomFit').addEventListener('click', () => this.fitCanvasToView());
        document.getElementById('gridToggle').addEventListener('click', () => this.toggleGrid());
        document.getElementById('snapToggle').addEventListener('click', () => this.toggleSnap());
        document.getElementById('resolutionSelect').addEventListener('change', (e) => this.changeResolution(e.target.value));

        // Tab switching
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Element buttons
        document.querySelectorAll('.element-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.addElement(e.target.closest('.element-btn').dataset.type));
        });

        // Animation presets
        document.querySelectorAll('.animation-preset').forEach(btn => {
            btn.addEventListener('click', (e) => this.applyAnimationPreset(e.target.dataset.preset));
        });

        // File upload
        document.getElementById('dropZone').addEventListener('click', () => document.getElementById('fileInput').click());
        document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileUpload(e));
        this.setupDragAndDrop();

        // Timeline controls
        document.getElementById('timelinePlayBtn').addEventListener('click', () => this.togglePlayback());
        document.getElementById('timelineZoomOut').addEventListener('click', () => this.adjustTimelineZoom(-0.5));
        document.getElementById('timelineZoomIn').addEventListener('click', () => this.adjustTimelineZoom(0.5));
        document.getElementById('currentTimeInput').addEventListener('change', (e) => this.setCurrentTime(e.target.value));
        document.getElementById('durationInput').addEventListener('change', (e) => this.setDuration(e.target.value));

        // Modal handlers
        this.setupModalHandlers();

        // Canvas interaction
        this.setupCanvasInteraction();

        // Property panel updates
        this.setupPropertyPanelHandlers();
    }

    setupModalHandlers() {
        // Text modal
        document.getElementById('cancelText').addEventListener('click', () => this.hideModal('textModal'));
        document.getElementById('confirmText').addEventListener('click', () => this.addTextElement());

        // Shape modal
        document.getElementById('cancelShape').addEventListener('click', () => this.hideModal('shapeModal'));
        document.getElementById('confirmShape').addEventListener('click', () => this.addShapeElement());
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.tab === tabName) {
                tab.classList.add('active');
            }
        });

        // Show/hide tab content
        document.getElementById('elementsTab').classList.toggle('hidden', tabName !== 'elements');
        document.getElementById('mediaTab').classList.toggle('hidden', tabName !== 'media');
        document.getElementById('animationsTab').classList.toggle('hidden', tabName !== 'animations');

        this.activeTab = tabName;
    }

    addElement(type) {
        if (type === 'text') {
            this.showModal('textModal');
        } else if (type === 'shape') {
            this.showModal('shapeModal');
        } else if (type === 'image' || type === 'video' || type === 'audio') {
            // For now, trigger file upload
            document.getElementById('fileInput').click();
        }
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
        document.getElementById(modalId).classList.add('flex');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
        document.getElementById(modalId).classList.remove('flex');
    }

    addTextElement() {
        const text = document.getElementById('textContent').value;
        const fontSize = parseInt(document.getElementById('textFontSize').value);
        const color = document.getElementById('textColor').value;

        if (!text.trim()) {
            alert('Please enter text content');
            return;
        }

        const element = {
            id: this.generateId(),
            type: 'text',
            text: text,
            fontSize: fontSize,
            color: color,
            x: this.canvasWidth / 2,
            y: this.canvasHeight / 2,
            width: 200,
            height: 50,
            time: this.currentTime,
            duration: 3,
            opacity: 100,
            rotation: 0,
            scaleX: 100,
            scaleY: 100,
            anchorX: 50,
            anchorY: 50,
            animations: []
        };

        this.elements.push(element);
        this.hideModal('textModal');
        this.clearTextModal();
        this.renderLayerNavigator();
        this.renderCanvas();
        this.renderTimeline();
        this.selectElement(element);
    }

    addShapeElement() {
        const shapeType = document.getElementById('shapeType').value;
        const width = parseInt(document.getElementById('shapeWidth').value);
        const height = parseInt(document.getElementById('shapeHeight').value);
        const fillColor = document.getElementById('shapeFillColor').value;

        const element = {
            id: this.generateId(),
            type: 'shape',
            shapeType: shapeType,
            fillColor: fillColor,
            x: this.canvasWidth / 2,
            y: this.canvasHeight / 2,
            width: width,
            height: height,
            time: this.currentTime,
            duration: 3,
            opacity: 100,
            rotation: 0,
            scaleX: 100,
            scaleY: 100,
            anchorX: 50,
            anchorY: 50,
            animations: []
        };

        this.elements.push(element);
        this.hideModal('shapeModal');
        this.clearShapeModal();
        this.renderLayerNavigator();
        this.renderCanvas();
        this.renderTimeline();
        this.selectElement(element);
    }

    clearTextModal() {
        document.getElementById('textContent').value = '';
        document.getElementById('textFontSize').value = '24';
        document.getElementById('textColor').value = '#ffffff';
    }

    clearShapeModal() {
        document.getElementById('shapeType').value = 'rectangle';
        document.getElementById('shapeWidth').value = '200';
        document.getElementById('shapeHeight').value = '200';
        document.getElementById('shapeFillColor').value = '#3b82f6';
    }

    generateId() {
        return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    updateCanvasSize() {
        const canvas = document.getElementById('canvas');
        const container = document.getElementById('canvasContainer');

        // Calculate scale to fit canvas in container
        const containerRect = container.getBoundingClientRect();
        const maxWidth = containerRect.width - 40;
        const maxHeight = containerRect.height - 40;

        const scaleX = maxWidth / this.canvasWidth;
        const scaleY = maxHeight / this.canvasHeight;
        const scale = Math.min(scaleX, scaleY, 1) * this.canvasZoom;

        const displayWidth = this.canvasWidth * scale;
        const displayHeight = this.canvasHeight * scale;

        canvas.style.width = displayWidth + 'px';
        canvas.style.height = displayHeight + 'px';

        // Update zoom level display
        document.getElementById('zoomLevel').textContent = Math.round(scale * 100) + '%';
    }

    renderCanvas() {
        const canvas = document.getElementById('canvasElements');
        canvas.innerHTML = '';

        this.elements.forEach(element => {
            if (this.isElementVisible(element)) {
                const elementDiv = this.createCanvasElement(element);
                canvas.appendChild(elementDiv);
            }
        });

        this.updateSelectionBox();
    }

    isElementVisible(element) {
        const elementStart = element.time || 0;
        const elementEnd = elementStart + (element.duration || this.duration);
        return this.currentTime >= elementStart && this.currentTime <= elementEnd;
    }

    createCanvasElement(element) {
        const div = document.createElement('div');
        div.className = 'canvas-element ' + element.type + '-element';
        div.dataset.elementId = element.id;

        if (this.selectedElement && this.selectedElement.id === element.id) {
            div.classList.add('selected');
        }

        // Apply transform and positioning
        const scaleX = (element.scaleX || 100) / 100;
        const scaleY = (element.scaleY || 100) / 100;
        const rotation = element.rotation || 0;
        const opacity = (element.opacity || 100) / 100;

        div.style.left = (element.x - element.width / 2) + 'px';
        div.style.top = (element.y - element.height / 2) + 'px';
        div.style.width = element.width + 'px';
        div.style.height = element.height + 'px';
        div.style.transform = `scale(${scaleX}, ${scaleY}) rotate(${rotation}deg)`;
        div.style.opacity = opacity;

        // Element-specific rendering
        if (element.type === 'text') {
            div.textContent = element.text;
            div.style.fontSize = element.fontSize + 'px';
            div.style.color = element.color;
        } else if (element.type === 'shape') {
            this.renderShape(div, element);
        }

        div.addEventListener('click', () => this.selectElement(element));

        return div;
    }

    renderShape(div, element) {
        div.style.backgroundColor = element.fillColor;

        if (element.shapeType === 'circle') {
            div.style.borderRadius = '50%';
        } else if (element.shapeType === 'triangle') {
            div.style.backgroundColor = 'transparent';
            div.style.width = '0';
            div.style.height = '0';
            div.style.borderLeft = (element.width / 2) + 'px solid transparent';
            div.style.borderRight = (element.width / 2) + 'px solid transparent';
            div.style.borderBottom = element.height + 'px solid ' + element.fillColor;
        }
    }

    selectElement(element) {
        this.selectedElement = element;
        this.renderCanvas();
        this.renderLayerNavigator();
        this.updatePropertiesPanel();
        this.updateSelectionBox();
    }

    updateSelectionBox() {
        const selectionBox = document.getElementById('selectionBox');

        if (!this.selectedElement || !this.isElementVisible(this.selectedElement)) {
            selectionBox.classList.add('hidden');
            return;
        }

        const element = this.selectedElement;
        const left = element.x - element.width / 2;
        const top = element.y - element.height / 2;

        selectionBox.style.left = left + 'px';
        selectionBox.style.top = top + 'px';
        selectionBox.style.width = element.width + 'px';
        selectionBox.style.height = element.height + 'px';
        selectionBox.classList.remove('hidden');
    }

    renderLayerNavigator() {
        const container = document.getElementById('layerNavigator');
        container.innerHTML = '';

        this.elements.forEach((element, index) => {
            const div = document.createElement('div');
            div.className = 'layer-item';
            div.dataset.elementId = element.id;

            if (this.selectedElement && this.selectedElement.id === element.id) {
                div.classList.add('selected');
            }

            const iconClass = this.getElementIcon(element.type);
            const name = this.getElementName(element);

            div.innerHTML = `
                <div class="layer-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="layer-name">${name}</div>
                <div class="layer-visibility" onclick="editor.toggleElementVisibility('${element.id}')">
                    <i class="fas fa-eye"></i>
                </div>
            `;

            div.addEventListener('click', (e) => {
                if (!e.target.closest('.layer-visibility')) {
                    this.selectElement(element);
                }
            });

            container.appendChild(div);
        });
    }

    getElementIcon(type) {
        const icons = {
            text: 'fas fa-font text-blue-400',
            image: 'fas fa-image text-green-400',
            video: 'fas fa-video text-purple-400',
            audio: 'fas fa-music text-orange-400',
            shape: 'fas fa-shapes text-red-400'
        };
        return icons[type] || 'fas fa-square';
    }

    getElementName(element) {
        if (element.type === 'text') {
            return element.text.substring(0, 20) + (element.text.length > 20 ? '...' : '');
        } else if (element.type === 'shape') {
            return element.shapeType.charAt(0).toUpperCase() + element.shapeType.slice(1);
        } else {
            return element.fileName || element.type.charAt(0).toUpperCase() + element.type.slice(1);
        }
    }

    updatePropertiesPanel() {
        const noSelection = document.getElementById('noSelection');
        const elementProperties = document.getElementById('elementProperties');

        if (!this.selectedElement) {
            noSelection.classList.remove('hidden');
            elementProperties.classList.add('hidden');
            return;
        }

        noSelection.classList.add('hidden');
        elementProperties.classList.remove('hidden');

        // Update property values
        this.updatePropertyInputs();
        this.updateSpecificProperties();
    }

    updatePropertyInputs() {
        const element = this.selectedElement;

        document.getElementById('propTime').value = element.time || 0;
        document.getElementById('propDuration').value = element.duration || 3;
        document.getElementById('propX').value = element.x || 0;
        document.getElementById('propY').value = element.y || 0;
        document.getElementById('propWidth').value = element.width || 100;
        document.getElementById('propHeight').value = element.height || 100;
        document.getElementById('propAnchorX').value = element.anchorX || 50;
        document.getElementById('propAnchorY').value = element.anchorY || 50;
        document.getElementById('propScaleX').value = element.scaleX || 100;
        document.getElementById('propScaleY').value = element.scaleY || 100;
        document.getElementById('propRotation').value = element.rotation || 0;
        document.getElementById('propOpacity').value = element.opacity || 100;

        // Update opacity display
        document.querySelector('#propOpacity + .property-value').textContent = (element.opacity || 100) + '%';
    }

    updateSpecificProperties() {
        const container = document.getElementById('specificProperties');
        const element = this.selectedElement;

        if (element.type === 'text') {
            container.innerHTML = `
                <div class="property-section">
                    <h4 class="property-section-title">
                        <i class="fas fa-font mr-2"></i>Text
                    </h4>
                    <div class="property-section-content">
                        <div class="property-row">
                            <label>Text</label>
                            <input type="text" value="${element.text}" onchange="editor.updateElementProperty('text', this.value)" class="property-input">
                        </div>
                        <div class="property-row">
                            <label>Font Size</label>
                            <input type="number" value="${element.fontSize}" onchange="editor.updateElementProperty('fontSize', parseInt(this.value))" class="property-input">
                        </div>
                        <div class="property-row">
                            <label>Color</label>
                            <input type="color" value="${element.color}" onchange="editor.updateElementProperty('color', this.value)" class="property-input">
                        </div>
                    </div>
                </div>
            `;
        } else if (element.type === 'shape') {
            container.innerHTML = `
                <div class="property-section">
                    <h4 class="property-section-title">
                        <i class="fas fa-shapes mr-2"></i>Shape
                    </h4>
                    <div class="property-section-content">
                        <div class="property-row">
                            <label>Type</label>
                            <select onchange="editor.updateElementProperty('shapeType', this.value)" class="property-input">
                                <option value="rectangle" ${element.shapeType === 'rectangle' ? 'selected' : ''}>Rectangle</option>
                                <option value="circle" ${element.shapeType === 'circle' ? 'selected' : ''}>Circle</option>
                                <option value="triangle" ${element.shapeType === 'triangle' ? 'selected' : ''}>Triangle</option>
                            </select>
                        </div>
                        <div class="property-row">
                            <label>Fill Color</label>
                            <input type="color" value="${element.fillColor}" onchange="editor.updateElementProperty('fillColor', this.value)" class="property-input">
                        </div>
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = '';
        }
    }

    updateElementProperty(property, value) {
        if (this.selectedElement) {
            this.selectedElement[property] = value;
            this.renderCanvas();
            this.renderLayerNavigator();
            this.renderTimeline();
        }
    }

    setupPropertyPanelHandlers() {
        // Add event listeners for property inputs
        const propertyInputs = [
            'propTime', 'propDuration', 'propX', 'propY', 'propWidth', 'propHeight',
            'propAnchorX', 'propAnchorY', 'propScaleX', 'propScaleY', 'propRotation'
        ];

        propertyInputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('change', (e) => {
                    const property = id.replace('prop', '').toLowerCase();
                    const value = parseFloat(e.target.value) || 0;
                    this.updateElementProperty(property, value);
                });
            }
        });

        // Opacity slider
        document.getElementById('propOpacity').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            this.updateElementProperty('opacity', value);
            document.querySelector('#propOpacity + .property-value').textContent = value + '%';
        });
    }

    setupCanvasInteraction() {
        const canvas = document.getElementById('canvasElements');

        canvas.addEventListener('mousedown', (e) => this.handleCanvasMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handleCanvasMouseMove(e));
        document.addEventListener('mouseup', (e) => this.handleCanvasMouseUp(e));
    }

    handleCanvasMouseDown(e) {
        const elementDiv = e.target.closest('.canvas-element');
        if (!elementDiv) {
            this.selectedElement = null;
            this.updatePropertiesPanel();
            this.updateSelectionBox();
            return;
        }

        const elementId = elementDiv.dataset.elementId;
        const element = this.elements.find(el => el.id === elementId);
        if (element) {
            this.selectElement(element);

            // Start drag
            this.dragState = {
                isDragging: true,
                element: element,
                startX: e.clientX,
                startY: e.clientY,
                offsetX: e.clientX - element.x,
                offsetY: e.clientY - element.y
            };
        }
    }

    handleCanvasMouseMove(e) {
        if (!this.dragState.isDragging) return;

        const deltaX = e.clientX - this.dragState.startX;
        const deltaY = e.clientY - this.dragState.startY;

        this.dragState.element.x = this.dragState.startX - this.dragState.offsetX + deltaX;
        this.dragState.element.y = this.dragState.startY - this.dragState.offsetY + deltaY;

        this.renderCanvas();
        this.updatePropertyInputs();
    }

    handleCanvasMouseUp(e) {
        this.dragState.isDragging = false;
    }

    setupDragAndDrop() {
        const dropZone = document.getElementById('dropZone');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            this.handleFileUpload(e);
        });
    }

    handleFileUpload(e) {
        const files = e.target.files || e.dataTransfer.files;
        if (!files.length) return;

        Array.from(files).forEach(file => {
            const url = URL.createObjectURL(file);
            this.mediaLibrary.push({
                id: this.generateId(),
                name: file.name,
                url: url,
                type: file.type.split('/')[0]
            });
        });

        this.renderMediaLibrary();
    }

    renderMediaLibrary() {
        const container = document.getElementById('mediaLibrary');
        container.innerHTML = '';

        this.mediaLibrary.forEach(item => {
            const div = document.createElement('div');
            div.className = 'media-item';

            if (item.type === 'image') {
                div.innerHTML = `
                    <img src="${item.url}" alt="${item.name}">
                    <div class="media-overlay">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                `;
            } else {
                div.innerHTML = `
                    <div class="bg-gray-600 w-full h-full flex items-center justify-center">
                        <i class="fas fa-file text-gray-400 text-2xl"></i>
                    </div>
                    <div class="media-overlay">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                `;
            }

            div.addEventListener('click', () => this.addMediaToCanvas(item));
            container.appendChild(div);
        });
    }

    addMediaToCanvas(mediaItem) {
        const element = {
            id: this.generateId(),
            type: mediaItem.type,
            src: mediaItem.url,
            fileName: mediaItem.name,
            x: this.canvasWidth / 2,
            y: this.canvasHeight / 2,
            width: 200,
            height: 150,
            time: this.currentTime,
            duration: 3,
            opacity: 100,
            rotation: 0,
            scaleX: 100,
            scaleY: 100,
            anchorX: 50,
            anchorY: 50,
            animations: []
        };

        this.elements.push(element);
        this.renderLayerNavigator();
        this.renderCanvas();
        this.renderTimeline();
        this.selectElement(element);
    }

    renderTimeline() {
        this.renderTimelineRuler();
        this.renderTimelineTracks();
        this.updateTimelinePlayhead();
    }

    renderTimelineRuler() {
        const ruler = document.getElementById('timelineRuler');
        ruler.innerHTML = '';

        const pixelsPerSecond = 100 * this.timelineZoom;
        const totalWidth = this.duration * pixelsPerSecond;

        for (let i = 0; i <= this.duration; i++) {
            const mark = document.createElement('div');
            mark.className = 'timeline-ruler-mark';
            if (i % 5 === 0) mark.classList.add('major');
            mark.style.left = (i * pixelsPerSecond) + 'px';
            ruler.appendChild(mark);

            if (i % 5 === 0) {
                const label = document.createElement('div');
                label.className = 'timeline-ruler-label';
                label.textContent = i + 's';
                label.style.left = (i * pixelsPerSecond) + 'px';
                ruler.appendChild(label);
            }
        }

        ruler.style.width = totalWidth + 'px';
    }

    renderTimelineTracks() {
        const tracksContainer = document.getElementById('timelineTracks');
        const labelsContainer = document.getElementById('timelineTrackLabels');

        tracksContainer.innerHTML = '';
        labelsContainer.innerHTML = '';

        const pixelsPerSecond = 100 * this.timelineZoom;

        this.elements.forEach((element, index) => {
            // Create track label
            const label = document.createElement('div');
            label.className = 'timeline-track-label';
            label.textContent = this.getElementName(element);
            labelsContainer.appendChild(label);

            // Create track
            const track = document.createElement('div');
            track.className = 'timeline-track';

            // Create timeline element
            const timelineElement = document.createElement('div');
            timelineElement.className = `timeline-element ${element.type}-type`;
            timelineElement.dataset.elementId = element.id;

            const startTime = element.time || 0;
            const duration = element.duration || 3;
            const left = startTime * pixelsPerSecond;
            const width = duration * pixelsPerSecond;

            timelineElement.style.left = left + 'px';
            timelineElement.style.width = width + 'px';
            timelineElement.textContent = this.getElementName(element);

            if (this.selectedElement && this.selectedElement.id === element.id) {
                timelineElement.classList.add('selected');
            }

            // Add resize handles
            const leftHandle = document.createElement('div');
            leftHandle.className = 'resize-handle left';
            const rightHandle = document.createElement('div');
            rightHandle.className = 'resize-handle right';

            timelineElement.appendChild(leftHandle);
            timelineElement.appendChild(rightHandle);

            timelineElement.addEventListener('click', () => this.selectElement(element));

            track.appendChild(timelineElement);
            tracksContainer.appendChild(track);
        });

        tracksContainer.style.width = (this.duration * pixelsPerSecond) + 'px';
    }

    updateTimelinePlayhead() {
        const playhead = document.getElementById('timelinePlayhead');
        const pixelsPerSecond = 100 * this.timelineZoom;
        const left = this.currentTime * pixelsPerSecond;
        playhead.style.left = left + 'px';
    }

    togglePlayback() {
        this.isPlaying = !this.isPlaying;
        const playBtn = document.getElementById('timelinePlayBtn');
        const previewBtn = document.getElementById('previewBtn');

        if (this.isPlaying) {
            playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            previewBtn.innerHTML = '<i class="fas fa-pause mr-2"></i>Pause';
            this.startPlayback();
        } else {
            playBtn.innerHTML = '<i class="fas fa-play"></i>';
            previewBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Preview';
            this.stopPlayback();
        }
    }

    startPlayback() {
        const startTime = performance.now();
        const initialTime = this.currentTime;

        const animate = (currentTimestamp) => {
            if (!this.isPlaying) return;

            const elapsed = (currentTimestamp - startTime) / 1000;
            this.currentTime = initialTime + elapsed;

            if (this.currentTime >= this.duration) {
                this.currentTime = this.duration;
                this.isPlaying = false;
                document.getElementById('timelinePlayBtn').innerHTML = '<i class="fas fa-play"></i>';
                document.getElementById('previewBtn').innerHTML = '<i class="fas fa-play mr-2"></i>Preview';
            }

            this.updateTimeDisplay();
            this.renderCanvas();
            this.updateTimelinePlayhead();

            if (this.isPlaying) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };

        this.animationFrame = requestAnimationFrame(animate);
    }

    stopPlayback() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }

    updateTimeDisplay() {
        const minutes = Math.floor(this.currentTime / 60);
        const seconds = (this.currentTime % 60).toFixed(1);
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.padStart(4, '0')}`;
        document.getElementById('currentTimeInput').value = timeString;
    }

    adjustCanvasZoom(delta) {
        this.canvasZoom = Math.max(0.1, Math.min(2, this.canvasZoom + delta));
        this.updateCanvasSize();
    }

    adjustTimelineZoom(delta) {
        this.timelineZoom = Math.max(0.5, Math.min(5, this.timelineZoom + delta));
        this.renderTimeline();
    }

    fitCanvasToView() {
        this.canvasZoom = 1;
        this.updateCanvasSize();
    }

    toggleGrid() {
        this.gridVisible = !this.gridVisible;
        document.getElementById('canvasGrid').classList.toggle('hidden', !this.gridVisible);
        document.getElementById('gridToggle').classList.toggle('text-blue-400', this.gridVisible);
    }

    toggleSnap() {
        this.snapEnabled = !this.snapEnabled;
        document.getElementById('snapToggle').classList.toggle('text-blue-400', this.snapEnabled);
    }

    changeResolution(resolution) {
        const [width, height] = resolution.split('x').map(Number);
        this.canvasWidth = width;
        this.canvasHeight = height;
        this.updateCanvasSize();
    }

    exportProject() {
        const project = {
            output_format: "mp4",
            width: this.canvasWidth,
            height: this.canvasHeight,
            duration: this.duration,
            elements: this.elements.map(element => {
                const cleanElement = { ...element };
                delete cleanElement.id;
                return cleanElement;
            })
        };

        const blob = new Blob([JSON.stringify(project, null, 2)], {
            type: "application/json"
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "visionframe_project.json";
        a.click();
        URL.revokeObjectURL(url);
    }

    undo() {
        // TODO: Implement undo functionality
        console.log('Undo');
    }

    redo() {
        // TODO: Implement redo functionality
        console.log('Redo');
    }

    applyAnimationPreset(preset) {
        if (!this.selectedElement) return;

        // TODO: Implement animation presets
        console.log('Apply animation preset:', preset);
    }

    setCurrentTime(timeString) {
        const [minutes, seconds] = timeString.split(':').map(parseFloat);
        this.currentTime = minutes * 60 + seconds;
        this.renderCanvas();
        this.updateTimelinePlayhead();
    }

    setDuration(timeString) {
        const [minutes, seconds] = timeString.split(':').map(parseFloat);
        this.duration = minutes * 60 + seconds;
        this.renderTimeline();
    }

    toggleElementVisibility(elementId) {
        const element = this.elements.find(el => el.id === elementId);
        if (element) {
            element.visible = !element.visible;
            this.renderCanvas();
        }
    }
}

// Initialize the editor when the page loads
let editor;
document.addEventListener('DOMContentLoaded', () => {
    editor = new CreatomateEditor();
});
