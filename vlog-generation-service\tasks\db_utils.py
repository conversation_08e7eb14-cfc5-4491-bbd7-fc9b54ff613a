"""
Database utilities for the queue system.
"""

import os
import json
import logging
import pymysql
from datetime import datetime
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_DATABASE', 'vlog_generator'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'autocommit': True
}

def get_db_connection():
    """Get a database connection."""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        raise

def update_task_status(queue_id, status, error_message=None, result_data=None):
    """Update the status of a task in the process_queue table."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        update_fields = ["status = %s"]
        update_values = [status]

        # Add start_time if task is starting
        if status == 'processing':
            # First check if start_time is already set
            cursor.execute("SELECT start_time FROM process_queue WHERE id = %s", (queue_id,))
            result = cursor.fetchone()
            if not result or not result['start_time']:
                update_fields.append("start_time = %s")
                update_values.append(datetime.now())
                logger.info(f"Setting start_time for task {queue_id}")

        # Add end_time if task is completed or failed
        if status in ['completed', 'failed']:
            # First check if end_time is already set
            cursor.execute("SELECT end_time FROM process_queue WHERE id = %s", (queue_id,))
            result = cursor.fetchone()
            if not result or not result['end_time']:
                update_fields.append("end_time = %s")
                update_values.append(datetime.now())
                logger.info(f"Setting end_time for task {queue_id}")

        # Add error_message if provided
        if error_message:
            update_fields.append("error_message = %s")
            update_values.append(error_message)

        # Add result_data if provided
        if result_data:
            update_fields.append("result_data = %s")
            update_values.append(json.dumps(result_data))

        # Build the SQL query
        sql = f"UPDATE process_queue SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(queue_id)

        # Execute the query
        cursor.execute(sql, update_values)
        conn.commit()

        logger.info(f"Updated task {queue_id} status to {status}")
        return True
    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_pending_tasks(limit=10):
    """Get pending tasks from the process_queue table."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get tasks with status 'pending', ordered by ID to ensure sequential processing
        cursor.execute("""
            SELECT * FROM process_queue
            WHERE status = 'pending'
            ORDER BY id ASC
            LIMIT %s
        """, (limit,))

        tasks = cursor.fetchall()
        return tasks
    except Exception as e:
        logger.error(f"Error getting pending tasks: {str(e)}")
        return []
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_next_task_in_sequence(chunk_id, current_step_order):
    """Get the next task in the sequence for a chunk."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get the next task in the sequence
        cursor.execute("""
            SELECT * FROM process_queue
            WHERE chunk_id = %s AND step_order = %s
        """, (chunk_id, current_step_order + 1))

        next_task = cursor.fetchone()
        return next_task
    except Exception as e:
        logger.error(f"Error getting next task in sequence: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def activate_next_task(chunk_id, current_step_order):
    """Activate the next task in the sequence for a chunk."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Update the next task in the sequence to 'pending' and reset timestamps
        cursor.execute("""
            UPDATE process_queue
            SET status = 'pending',
                start_time = NULL,
                end_time = NULL
            WHERE chunk_id = %s AND step_order = %s
        """, (chunk_id, current_step_order + 1))

        conn.commit()

        # Get the updated task
        cursor.execute("""
            SELECT * FROM process_queue
            WHERE chunk_id = %s AND step_order = %s
        """, (chunk_id, current_step_order + 1))

        next_task = cursor.fetchone()

        if next_task:
            logger.info(f"Activated next task {next_task['id']} for chunk {chunk_id}")

        return next_task
    except Exception as e:
        logger.error(f"Error activating next task: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_chunk_data(chunk_id):
    """Get data for a content chunk."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get the chunk data
        cursor.execute("""
            SELECT * FROM content_chunk
            WHERE id = %s
        """, (chunk_id,))

        chunk = cursor.fetchone()

        if not chunk:
            logger.error(f"Chunk {chunk_id} not found")
            return None

        # Get the content data
        cursor.execute("""
            SELECT * FROM generated_content
            WHERE id = %s
        """, (chunk['content_id'],))

        content = cursor.fetchone()

        if not content:
            logger.error(f"Content {chunk['content_id']} not found")
            return None

        # Combine the data
        result = {
            'chunk': chunk,
            'content': content
        }

        return result
    except Exception as e:
        logger.error(f"Error getting chunk data: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_task_by_id(queue_id):
    """Get a task by its ID."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get the task
        cursor.execute("""
            SELECT * FROM process_queue
            WHERE id = %s
        """, (queue_id,))

        task = cursor.fetchone()
        return task
    except Exception as e:
        logger.error(f"Error getting task by ID: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_task_execution_stats():
    """Get statistics on task execution times."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get statistics on completed tasks
        cursor.execute("""
            SELECT
                process_step,
                COUNT(*) as total_tasks,
                AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avg_execution_time,
                MIN(TIMESTAMPDIFF(SECOND, start_time, end_time)) as min_execution_time,
                MAX(TIMESTAMPDIFF(SECOND, start_time, end_time)) as max_execution_time
            FROM process_queue
            WHERE status = 'completed'
                AND start_time IS NOT NULL
                AND end_time IS NOT NULL
            GROUP BY process_step
        """)

        stats = cursor.fetchall()

        # Get counts by status
        cursor.execute("""
            SELECT
                status,
                COUNT(*) as count
            FROM process_queue
            GROUP BY status
        """)

        status_counts = cursor.fetchall()

        return {
            'execution_stats': stats,
            'status_counts': status_counts
        }
    except Exception as e:
        logger.error(f"Error getting task execution stats: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
