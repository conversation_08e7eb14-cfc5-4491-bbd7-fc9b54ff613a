import pymysql
import csv
import re
from dotenv import load_dotenv
import os
from datetime import datetime

def clean_scenario(scenario):
    """Remove numbering from the beginning of the scenario."""
    # Remove patterns like "1.", "2.", etc. from the start of the string
    cleaned = re.sub(r'^\d+\.\s*', '', scenario.strip())
    return cleaned

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

# Path to CSV file - using relative path
CSV_FILE_PATH = os.path.join("content-csv", "content1.csv")

# Function to log events to database
def log_event(cursor, event_type, message, status="INFO", error=None):
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute(
            "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
            "VALUES (%s, %s, %s, %s, %s)",
            (timestamp, event_type, status, message, str(error) if error else None)
        )
    except Exception as e:
        print(f"Failed to log event: {e}")

try:
    # Verify if file exists
    if not os.path.exists(CSV_FILE_PATH):
        print(f"Error: CSV file not found at {os.path.abspath(CSV_FILE_PATH)}")
        print("Please ensure the file exists and the path is correct")
        exit(1)

    # Connect to MySQL database
    conn = pymysql.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    )
    cursor = conn.cursor()

    # SQL query to insert data (4 columns as per your schema)
    insert_query = """
    INSERT INTO generated_content 
    (scenario, empathetic_advice, practical_advice, thumbnail_prompt)
    VALUES (%s, %s, %s, %s)
    """

    # Open and read the CSV file
    with open(CSV_FILE_PATH, encoding="utf-8") as csv_file:
        csv_reader = csv.reader(csv_file)
        header = next(csv_reader)  # Skip header row
        
        print(f"CSV Header: {header}")  # Debug print
        print(f"Number of columns in header: {len(header)}")
        
        rows_processed = 0
        rows_successful = 0
        
        for row in csv_reader:
            rows_processed += 1
            print(f"\nProcessing row {rows_processed}: {row}")
            print(f"Number of columns in row: {len(row)}")
            
            # Ensure row has exactly 4 values
            if len(row) != 4:
                print(f"Warning: Row {rows_processed} has {len(row)} columns instead of 4")
                print(f"Row content: {row}")
                continue
            
            try:
                # Extract the 4 required columns
                scenario = clean_scenario(row[0])  # Clean the scenario before using it
                empathetic_advice = row[1]
                practical_advice = row[2]
                thumbnail_prompt = row[3]
                
                # Log the cleaning operation
                log_event(cursor, "SCENARIO_CLEANED", f"Original: {row[0]} -> Cleaned: {scenario}")
                
                # Create tuple of values for insertion
                values = (scenario, empathetic_advice, practical_advice, thumbnail_prompt)
                
                # Execute insert
                cursor.execute(insert_query, values)
                rows_successful += 1
                print(f"Successfully inserted row {rows_processed}")
                
            except Exception as e:
                print(f"Error inserting row {rows_processed}: {e}")
                print(f"Row data: {row}")
                log_event(cursor, "ROW_PROCESSING_ERROR", f"Error processing row {rows_processed}", "ERROR", e)
                continue

    # Commit the transaction
    conn.commit()
    print(f"\nImport Summary:")
    print(f"Total rows processed: {rows_processed}")
    print(f"Successfully inserted: {rows_successful}")
    print(f"Failed rows: {rows_processed - rows_successful}")

except pymysql.MySQLError as e:
    print(f"MySQL Error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
    print(f"Error type: {type(e)}")
finally:
    if 'conn' in locals() and conn:
        conn.close()
        print("Database connection closed")
