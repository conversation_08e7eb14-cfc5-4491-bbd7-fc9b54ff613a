import asyncio
import aiomysql
import json
import os
from dotenv import load_dotenv
import logging
from datetime import datetime

# Import necessary functions from content_generationgrok.py
from content_generationgrok import generate_image_prompts, save_image_prompts, log_event

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('image_prompt_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'db': os.getenv('DB_DATABASE'),
}

async def log_to_db(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Log events to database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def get_pending_content(pool):
    """Retrieve content that needs image prompts generated."""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT id, scenario, empathetic_advice, practical_advice, thumbnail_prompt 
                    FROM generated_content 
                    WHERE image_prompt_generated IS NULL OR image_prompt_generated = 0
                """)
                results = await cursor.fetchall()
                await log_to_db(pool, "PENDING_CONTENT_QUERY", f"Found {len(results)} pending items")
                return results
    except Exception as e:
        await log_to_db(pool, "PENDING_CONTENT_QUERY_ERROR", "Failed to fetch pending content", "ERROR", e)
        raise

async def mark_content_processed(pool, content_id):
    """Mark content as having image prompts generated."""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    UPDATE generated_content 
                    SET image_prompt_generated = 1 
                    WHERE id = %s
                """, (content_id,))
                await conn.commit()
                await log_to_db(pool, "CONTENT_MARKED_PROCESSED", f"Content ID {content_id} marked as processed")
    except Exception as e:
        await log_to_db(pool, "MARK_PROCESSED_ERROR", f"Failed to mark content ID {content_id} as processed", "ERROR", e)
        raise

async def process_pending_content():
    """Main function to process all pending content."""
    pool = await aiomysql.create_pool(**DB_CONFIG)
    
    try:
        await log_to_db(pool, "PROCESS_START", "Starting pending content processing")
        
        pending_content = await get_pending_content(pool)
        
        if not pending_content:
            await log_to_db(pool, "NO_PENDING_CONTENT", "No pending content found")
            print("No pending content found for image prompt generation.")
            return

        print(f"Found {len(pending_content)} items pending for image prompt generation.")

        for content in pending_content:
            content_id, scenario, empathetic_advice, practical_advice, thumbnail_prompt = content
            
            try:
                print(f"\nProcessing content ID: {content_id}")
                
                # Combine advice for image prompt generation
                combined_advice = f"{empathetic_advice}\n\n{practical_advice}"
                
                # Generate image prompts
                await log_to_db(
                    pool,
                    "IMAGE_PROMPT_GENERATION_START",
                    f"Starting image prompt generation for content ID: {content_id}"
                )
                
                image_prompts = await generate_image_prompts(pool, combined_advice)
                
                # Save the generated prompts
                await save_image_prompts(pool, content_id, image_prompts)
                
                # Mark content as processed
                await mark_content_processed(pool, content_id)
                
                await log_to_db(
                    pool,
                    "IMAGE_PROMPT_GENERATION_SUCCESS",
                    f"Successfully generated image prompts for content ID: {content_id}"
                )
                
                print(f"Successfully processed content ID: {content_id}")
                
            except Exception as e:
                await log_to_db(
                    pool,
                    "IMAGE_PROMPT_GENERATION_ERROR",
                    f"Error processing content ID {content_id}: {str(e)}",
                    "ERROR",
                    e
                )
                print(f"Error processing content ID {content_id}: {str(e)}")
                continue

    except Exception as e:
        await log_to_db(pool, "PROCESS_ERROR", "Error in main process", "ERROR", e)
        print(f"Error in main process: {str(e)}")
        raise
    finally:
        await log_to_db(pool, "PROCESS_END", "Ending pending content processing")
        pool.close()
        await pool.wait_closed()

def main():
    asyncio.run(process_pending_content())

if __name__ == "__main__":
    main()
