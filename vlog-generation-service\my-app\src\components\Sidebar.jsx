import React, { useState } from 'react';
import { Nav, Dropdown, Collapse } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ContentPasteIcon from '@mui/icons-material/ContentPaste';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import SettingsIcon from '@mui/icons-material/Settings';
import MenuBookIcon from '@mui/icons-material/MenuBook';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import MovieFilterIcon from '@mui/icons-material/MovieFilter';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import PersonIcon from '@mui/icons-material/Person';
import DescriptionIcon from '@mui/icons-material/Description';
import './Sidebar.css';

const Sidebar = () => {
    const location = useLocation();



    return (
        <div className="sidebar">
            <div className="sidebar-header">
                <div className="app-logo">
                    <div className="logo-container position-relative" style={{ width: '40px', height: '40px', marginRight: '12px' }}>
                        <div className="position-absolute" style={{ top: 0, left: 0, width: '100%', height: '100%', background: '#2541b8', borderRadius: '8px', transform: 'rotate(45deg)', animation: 'pulse 3s infinite ease-in-out' }}></div>
                        <div className="position-absolute d-flex align-items-center justify-content-center" style={{ top: 0, left: 0, width: '100%', height: '100%', zIndex: 2 }}>
                            <span className="fw-bold text-white" style={{ fontSize: '1rem' }}>VF</span>
                        </div>
                    </div>
                    <div>
                        <h2 className="app-title" style={{ color: '#2541b8', letterSpacing: '0.5px', margin: 0, fontSize: '1.2rem' }}>VisionFrame</h2>
                        <div className="d-flex align-items-center">
                            <span className="fw-bold" style={{ color: '#ff5757', fontSize: '0.9rem', letterSpacing: '1px' }}>AI</span>
                            <span className="ms-2 badge bg-primary" style={{ fontSize: '0.6rem', backgroundColor: '#e6ebff !important', color: '#2541b8', border: '1px solid #d1d9ff' }}>BETA</span>
                        </div>
                    </div>
                </div>
            </div>
            <Nav className="flex-column">
                <Nav.Link
                    as={Link}
                    to="/dashboard"
                    className={`sidebar-link ${location.pathname === '/dashboard' ? 'active' : ''}`}
                >
                    <DashboardIcon className="sidebar-icon" />
                    <span>Dashboard</span>
                </Nav.Link>
                <Nav.Link
                    as={Link}
                    to="/dashboard/content-management"
                    className={`sidebar-link ${location.pathname.includes('/dashboard/content-management') ? 'active' : ''}`}
                >
                    <ContentPasteIcon className="sidebar-icon" />
                    <span>Content Management</span>
                </Nav.Link>

                <Nav.Link
                    as={Link}
                    to="/dashboard/videos"
                    className={`sidebar-link ${location.pathname.includes('/videos') ? 'active' : ''}`}
                >
                    <VideoLibraryIcon className="sidebar-icon" />
                    <span>Videos</span>
                </Nav.Link>

                <Nav.Link
                    as={Link}
                    to="/dashboard/cheatsheets"
                    className={`sidebar-link ${location.pathname.includes('/cheatsheets') ? 'active' : ''}`}
                >
                    <MenuBookIcon className="sidebar-icon" />
                    <span>Cheatsheets</span>
                </Nav.Link>

                <Nav.Link
                    as={Link}
                    to="/dashboard/settings"
                    className={`sidebar-link ${location.pathname.includes('/settings') ? 'active' : ''}`}
                >
                    <SettingsIcon className="sidebar-icon" />
                    <span>Settings</span>
                </Nav.Link>
            </Nav>

            {/* User Profile Section */}
            <div className="user-profile">
                <Dropdown>
                    <Dropdown.Toggle variant="link" id="user-dropdown" className="user-dropdown-toggle">
                        <div className="user-info">
                            <div className="user-avatar">
                                <AccountCircleIcon className="avatar-icon" />
                            </div>
                            <div className="user-details">
                                <div className="user-name">Admin User</div>
                                <div className="user-role">Administrator</div>
                            </div>
                        </div>
                    </Dropdown.Toggle>

                    <Dropdown.Menu className="user-dropdown-menu">
                        <Dropdown.Item as={Link} to="/dashboard/profile">
                            <PersonIcon className="dropdown-icon" />
                            <span>My Profile</span>
                        </Dropdown.Item>
                        <Dropdown.Divider />
                        <Dropdown.Item as={Link} to="/dashboard/logout">
                            <ExitToAppIcon className="dropdown-icon" />
                            <span>Logout</span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </div>
        </div>
    );
};

export default Sidebar;
