{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/styles": "^5.15.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "bootstrap": "^5.3.3", "clsx": "^2.1.1", "react": "18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "18.2.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "tailwind-merge": "^3.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.3.0"}}