# VisionFrame AI Queue System

This document provides instructions for setting up and running the VisionFrame AI queue system.

## Overview

The queue system consists of the following components:

1. **Redis**: Message broker for Celery
2. **Celery Worker**: Processes tasks from the queue
3. **Celery Beat**: Schedules periodic tasks
4. **API Server**: Provides endpoints for submitting tasks to the queue

## Prerequisites

- Python 3.8+
- Redis server
- MySQL database

## Installation

### 1. Install Redis

#### Windows
Download and install Redis from https://github.com/microsoftarchive/redis/releases

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis
```

#### macOS
```bash
brew install redis
```

### 2. Install Python Dependencies

```bash
python -m pip install celery redis flask-cors pymysql aiomysql python-dotenv requests
```

## Configuration

The queue system is configured using environment variables in the `.env` file:

```
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_DATABASE=vlog_generator

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# API Configuration
API_BASE_URL=http://localhost:5000
```

## Starting the Queue System

### Option 1: Start All Components at Once

#### Windows
```bash
start_queue_system.bat
```

#### Linux/Mac
```bash
chmod +x start_queue_system.sh
./start_queue_system.sh
```

### Option 2: Start Components Individually

#### Start Redis
```bash
# Windows
redis-server

# Linux/Mac
redis-server &
```

#### Start Celery Worker
```bash
python start_worker.py
```

#### Start Celery Beat
```bash
python start_beat.py
```

#### Start API Server
```bash
python consolidated_api.py
```

## Using the Queue System

### Submit Tasks to the Queue

You can submit tasks to the queue using the media_queue_bridge.py script:

```bash
# Submit chunks to the media generation pipeline
python media_queue_bridge.py submit_media 1 2 3

# Submit chunks to a specific step of the pipeline
python media_queue_bridge.py submit_step "Generate Images" 1 2 3
```

### Monitor the Queue

You can monitor the queue status using the media_queue_bridge.py script:

```bash
# Get the status of the processing queue
python media_queue_bridge.py get_status
```

### API Endpoints

The queue system provides the following API endpoints:

- `POST /api/queue/submit`: Submit chunks to the processing queue
- `POST /api/queue/media/submit`: Submit chunks to the media generation pipeline
- `POST /api/queue/media/step`: Submit chunks to a specific step of the pipeline
- `GET /api/queue/status`: Get the status of the processing queue
- `GET /api/queue/media/steps`: Get the available steps in the pipeline
- `POST /api/queue/retry/<task_id>`: Retry a failed task
- `POST /api/queue/clear`: Clear the processing queue

## Troubleshooting

### Redis Connection Issues

If you encounter Redis connection issues, make sure Redis is running:

```bash
# Check if Redis is running
redis-cli ping
```

If Redis is not running, start it:

```bash
# Windows
redis-server

# Linux/Mac
redis-server &
```

### Celery Worker Issues

If the Celery worker is not processing tasks, check the worker logs:

```bash
# Start the worker with debug logging
celery -A tasks worker --loglevel=debug --pool=solo
```

### Database Issues

If you encounter database issues, make sure the database is running and the credentials in the `.env` file are correct.

## Additional Resources

- [Celery Documentation](https://docs.celeryproject.org/)
- [Redis Documentation](https://redis.io/documentation)
- [Flask Documentation](https://flask.palletsprojects.com/)
