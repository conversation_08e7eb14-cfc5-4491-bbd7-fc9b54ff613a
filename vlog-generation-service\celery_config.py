"""
Celery configuration for the VisionFrame AI queue system.
This module defines the Celery app and its configuration.
"""

import os
from celery import Celery
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create Celery app
celery_app = Celery(
    'visionframe_tasks',
    broker='redis://localhost:6379/0',
    backend='redis://localhost:6379/1',
    include=[
        'tasks.image_tasks',
        'tasks.speech_tasks',
        'tasks.slideshow_tasks',
        'tasks.subtitle_tasks',
        'tasks.video_tasks',
        'tasks.queue_manager'
    ]
)

# Configure Celery
celery_app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Queue settings
    task_default_queue='default',
    task_queues={
        'default': {},
        'image_generation': {},
        'speech_generation': {},
        'slideshow_creation': {},
        'subtitle_creation': {},
        'video_mixing': {},
    },
    
    # Concurrency settings
    worker_concurrency=4,  # Number of worker processes
    
    # Task execution settings
    task_acks_late=True,  # Tasks are acknowledged after execution
    task_reject_on_worker_lost=True,  # Tasks are rejected if worker is lost
    
    # Result settings
    result_expires=3600,  # Results expire after 1 hour
    
    # Logging settings
    worker_hijack_root_logger=False,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,  # Maximum number of retries
)

# Define task routes
celery_app.conf.task_routes = {
    'tasks.image_tasks.*': {'queue': 'image_generation'},
    'tasks.speech_tasks.*': {'queue': 'speech_generation'},
    'tasks.slideshow_tasks.*': {'queue': 'slideshow_creation'},
    'tasks.subtitle_tasks.*': {'queue': 'subtitle_creation'},
    'tasks.video_tasks.*': {'queue': 'video_mixing'},
    'tasks.queue_manager.*': {'queue': 'default'},
}

# Define periodic tasks
celery_app.conf.beat_schedule = {
    'check-pending-tasks': {
        'task': 'tasks.queue_manager.check_pending_tasks',
        'schedule': 30.0,  # Every 30 seconds
    },
}

if __name__ == '__main__':
    celery_app.start()
