"""
Image generation tasks for the VisionFrame AI queue system.
"""

import os
import json
import logging
import requests
from celery import shared_task
from .db_utils import get_chunk_data, update_task_status, get_task_by_id
from .task_utils import task_completed, task_failed
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Image generation parameters
IMAGE_MODEL = os.getenv('IMAGE_MODEL', 'runware:101@1')
IMAGE_HEIGHT = int(os.getenv('IMAGE_HEIGHT', 2048))
IMAGE_WIDTH = int(os.getenv('IMAGE_WIDTH', 1152))
IMAGE_CFG_SCALE = float(os.getenv('IMAGE_CFG_SCALE', 7.5))
IMAGE_NEGATIVE_PROMPT = os.getenv('IMAGE_NEGATIVE_PROMPT', 'blurry, low quality, deformed, text, watermark')
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

@shared_task(
    name='tasks.image_tasks.generate_images',
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    rate_limit='10/m'
)
def generate_images(self, queue_id):
    """Generate images for a content chunk."""
    logger.info(f"Generating images for task {queue_id}")

    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the chunk data
        chunk_id = task['chunk_id']
        chunk_data = get_chunk_data(chunk_id)

        if not chunk_data:
            error_msg = f"Chunk data not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Extract the text from the chunk
        chunk_text = chunk_data['chunk']['text']

        if not chunk_text:
            error_msg = f"No text found in chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Determine how many images to generate (max 5 per chunk)
        # Calculate based on text length: 1 image per 146 words, max 5
        word_count = len(chunk_text.split())
        num_images = min(max(1, word_count // 146), 5)

        logger.info(f"Generating {num_images} images for chunk {chunk_id} with {word_count} words")

        # Generate images using Runware API
        generated_images = []

        for i in range(num_images):
            # Create a prompt for the image
            # Use a portion of the text for each image
            start_idx = (i * len(chunk_text)) // num_images
            end_idx = ((i + 1) * len(chunk_text)) // num_images
            text_portion = chunk_text[start_idx:end_idx]

            # Limit prompt length
            if len(text_portion) > 500:
                text_portion = text_portion[:497] + "..."

            prompt = f"High quality, photorealistic image of: {text_portion}"

            # Call the Runware API
            image_path = generate_image_with_runware(prompt, f"chunk_{chunk_id}_image_{i+1}")

            if image_path:
                generated_images.append({
                    'image_path': image_path,
                    'prompt': prompt,
                    'order': i + 1
                })
            else:
                logger.warning(f"Failed to generate image {i+1} for chunk {chunk_id}")

        # Update the result data
        result_data = {
            'generated_images': generated_images,
            'num_images': len(generated_images)
        }

        # Mark the task as completed
        task_completed.delay(queue_id, result_data)

        return f"Generated {len(generated_images)} images for chunk {chunk_id}"

    except Exception as e:
        logger.error(f"Error generating images for task {queue_id}: {str(e)}")

        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            # If max retries exceeded, mark the task as failed
            task_failed.delay(queue_id, str(e))

        return f"Error generating images for task {queue_id}: {str(e)}"

def generate_image_with_runware(prompt, image_name):
    """Generate an image using the Runware API."""
    if not RUNWARE_API_KEY:
        logger.error("RUNWARE_API_KEY not configured")
        return None

    try:
        # Create the images directory if it doesn't exist
        os.makedirs('images', exist_ok=True)

        # Prepare the API request
        url = "https://api.runware.ai/v1/image/generate"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RUNWARE_API_KEY}"
        }
        payload = {
            "model": IMAGE_MODEL,
            "prompt": prompt,
            "negative_prompt": IMAGE_NEGATIVE_PROMPT,
            "height": IMAGE_HEIGHT,
            "width": IMAGE_WIDTH,
            "cfg_scale": IMAGE_CFG_SCALE,
            "steps": 30,
            "seed": -1,
            "sampler": "DPM++ 2M Karras"
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            logger.error(f"Runware API error: {response.status_code} - {response.text}")
            return None

        # Parse the response
        result = response.json()

        if 'images' not in result or not result['images']:
            logger.error(f"No images in Runware API response: {result}")
            return None

        # Save the image
        image_data = result['images'][0]
        image_path = f"images/{image_name}.png"

        with open(image_path, 'wb') as f:
            f.write(bytes.fromhex(image_data))

        logger.info(f"Image saved to {image_path}")

        return image_path

    except Exception as e:
        logger.error(f"Error generating image with Runware: {str(e)}")
        return None
