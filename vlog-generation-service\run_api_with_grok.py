import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import the Flask app and Grok blueprint
from API.contentapi_crud import app
from API.grok_api import grok_bp

# Ensure the blueprint is registered
if not any(bp.name == 'grok' for bp in app.blueprints.values()):
    logger.info("Registering Grok blueprint")
    app.register_blueprint(grok_bp)
else:
    logger.info("Grok blueprint already registered")

if __name__ == '__main__':
    logger.info("Starting API server with Grok integration...")
    logger.info("API endpoints available at http://localhost:5000/api/")
    logger.info("Grok API endpoints available at http://localhost:5000/api/grok/")

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
