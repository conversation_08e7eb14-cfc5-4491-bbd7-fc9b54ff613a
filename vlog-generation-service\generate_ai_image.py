import asyncio
import os
from dotenv import load_dotenv
import logging
from runware import Runware, IImageInference

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ai_image.log')
    ]
)
logger = logging.getLogger(__name__)

# Runware configuration
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

# Define a more detailed prompt for AI Image Generation
AI_IMAGE_PROMPT = """
A stunning, photorealistic visualization of AI generating images from text prompts. 
Show a futuristic interface with text prompts on one side and resulting beautiful, 
diverse images appearing on the other side. Include visual elements like neural networks, 
creative design elements, and a sense of technological magic. The scene should be vibrant, 
colorful, and visually impressive with high detail. 4K resolution, professional lighting, 
cinematic quality.
"""

async def generate_image(prompt, filename, runware):
    """Generate an image using Runware API."""
    try:
        logger.info(f"Generating image for: {filename}")
        
        request_image = IImageInference(
            positivePrompt=prompt,
            negativePrompt="blurry, low quality, deformed, text, watermark, signature, pixelated",
            model="runware:101@1",
            numberResults=1,
            height=1024,
            width=1024,
            CFGScale=7.5,
        )
                
        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL
        
        # Download the image
        await download_image(image_url, filename)
        
        logger.info(f"Successfully generated image: {filename}")
        return image_url
    except Exception as e:
        logger.error(f"Error generating image {filename}: {str(e)}")
        raise

async def download_image(image_url, filename):
    """Download image from URL and save to local storage."""
    import aiohttp
    import aiofiles
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    # Ensure the images directory exists
                    output_dir = "my-app/public/images"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # Save the image
                    file_path = os.path.join(output_dir, filename)
                    async with aiofiles.open(file_path, mode='wb') as f:
                        await f.write(await response.read())
                    
                    logger.info(f"Successfully downloaded image to {file_path}")
                    return True
                else:
                    logger.error(f"Failed to download image. Status code: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        return False

async def main():
    """Main function to generate the AI Image Generation image."""
    logger.info("Starting AI Image Generation image creation")
    
    runware = Runware(api_key=RUNWARE_API_KEY)
    await runware.connect()
    
    try:
        filename = "ai_image_generation.jpg"
        await generate_image(AI_IMAGE_PROMPT, filename, runware)
        logger.info(f"Image generation complete: {filename}")
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
    finally:
        logger.info("Image generation process completed")

if __name__ == "__main__":
    asyncio.run(main())
