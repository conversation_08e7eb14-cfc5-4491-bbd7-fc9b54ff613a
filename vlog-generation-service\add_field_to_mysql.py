import pymysql
import logging
import sys
import os
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    import os

    DB_CONFIG = {
        "host": os.getenv('DB_HOST', 'localhost'),
        "user": os.getenv('DB_USER', 'root'),
        "password": os.getenv('DB_PASSWORD', ''),
        "db": os.getenv('DB_DATABASE', 'vlog_generator'),
        "charset": "utf8mb4",
        "cursorclass": pymysql.cursors.DictCursor
    }
    logger.info("Loaded database configuration from .env file")
    logger.info(f"Database: {DB_CONFIG['db']}")
except Exception as e:
    logger.error(f"Error loading database configuration: {str(e)}")
    DB_CONFIG = {
        "host": "localhost",
        "user": "root",
        "password": "@Oppa121089",
        "db": "vlog_generator",
        "charset": "utf8mb4",
        "cursorclass": pymysql.cursors.DictCursor
    }
    logger.info("Using default database configuration")

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"SHOW COLUMNS FROM {table_name} LIKE '{column_name}'")
    return cursor.fetchone() is not None

def add_field_to_table(table_name, field_name, field_type):
    """Add a field to a table."""
    conn = None
    cursor = None

    try:
        # Connect to the database
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if table exists
        if not check_table_exists(cursor, table_name):
            logger.error(f"{table_name} table does not exist.")
            return False

        # Check if column already exists
        if check_column_exists(cursor, table_name, field_name):
            logger.info(f"{field_name} column already exists in {table_name} table.")
            return True

        # Add column to table
        logger.info(f"Adding {field_name} column to {table_name} table...")
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type}")
        conn.commit()
        logger.info(f"{field_name} column added successfully!")

        # Verify the column was added
        if check_column_exists(cursor, table_name, field_name):
            logger.info(f"Verified {field_name} column exists in {table_name} table.")
            return True
        else:
            logger.error(f"Failed to add {field_name} column to {table_name} table.")
            return False

    except Exception as e:
        logger.error(f"Error adding {field_name} field: {str(e)}")
        return False

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def list_table_columns(table_name):
    """List all columns in a table."""
    conn = None
    cursor = None

    try:
        # Connect to the database
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if table exists
        if not check_table_exists(cursor, table_name):
            logger.error(f"{table_name} table does not exist.")
            return False

        # Get table schema
        cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        columns = cursor.fetchall()

        # Print column information
        logger.info(f"Columns in {table_name} table:")
        for column in columns:
            logger.info(f"  {column['Field']} ({column['Type']}){' PRIMARY KEY' if column['Key'] == 'PRI' else ''}{' NOT NULL' if column['Null'] == 'NO' else ''}")

        return True

    except Exception as e:
        logger.error(f"Error listing columns: {str(e)}")
        return False

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Add a field to a table in the MySQL database.')
    parser.add_argument('--table', type=str, default='content_prompt_claude', help='Table name')
    parser.add_argument('--field', type=str, default='content_prompt', help='Field name')
    parser.add_argument('--type', type=str, default='TEXT', help='Field type')
    parser.add_argument('--list', action='store_true', help='List table columns')

    args = parser.parse_args()

    logger.info(f"Starting script to manage fields in {args.table} table...")

    # Print current working directory for debugging
    logger.info(f"Current working directory: {os.getcwd()}")

    if args.list:
        # List table columns
        success = list_table_columns(args.table)
    else:
        # Add field to table
        success = add_field_to_table(args.table, args.field, args.type)

    if success:
        logger.info("Script completed successfully!")
    else:
        logger.error("Script failed to complete successfully.")
        sys.exit(1)

if __name__ == "__main__":
    main()
