// VisionFrame AI Video Editor - Main JavaScript File

class VideoEditor {
    constructor() {
        this.elements = [];
        this.selectedElement = null;
        this.currentTime = 0;
        this.isPlaying = false;
        this.duration = 10;
        this.animationFrame = null;
        this.timelineZoom = 1;
        this.timelineVisible = false;
        this.dragState = {
            isDragging: false,
            element: null,
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0
        };
        this.timelineDragState = {
            isDragging: false,
            element: null,
            startTime: 0,
            isResizing: false,
            resizeHandle: null
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTimeline();
        this.renderElements();
    }

    setupEventListeners() {
        // Project settings
        document.getElementById('duration').addEventListener('change', (e) => {
            this.duration = parseFloat(e.target.value);
            document.getElementById('timeline').max = this.duration;
            this.updateTimeDisplay();
        });

        // Add element buttons
        document.getElementById('addText').addEventListener('click', () => this.showModal('textModal'));
        document.getElementById('addImage').addEventListener('click', () => this.showModal('imageModal'));
        document.getElementById('addVideo').addEventListener('click', () => this.showModal('videoModal'));
        document.getElementById('addAudio').addEventListener('click', () => this.showModal('audioModal'));

        // File upload handlers
        document.getElementById('uploadImage').addEventListener('change', (e) => this.handleFileUpload(e, 'image'));
        document.getElementById('uploadVideo').addEventListener('change', (e) => this.handleFileUpload(e, 'video'));
        document.getElementById('uploadAudio').addEventListener('change', (e) => this.handleFileUpload(e, 'audio'));

        // Timeline view controls
        document.getElementById('toggleTimelineView').addEventListener('click', () => this.toggleTimelineView());
        document.getElementById('closeTimelineView').addEventListener('click', () => this.closeTimelineView());
        document.getElementById('timelineZoom').addEventListener('input', (e) => this.updateTimelineZoom(e.target.value));

        // Drag and drop for preview area
        this.setupDragAndDrop();

        // Modal handlers
        this.setupModalHandlers();

        // Timeline controls
        document.getElementById('playPause').addEventListener('click', () => this.togglePlayback());
        document.getElementById('timeline').addEventListener('input', (e) => {
            this.currentTime = parseFloat(e.target.value);
            this.updateTimeDisplay();
            this.renderElements();
        });

        // Project action buttons
        document.getElementById('loadSample').addEventListener('click', () => this.loadSampleProject());
        document.getElementById('clearProject').addEventListener('click', () => this.clearProject());
        document.getElementById('exportJson').addEventListener('click', () => this.exportToJson());
    }

    setupModalHandlers() {
        // Text modal
        document.getElementById('cancelText').addEventListener('click', () => this.hideModal('textModal'));
        document.getElementById('confirmText').addEventListener('click', () => this.addTextElement());

        // Image modal
        document.getElementById('cancelImage').addEventListener('click', () => this.hideModal('imageModal'));
        document.getElementById('confirmImage').addEventListener('click', () => this.addImageElement());

        // Video modal
        document.getElementById('cancelVideo').addEventListener('click', () => this.hideModal('videoModal'));
        document.getElementById('confirmVideo').addEventListener('click', () => this.addVideoElement());

        // Audio modal
        document.getElementById('cancelAudio').addEventListener('click', () => this.hideModal('audioModal'));
        document.getElementById('confirmAudio').addEventListener('click', () => this.addAudioElement());
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
        document.getElementById(modalId).classList.add('flex');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
        document.getElementById(modalId).classList.remove('flex');
    }

    addTextElement() {
        const text = document.getElementById('textContent').value;
        const x = document.getElementById('textX').value + '%';
        const y = document.getElementById('textY').value + '%';

        if (!text.trim()) {
            alert('Please enter text content');
            return;
        }

        const element = {
            id: this.generateId(),
            type: 'text',
            text: text,
            x: x,
            y: y,
            animations: [
                {
                    property: 'opacity',
                    keyframes: [
                        { time: 0, value: 0 },
                        { time: 1, value: 1 }
                    ]
                }
            ]
        };

        this.elements.push(element);
        this.hideModal('textModal');
        this.clearModalInputs('text');
        this.updateElementsList();
        this.renderElements();
    }

    addImageElement() {
        const src = document.getElementById('imageUrl').value;
        const x = document.getElementById('imageX').value + '%';
        const y = document.getElementById('imageY').value + '%';

        if (!src.trim()) {
            alert('Please enter image URL');
            return;
        }

        const element = {
            id: this.generateId(),
            type: 'image',
            src: src,
            x: x,
            y: y,
            animations: [
                {
                    property: 'opacity',
                    keyframes: [
                        { time: 0, value: 1 }
                    ]
                }
            ]
        };

        this.elements.push(element);
        this.hideModal('imageModal');
        this.clearModalInputs('image');
        this.updateElementsList();
        this.renderElements();
    }

    addVideoElement() {
        const src = document.getElementById('videoUrl').value;
        const start = parseFloat(document.getElementById('videoStart').value);
        const end = parseFloat(document.getElementById('videoEnd').value);
        const x = document.getElementById('videoX').value + '%';
        const y = document.getElementById('videoY').value + '%';

        if (!src.trim()) {
            alert('Please enter video URL');
            return;
        }

        if (end <= start) {
            alert('End time must be greater than start time');
            return;
        }

        const element = {
            id: this.generateId(),
            type: 'video',
            src: src,
            start: start,
            end: end,
            x: x,
            y: y,
            animations: [
                {
                    property: 'opacity',
                    keyframes: [
                        { time: start, value: 1 },
                        { time: end, value: 1 }
                    ]
                }
            ]
        };

        this.elements.push(element);
        this.hideModal('videoModal');
        this.clearModalInputs('video');
        this.updateElementsList();
        this.renderElements();
    }

    addAudioElement() {
        const src = document.getElementById('audioUrl').value;
        const start = parseFloat(document.getElementById('audioStart').value);
        const end = parseFloat(document.getElementById('audioEnd').value);
        const volume = parseFloat(document.getElementById('audioVolume').value) / 100;

        if (!src.trim()) {
            alert('Please enter audio URL');
            return;
        }

        if (end <= start) {
            alert('End time must be greater than start time');
            return;
        }

        const element = {
            id: this.generateId(),
            type: 'audio',
            src: src,
            start: start,
            end: end,
            volume: volume,
            x: '50%',
            y: '50%',
            animations: [
                {
                    property: 'opacity',
                    keyframes: [
                        { time: start, value: 1 },
                        { time: end, value: 1 }
                    ]
                }
            ]
        };

        this.elements.push(element);
        this.hideModal('audioModal');
        this.clearModalInputs('audio');
        this.updateElementsList();
        this.renderElements();
        this.updateTimelineView();
    }

    clearModalInputs(type) {
        if (type === 'text') {
            document.getElementById('textContent').value = '';
            document.getElementById('textX').value = '50';
            document.getElementById('textY').value = '50';
        } else if (type === 'image') {
            document.getElementById('imageUrl').value = '';
            document.getElementById('imageX').value = '50';
            document.getElementById('imageY').value = '50';
        } else if (type === 'video') {
            document.getElementById('videoUrl').value = '';
            document.getElementById('videoStart').value = '0';
            document.getElementById('videoEnd').value = '5';
            document.getElementById('videoX').value = '50';
            document.getElementById('videoY').value = '50';
        } else if (type === 'audio') {
            document.getElementById('audioUrl').value = '';
            document.getElementById('audioStart').value = '0';
            document.getElementById('audioEnd').value = '5';
            document.getElementById('audioVolume').value = '100';
        }
    }

    handleFileUpload(event, type) {
        const file = event.target.files[0];
        if (!file) return;

        // Create object URL for the file
        const url = URL.createObjectURL(file);

        // Create element based on type
        const element = {
            id: this.generateId(),
            type: type,
            src: url,
            fileName: file.name,
            x: '50%',
            y: '50%',
            animations: [
                {
                    property: 'opacity',
                    keyframes: [
                        { time: 0, value: 1 }
                    ]
                }
            ]
        };

        // Add type-specific properties
        if (type === 'video' || type === 'audio') {
            element.start = 0;
            element.end = 5; // Default duration
        }

        if (type === 'audio') {
            element.volume = 1;
        }

        this.elements.push(element);
        this.updateElementsList();
        this.renderElements();
        this.updateTimelineView();

        // Clear the input
        event.target.value = '';

        console.log(`${type} file uploaded:`, file.name);
    }

    generateId() {
        return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    updateElementsList() {
        const container = document.getElementById('elementsList');
        container.innerHTML = '';

        this.elements.forEach((element, index) => {
            const div = document.createElement('div');
            div.className = 'element-item';
            div.dataset.elementId = element.id;

            if (this.selectedElement && this.selectedElement.id === element.id) {
                div.classList.add('selected');
            }

            let content = '';
            if (element.type === 'text') {
                content = element.text;
            } else if (element.type === 'image') {
                content = element.src.split('/').pop() || 'Image';
            } else if (element.type === 'video') {
                content = element.src.split('/').pop() || 'Video';
            }

            div.innerHTML = `
                <div class="element-type">${element.type}</div>
                <div class="element-content">${content}</div>
                <button class="delete-element mt-2 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs"
                        onclick="editor.deleteElement('${element.id}')">Delete</button>
            `;

            div.addEventListener('click', (e) => {
                if (!e.target.classList.contains('delete-element')) {
                    this.selectElement(element);
                }
            });

            container.appendChild(div);
        });
    }

    selectElement(element) {
        this.selectedElement = element;
        this.updateElementsList();
        this.updatePropertiesPanel();
        this.highlightPreviewElement(element.id);
    }

    deleteElement(elementId) {
        this.elements = this.elements.filter(el => el.id !== elementId);
        if (this.selectedElement && this.selectedElement.id === elementId) {
            this.selectedElement = null;
        }
        this.updateElementsList();
        this.updatePropertiesPanel();
        this.renderElements();
        this.updateTimelineView();
    }

    setupDragAndDrop() {
        const previewContainer = document.getElementById('previewContainer');

        // Mouse events for dragging elements in preview
        previewContainer.addEventListener('mousedown', (e) => this.handlePreviewMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handlePreviewMouseMove(e));
        document.addEventListener('mouseup', (e) => this.handlePreviewMouseUp(e));

        // Prevent default drag behavior on images
        previewContainer.addEventListener('dragstart', (e) => e.preventDefault());
    }

    handlePreviewMouseDown(e) {
        const element = e.target.closest('.preview-element');
        if (!element) return;

        const elementId = element.dataset.elementId;
        const elementData = this.elements.find(el => el.id === elementId);
        if (!elementData) return;

        this.selectElement(elementData);

        const rect = element.getBoundingClientRect();
        const containerRect = document.getElementById('previewContainer').getBoundingClientRect();

        this.dragState = {
            isDragging: true,
            element: elementData,
            startX: e.clientX,
            startY: e.clientY,
            offsetX: e.clientX - rect.left - rect.width / 2,
            offsetY: e.clientY - rect.top - rect.height / 2
        };

        element.classList.add('dragging');
        e.preventDefault();
    }

    handlePreviewMouseMove(e) {
        if (!this.dragState.isDragging) return;

        const containerRect = document.getElementById('previewContainer').getBoundingClientRect();
        const x = ((e.clientX - this.dragState.offsetX - containerRect.left) / containerRect.width) * 100;
        const y = ((e.clientY - this.dragState.offsetY - containerRect.top) / containerRect.height) * 100;

        // Constrain to container bounds
        const constrainedX = Math.max(0, Math.min(100, x));
        const constrainedY = Math.max(0, Math.min(100, y));

        this.dragState.element.x = constrainedX + '%';
        this.dragState.element.y = constrainedY + '%';

        this.renderElements();
        this.updatePropertiesPanel();

        e.preventDefault();
    }

    handlePreviewMouseUp(e) {
        if (!this.dragState.isDragging) return;

        const element = document.querySelector(`[data-element-id="${this.dragState.element.id}"]`);
        if (element) {
            element.classList.remove('dragging');
        }

        this.dragState = {
            isDragging: false,
            element: null,
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0
        };
    }

    updatePropertiesPanel() {
        const panel = document.getElementById('propertiesPanel');

        if (!this.selectedElement) {
            panel.innerHTML = '<p class="text-gray-400">Select an element to edit properties</p>';
            return;
        }

        let specificProperties = '';
        if (this.selectedElement.type === 'text') {
            specificProperties = `
                <div class="property-group">
                    <h4>Text Properties</h4>
                    <div class="property-row">
                        <label>Text:</label>
                        <input type="text" value="${this.selectedElement.text}"
                               onchange="editor.updateElementProperty('text', this.value)">
                    </div>
                </div>
            `;
        } else if (this.selectedElement.type === 'image') {
            specificProperties = `
                <div class="property-group">
                    <h4>Image Properties</h4>
                    <div class="property-row">
                        <label>Source URL:</label>
                        <input type="url" value="${this.selectedElement.src}"
                               onchange="editor.updateElementProperty('src', this.value)">
                    </div>
                </div>
            `;
        } else if (this.selectedElement.type === 'video') {
            specificProperties = `
                <div class="property-group">
                    <h4>Video Properties</h4>
                    <div class="property-row">
                        <label>Source URL:</label>
                        <input type="url" value="${this.selectedElement.src}"
                               onchange="editor.updateElementProperty('src', this.value)">
                    </div>
                    <div class="property-row">
                        <label>Start Time:</label>
                        <input type="number" value="${this.selectedElement.start}" step="0.1"
                               onchange="editor.updateElementProperty('start', parseFloat(this.value))">
                    </div>
                    <div class="property-row">
                        <label>End Time:</label>
                        <input type="number" value="${this.selectedElement.end}" step="0.1"
                               onchange="editor.updateElementProperty('end', parseFloat(this.value))">
                    </div>
                </div>
            `;
        } else if (this.selectedElement.type === 'audio') {
            specificProperties = `
                <div class="property-group">
                    <h4>Audio Properties</h4>
                    <div class="property-row">
                        <label>Source URL:</label>
                        <input type="url" value="${this.selectedElement.src}"
                               onchange="editor.updateElementProperty('src', this.value)">
                    </div>
                    <div class="property-row">
                        <label>Start Time:</label>
                        <input type="number" value="${this.selectedElement.start}" step="0.1"
                               onchange="editor.updateElementProperty('start', parseFloat(this.value))">
                    </div>
                    <div class="property-row">
                        <label>End Time:</label>
                        <input type="number" value="${this.selectedElement.end}" step="0.1"
                               onchange="editor.updateElementProperty('end', parseFloat(this.value))">
                    </div>
                    <div class="property-row">
                        <label>Volume:</label>
                        <input type="number" value="${(this.selectedElement.volume || 1) * 100}" min="0" max="100"
                               onchange="editor.updateElementProperty('volume', parseFloat(this.value) / 100)">
                    </div>
                </div>
            `;
        }

        panel.innerHTML = `
            <div class="property-group">
                <h4>Basic Properties</h4>
                <div class="property-row">
                    <label>X Position:</label>
                    <input type="text" value="${this.selectedElement.x}"
                           onchange="editor.updateElementProperty('x', this.value)">
                </div>
                <div class="property-row">
                    <label>Y Position:</label>
                    <input type="text" value="${this.selectedElement.y}"
                           onchange="editor.updateElementProperty('y', this.value)">
                </div>
            </div>
            ${specificProperties}
            <div class="property-group">
                <h4>Animations</h4>
                <div id="animationsList">
                    ${this.renderAnimationsList()}
                </div>
                <button class="add-keyframe" onclick="editor.addAnimation()">Add Animation</button>
            </div>
        `;
    }

    renderAnimationsList() {
        if (!this.selectedElement || !this.selectedElement.animations) {
            return '<p class="text-gray-400 text-sm">No animations</p>';
        }

        return this.selectedElement.animations.map((animation, animIndex) => `
            <div class="animation-group mb-4 p-3 bg-gray-700 rounded">
                <div class="flex justify-between items-center mb-2">
                    <select onchange="editor.updateAnimationProperty(${animIndex}, this.value)"
                            class="bg-gray-600 text-white p-1 rounded text-sm">
                        <option value="opacity" ${animation.property === 'opacity' ? 'selected' : ''}>Opacity</option>
                        <option value="x" ${animation.property === 'x' ? 'selected' : ''}>X Position</option>
                        <option value="y" ${animation.property === 'y' ? 'selected' : ''}>Y Position</option>
                        <option value="scale" ${animation.property === 'scale' ? 'selected' : ''}>Scale</option>
                        <option value="rotation" ${animation.property === 'rotation' ? 'selected' : ''}>Rotation</option>
                    </select>
                    <button class="delete-keyframe" onclick="editor.deleteAnimation(${animIndex})">Delete</button>
                </div>
                <div class="keyframes-list">
                    ${animation.keyframes.map((keyframe, kfIndex) => `
                        <div class="keyframe-item">
                            <div class="keyframe-controls">
                                <input type="number" value="${keyframe.time}" step="0.1" placeholder="Time"
                                       onchange="editor.updateKeyframe(${animIndex}, ${kfIndex}, 'time', parseFloat(this.value))">
                                <input type="text" value="${keyframe.value}" placeholder="Value"
                                       onchange="editor.updateKeyframe(${animIndex}, ${kfIndex}, 'value', this.value)">
                                <button class="delete-keyframe" onclick="editor.deleteKeyframe(${animIndex}, ${kfIndex})">×</button>
                            </div>
                        </div>
                    `).join('')}
                    <button class="add-keyframe" onclick="editor.addKeyframe(${animIndex})">Add Keyframe</button>
                </div>
            </div>
        `).join('');
    }

    addAnimation() {
        if (!this.selectedElement) return;

        const newAnimation = {
            property: 'opacity',
            keyframes: [
                { time: 0, value: 1 }
            ]
        };

        this.selectedElement.animations.push(newAnimation);
        this.updatePropertiesPanel();
        this.renderElements();
    }

    deleteAnimation(animIndex) {
        if (!this.selectedElement) return;

        this.selectedElement.animations.splice(animIndex, 1);
        this.updatePropertiesPanel();
        this.renderElements();
    }

    updateAnimationProperty(animIndex, property) {
        if (!this.selectedElement) return;

        this.selectedElement.animations[animIndex].property = property;
        this.updatePropertiesPanel();
        this.renderElements();
    }

    addKeyframe(animIndex) {
        if (!this.selectedElement) return;

        const animation = this.selectedElement.animations[animIndex];
        const newKeyframe = {
            time: this.currentTime,
            value: animation.property === 'opacity' ? 1 :
                   animation.property === 'scale' ? 1 :
                   animation.property === 'rotation' ? 0 : '50%'
        };

        animation.keyframes.push(newKeyframe);
        animation.keyframes.sort((a, b) => a.time - b.time);
        this.updatePropertiesPanel();
        this.renderElements();
    }

    deleteKeyframe(animIndex, kfIndex) {
        if (!this.selectedElement) return;

        this.selectedElement.animations[animIndex].keyframes.splice(kfIndex, 1);
        this.updatePropertiesPanel();
        this.renderElements();
    }

    updateKeyframe(animIndex, kfIndex, property, value) {
        if (!this.selectedElement) return;

        if (property === 'value' && typeof value === 'string') {
            // Try to parse as number if it's a numeric string
            const numValue = parseFloat(value);
            if (!isNaN(numValue) && value.trim() === numValue.toString()) {
                value = numValue;
            }
        }

        this.selectedElement.animations[animIndex].keyframes[kfIndex][property] = value;

        // Re-sort keyframes by time
        if (property === 'time') {
            this.selectedElement.animations[animIndex].keyframes.sort((a, b) => a.time - b.time);
            this.updatePropertiesPanel();
        }

        this.renderElements();
    }

    updateElementProperty(property, value) {
        if (this.selectedElement) {
            this.selectedElement[property] = value;
            this.renderElements();
            this.updateTimelineView();
        }
    }

    renderElements() {
        const container = document.getElementById('previewContainer');
        container.innerHTML = '';

        this.elements.forEach(element => {
            const div = document.createElement('div');
            div.className = 'preview-element';
            div.dataset.elementId = element.id;

            // Apply animations based on current time
            this.applyAnimations(element, div);

            // Position element
            div.style.left = element.x;
            div.style.top = element.y;
            div.style.transform = 'translate(-50%, -50%)';

            if (element.type === 'text') {
                div.className += ' text-element';
                div.textContent = element.text;
            } else if (element.type === 'image') {
                div.className += ' image-element';
                const img = document.createElement('img');
                img.src = element.src;
                img.style.width = '100%';
                img.style.height = '100%';
                div.appendChild(img);
            } else if (element.type === 'video') {
                div.className += ' video-element';
                const video = document.createElement('video');
                video.src = element.src;
                video.style.width = '100%';
                video.style.height = '100%';
                video.muted = true;

                // Set video time based on element timing and current time
                if (this.currentTime >= element.start && this.currentTime <= element.end) {
                    video.currentTime = this.currentTime - element.start;
                    div.style.display = 'block';
                } else {
                    div.style.display = 'none';
                }

                div.appendChild(video);
            } else if (element.type === 'audio') {
                div.className += ' audio-element';
                div.innerHTML = `🎵 ${element.fileName || 'Audio'}`;

                // Audio elements are only visible during their time range
                if (this.currentTime >= element.start && this.currentTime <= element.end) {
                    div.style.display = 'flex';
                } else {
                    div.style.display = 'none';
                }
            }

            div.addEventListener('click', () => this.selectElement(element));
            container.appendChild(div);
        });
    }

    toggleTimelineView() {
        this.timelineVisible = !this.timelineVisible;
        const panel = document.getElementById('timelinePanel');

        if (this.timelineVisible) {
            panel.classList.remove('hidden');
            this.updateTimelineView();
        } else {
            panel.classList.add('hidden');
        }
    }

    closeTimelineView() {
        this.timelineVisible = false;
        document.getElementById('timelinePanel').classList.add('hidden');
    }

    updateTimelineZoom(value) {
        this.timelineZoom = parseFloat(value);
        this.updateTimelineView();
    }

    updateTimelineView() {
        if (!this.timelineVisible) return;

        this.renderTimelineRuler();
        this.renderTimelineTracks();
        this.updatePlayhead();
    }

    renderTimelineRuler() {
        const ruler = document.getElementById('timelineRuler');
        ruler.innerHTML = '';

        const pixelsPerSecond = 50 * this.timelineZoom;
        const totalWidth = this.duration * pixelsPerSecond;

        // Create ruler marks
        for (let i = 0; i <= this.duration; i++) {
            const mark = document.createElement('div');
            mark.className = 'timeline-ruler-mark';
            if (i % 5 === 0) mark.classList.add('major');
            mark.style.left = (i * pixelsPerSecond) + 'px';
            ruler.appendChild(mark);

            // Add time labels for major marks
            if (i % 5 === 0) {
                const label = document.createElement('div');
                label.className = 'timeline-ruler-label';
                label.textContent = i + 's';
                label.style.left = (i * pixelsPerSecond) + 'px';
                ruler.appendChild(label);
            }
        }

        ruler.style.width = totalWidth + 'px';
    }

    renderTimelineTracks() {
        const tracksContainer = document.getElementById('timelineTracks');
        const labelsContainer = document.getElementById('trackLabels');

        tracksContainer.innerHTML = '';
        labelsContainer.innerHTML = '';

        const pixelsPerSecond = 50 * this.timelineZoom;

        this.elements.forEach((element, index) => {
            // Create track label
            const label = document.createElement('div');
            label.className = 'track-label';
            label.textContent = `${element.type} ${index + 1}`;
            labelsContainer.appendChild(label);

            // Create track
            const track = document.createElement('div');
            track.className = 'timeline-track';

            // Create timeline element
            const timelineElement = document.createElement('div');
            timelineElement.className = `timeline-element ${element.type}-type`;
            timelineElement.dataset.elementId = element.id;

            const startTime = element.start || 0;
            const endTime = element.end || this.duration;
            const left = startTime * pixelsPerSecond;
            const width = (endTime - startTime) * pixelsPerSecond;

            timelineElement.style.left = left + 'px';
            timelineElement.style.width = width + 'px';
            timelineElement.textContent = element.text || element.fileName || element.type;

            // Add resize handles
            const leftHandle = document.createElement('div');
            leftHandle.className = 'resize-handle left';
            const rightHandle = document.createElement('div');
            rightHandle.className = 'resize-handle right';

            timelineElement.appendChild(leftHandle);
            timelineElement.appendChild(rightHandle);

            // Add event listeners for timeline interaction
            this.setupTimelineElementEvents(timelineElement, element);

            track.appendChild(timelineElement);
            tracksContainer.appendChild(track);
        });

        tracksContainer.style.width = (this.duration * pixelsPerSecond) + 'px';
    }

    setupTimelineElementEvents(timelineElement, element) {
        timelineElement.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('resize-handle')) {
                this.startTimelineResize(e, element, e.target.classList.contains('left') ? 'left' : 'right');
            } else {
                this.startTimelineDrag(e, element);
            }
        });

        timelineElement.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
    }

    startTimelineDrag(e, element) {
        const pixelsPerSecond = 50 * this.timelineZoom;
        const startX = e.clientX;
        const startTime = element.start || 0;

        this.timelineDragState = {
            isDragging: true,
            element: element,
            startTime: startTime,
            isResizing: false,
            startX: startX
        };

        document.addEventListener('mousemove', this.handleTimelineMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleTimelineMouseUp.bind(this));

        e.preventDefault();
    }

    startTimelineResize(e, element, handle) {
        this.timelineDragState = {
            isDragging: true,
            element: element,
            startTime: element.start || 0,
            endTime: element.end || this.duration,
            isResizing: true,
            resizeHandle: handle,
            startX: e.clientX
        };

        document.addEventListener('mousemove', this.handleTimelineMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleTimelineMouseUp.bind(this));

        e.preventDefault();
        e.stopPropagation();
    }

    handleTimelineMouseMove(e) {
        if (!this.timelineDragState.isDragging) return;

        const pixelsPerSecond = 50 * this.timelineZoom;
        const deltaX = e.clientX - this.timelineDragState.startX;
        const deltaTime = deltaX / pixelsPerSecond;

        if (this.timelineDragState.isResizing) {
            if (this.timelineDragState.resizeHandle === 'left') {
                const newStart = Math.max(0, this.timelineDragState.startTime + deltaTime);
                if (newStart < (this.timelineDragState.element.end || this.duration)) {
                    this.timelineDragState.element.start = newStart;
                }
            } else {
                const newEnd = Math.min(this.duration, this.timelineDragState.endTime + deltaTime);
                if (newEnd > (this.timelineDragState.element.start || 0)) {
                    this.timelineDragState.element.end = newEnd;
                }
            }
        } else {
            // Dragging the entire element
            const duration = (this.timelineDragState.element.end || this.duration) - this.timelineDragState.startTime;
            const newStart = Math.max(0, Math.min(this.duration - duration, this.timelineDragState.startTime + deltaTime));

            this.timelineDragState.element.start = newStart;
            this.timelineDragState.element.end = newStart + duration;
        }

        this.updateTimelineView();
        this.updatePropertiesPanel();
        this.renderElements();
    }

    handleTimelineMouseUp(e) {
        this.timelineDragState = {
            isDragging: false,
            element: null,
            startTime: 0,
            isResizing: false,
            resizeHandle: null
        };

        document.removeEventListener('mousemove', this.handleTimelineMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleTimelineMouseUp.bind(this));
    }

    updatePlayhead() {
        if (!this.timelineVisible) return;

        const playhead = document.getElementById('playhead');
        const pixelsPerSecond = 50 * this.timelineZoom;
        const left = this.currentTime * pixelsPerSecond;

        playhead.style.left = left + 'px';
    }

    applyAnimations(element, domElement) {
        element.animations.forEach(animation => {
            const value = this.getInterpolatedValue(animation.keyframes, this.currentTime);
            if (value !== null) {
                if (animation.property === 'x') {
                    domElement.style.left = typeof value === 'string' ? value : value + 'px';
                } else if (animation.property === 'y') {
                    domElement.style.top = typeof value === 'string' ? value : value + 'px';
                } else if (animation.property === 'opacity') {
                    domElement.style.opacity = value;
                } else if (animation.property === 'scale') {
                    domElement.style.transform += ` scale(${value})`;
                } else if (animation.property === 'rotation') {
                    domElement.style.transform += ` rotate(${value}deg)`;
                }
            }
        });
    }

    getInterpolatedValue(keyframes, time) {
        if (keyframes.length === 0) return null;

        // Find surrounding keyframes
        const before = keyframes.filter(kf => kf.time <= time).pop();
        const after = keyframes.find(kf => kf.time > time);

        if (!before && !after) return null;
        if (!before) return after.value;
        if (!after) return before.value;
        if (before.time === after.time) return before.value;

        // Linear interpolation for numbers
        if (typeof before.value === 'number' && typeof after.value === 'number') {
            const progress = (time - before.time) / (after.time - before.time);
            return before.value + (after.value - before.value) * progress;
        }

        // For non-numeric values, return the "before" value
        return before.value;
    }

    highlightPreviewElement(elementId) {
        document.querySelectorAll('.preview-element').forEach(el => {
            el.classList.remove('selected');
        });

        const element = document.querySelector(`[data-element-id="${elementId}"]`);
        if (element) {
            element.classList.add('selected');
        }
    }

    togglePlayback() {
        const button = document.getElementById('playPause');

        if (this.isPlaying) {
            this.isPlaying = false;
            button.textContent = 'Play';
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
            }
        } else {
            this.isPlaying = true;
            button.textContent = 'Pause';
            this.startPlayback();
        }
    }

    startPlayback() {
        const startTime = performance.now();
        const initialTime = this.currentTime;

        const animate = (currentTimestamp) => {
            if (!this.isPlaying) return;

            const elapsed = (currentTimestamp - startTime) / 1000;
            this.currentTime = initialTime + elapsed;

            if (this.currentTime >= this.duration) {
                this.currentTime = this.duration;
                this.isPlaying = false;
                document.getElementById('playPause').textContent = 'Play';
            }

            this.updateTimeline();
            this.updateTimeDisplay();
            this.renderElements();
            this.updatePlayhead();

            if (this.isPlaying) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };

        this.animationFrame = requestAnimationFrame(animate);
    }

    updateTimeline() {
        document.getElementById('timeline').value = this.currentTime;
    }

    updateTimeDisplay() {
        document.getElementById('timeDisplay').textContent =
            `${this.currentTime.toFixed(1)}s / ${this.duration.toFixed(1)}s`;
    }

    loadSampleProject() {
        fetch('./sample-project.json')
            .then(response => response.json())
            .then(data => {
                // Load project settings
                this.duration = data.duration;
                document.getElementById('duration').value = data.duration;
                document.getElementById('width').value = data.width;
                document.getElementById('height').value = data.height;
                document.getElementById('timeline').max = data.duration;

                // Load elements with generated IDs
                this.elements = data.elements.map(element => ({
                    ...element,
                    id: this.generateId()
                }));

                // Reset timeline
                this.currentTime = 0;
                this.updateTimeline();
                this.updateTimeDisplay();

                // Update UI
                this.selectedElement = null;
                this.updateElementsList();
                this.updatePropertiesPanel();
                this.renderElements();

                console.log('Sample project loaded successfully');
            })
            .catch(error => {
                console.error('Error loading sample project:', error);
                alert('Could not load sample project. Make sure sample-project.json exists.');
            });
    }

    clearProject() {
        if (this.elements.length > 0) {
            const confirmed = confirm('Are you sure you want to clear the project? This will remove all elements.');
            if (!confirmed) return;
        }

        // Reset everything
        this.elements = [];
        this.selectedElement = null;
        this.currentTime = 0;
        this.isPlaying = false;

        // Reset UI
        document.getElementById('playPause').textContent = 'Play';
        this.updateTimeline();
        this.updateTimeDisplay();
        this.updateElementsList();
        this.updatePropertiesPanel();
        this.renderElements();

        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        console.log('Project cleared');
    }

    exportToJson() {
        const width = parseInt(document.getElementById('width').value);
        const height = parseInt(document.getElementById('height').value);

        const json = {
            output_format: "mp4",
            width: width,
            height: height,
            duration: this.duration,
            elements: this.elements.map(element => {
                // Create a clean copy without internal IDs
                const cleanElement = { ...element };
                delete cleanElement.id;
                return cleanElement;
            })
        };

        const blob = new Blob([JSON.stringify(json, null, 2)], {
            type: "application/json"
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "video_project.json";
        a.click();
        URL.revokeObjectURL(url);

        console.log('Exported JSON:', json);
    }
}

// Initialize the editor when the page loads
let editor;
document.addEventListener('DOMContentLoaded', () => {
    editor = new VideoEditor();
});
