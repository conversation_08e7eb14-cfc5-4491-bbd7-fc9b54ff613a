import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal, <PERSON>, Badge,
  OverlayTrigger, Tooltip, InputGroup, Table, Card
} from 'react-bootstrap';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Info as InfoIcon,
  AutoAwesome as AutoAwesomeIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import {
  OUTPUT_FORMAT_TEMPLATES,
  STANDARD_OUTPUT_FORMAT,
  VIDEO_FORMAT_OPTIONS,
  TONE_OPTIONS,
  GOAL_OPTIONS,
  TARGET_AUDIENCE_OPTIONS,
  FORMAT_OPTIONS,
  DEFAULT_CONTENT_PROMPT
} from '../config/promptTemplates';
import { generateContentPrompt, checkGrokAvailability } from '../services/grokService';
import { API_BASE_URL } from '../constants';

const ContentManagementCRUD = ({ setParentError }) => {
  // State for content prompts data
  const [contentPrompts, setContentPrompts] = useState([]);
  const [contentPromptTopics, setContentPromptTopics] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for search
  const [searchTerm, setSearchTerm] = useState('');

  // State for CRUD operations
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showOutputFormatInfo, setShowOutputFormatInfo] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState(null);
  const [formData, setFormData] = useState({ ...DEFAULT_CONTENT_PROMPT });
  const [formErrors, setFormErrors] = useState({});
  const [actionSuccess, setActionSuccess] = useState(null);
  const [actionError, setActionError] = useState(null);

  // State for Grok integration
  const [isGrokAvailable, setIsGrokAvailable] = useState(false);
  const [isGeneratingWithGrok, setIsGeneratingWithGrok] = useState(false);
  const [grokDescription, setGrokDescription] = useState('');

  // State for Claude integration
  const [isGeneratingClaudePrompt, setIsGeneratingClaudePrompt] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);

  // Ref to track form data
  const formDataRef = useRef(DEFAULT_CONTENT_PROMPT);

  // State for category titles
  const [categoryTitles, setCategoryTitles] = useState([]);
  const [loadingTitles, setLoadingTitles] = useState(false);
  const [generatingTitles, setGeneratingTitles] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchContentPrompts();
    fetchContentPromptTopics();
    fetchCategories();
    checkGrokStatus();
  }, []);

  // Debug: Log form data changes
  useEffect(() => {
    console.log('Form data changed:', formData);
    console.log('Content prompt in form data:', formData.content_prompt);

    // Update the DOM directly with the content_prompt value
    const contentPromptTextarea = document.querySelector('textarea[name="content_prompt"]');
    if (contentPromptTextarea && formData.content_prompt && contentPromptTextarea.value !== formData.content_prompt) {
      console.log('Updating content_prompt textarea value from useEffect');
      contentPromptTextarea.value = formData.content_prompt;
    }
  }, [formData]);

  // Check if Grok API is available
  const checkGrokStatus = async () => {
    try {
      const isAvailable = await checkGrokAvailability();
      console.log('Grok availability:', isAvailable);
      setIsGrokAvailable(true); // Force enable for testing
    } catch (error) {
      console.error('Error checking Grok availability:', error);
      setIsGrokAvailable(true); // Force enable for testing
    }
  };

  // Fetch titles for a specific category
  const fetchCategoryTitles = async (categoryId) => {
    if (!categoryId) return;

    setLoadingTitles(true);
    setCategoryTitles([]);
    setActionError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/category-titles/${categoryId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch titles');
      }

      const data = await response.json();
      console.log('Fetched titles:', data);
      setCategoryTitles(data);
    } catch (error) {
      console.error('Error fetching category titles:', error);
      setActionError(`Error fetching titles: ${error.message}`);
    } finally {
      setLoadingTitles(false);
    }
  };

  // Generate titles for a specific category
  const generateTitlesForCategory = async (categoryId) => {
    if (!categoryId) return;

    setGeneratingTitles(true);
    setActionError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/generate-titles/${categoryId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate titles');
      }

      const data = await response.json();
      console.log('Generated titles:', data);

      // Refresh the titles list
      await fetchCategoryTitles(categoryId);

      setActionSuccess(`Successfully generated ${data.titles.length} new titles for ${data.category}`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating titles:', error);
      setActionError(`Error generating titles: ${error.message}`);
    } finally {
      setGeneratingTitles(false);
    }
  };

  // Mark a title as used
  const markTitleAsUsed = async (titleId) => {
    if (!titleId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/category-titles/${titleId}/mark-used`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark title as used');
      }

      // Update the local state to mark the title as used
      setCategoryTitles(prevTitles =>
        prevTitles.map(title =>
          title.id === titleId ? { ...title, is_used: true } : title
        )
      );
    } catch (error) {
      console.error('Error marking title as used:', error);
      // We don't show this error to the user to avoid disrupting the flow
    }
  };

  // Fetch content prompt topics from API
  const fetchContentPromptTopics = async () => {
    try {
      console.log('Fetching content prompt topics...');
      const response = await fetch(`${API_BASE_URL}/api/content-prompts/topics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch content prompt topics: ${response.statusText}`);
      }
      const data = await response.json();
      console.log('Content prompt topics:', data);
      setContentPromptTopics(data);
    } catch (error) {
      console.error('Error fetching content prompt topics:', error);
      setError(error.message);
    }
  };

  // Fetch content prompts from API
  const fetchContentPrompts = async () => {
    try {
      console.log('Fetching content prompts...');
      setLoading(true);
      setError(null);
      if (setParentError) setParentError(null);

      try {
        const response = await fetch(`${API_BASE_URL}/api/content-prompts`);

        // Even if we get a 500 error, try to parse the response
        const data = await response.json();
        console.log('Fetched data:', data);

        if (!response.ok) {
          // If the server returned an error message, display it
          if (data && data.error) {
            throw new Error(data.error);
          } else {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        }

        // If we got here, the response was successful
        setContentPrompts(Array.isArray(data) ? data : []);
      } catch (fetchError) {
        console.error('Error fetching content:', fetchError);

        // If the API is not available, show a message and use empty data
        console.log('API might not be available, using empty data');
        setContentPrompts([]);
        const errorMsg = 'API is not available. Please make sure the backend server is running.';
        setError(errorMsg);
        if (setParentError) setParentError(errorMsg);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      const errorMsg = err.message;
      setError(errorMsg);
      if (setParentError) setParentError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/categories`);

      if (!response.ok) {
        console.error('Error fetching categories');
        return;
      }

      const data = await response.json();
      setCategories(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Fetch content prompt by ID
  const fetchContentPromptById = async (id) => {
    try {
      console.log(`Fetching content prompt with ID: ${id}`);
      const response = await fetch(`${API_BASE_URL}/api/content-prompts/${id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch content prompt: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched content prompt:', data);

      // Update the form data with the fetched content prompt
      setFormData({
        ...formData,
        topic: data.title || '',
        title: data.title || '',
        content_type: data.content_type || 'blog_post',
        purpose: data.purpose || 'inform',
        audience: data.audience || 'general',
        tone: data.tone || 'conversational',
        style: data.style || 'descriptive',
        word_count: data.word_count || '500',
        persona: data.persona || 'expert',
        content_prompt: data.content_prompt || '',
        call_to_action: data.call_to_action || '',
        output_format: data.output_format || 'standard'
      });

      return data;
    } catch (error) {
      console.error('Error fetching content prompt:', error);
      setError(error.message);
      return null;
    }
  };

  // Helper function to update content prompt
  const updateContentPrompt = (content, callToAction) => {
    console.log('Updating content prompt with:', content);
    console.log('Updating call to action with:', callToAction);

    // Update the DOM directly
    const contentPromptTextarea = document.querySelector('textarea[name="content_prompt"]');
    if (contentPromptTextarea) {
      console.log('Found content_prompt textarea, setting value directly');
      contentPromptTextarea.value = content;
    } else {
      console.log('Could not find content_prompt textarea');
    }

    // Update the form data
    const updatedFormData = {
      ...formData,
      content_prompt: content,
      call_to_action: callToAction
    };

    // Update the ref
    formDataRef.current = updatedFormData;

    // Force update the form data
    setFormData(updatedFormData);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newFormData = {
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    };

    // Sync title and topic fields
    if (name === 'topic') {
      newFormData.title = value;  // Update title when topic changes
    }

    setFormData(newFormData);

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Handle title selection
  const handleTitleSelect = (titleId, titleText) => {
    // Update form data with selected title
    setFormData({
      ...formData,
      title: titleText
    });

    // Mark the title as used
    markTitleAsUsed(titleId);

    // Clear validation error for title if any
    if (formErrors.title) {
      setFormErrors({
        ...formErrors,
        title: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = {};

    // Validate Claude format fields
    if (!formData.topic.trim()) {
      errors.topic = 'Topic is required';
    }

    if (!formData.content_type.trim()) {
      errors.content_type = 'Content type is required';
    }

    if (!formData.purpose.trim()) {
      errors.purpose = 'Purpose is required';
    }

    if (!formData.audience.trim()) {
      errors.audience = 'Audience is required';
    }

    if (!formData.tone.trim()) {
      errors.tone = 'Tone is required';
    }

    // Common validations
    if (!formData.category_id) {
      errors.category_id = 'Category is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Get output format template text
  const getOutputFormatTemplate = (formatKey) => {
    return OUTPUT_FORMAT_TEMPLATES[formatKey]?.template ||
           OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].template;
  };

  // Handle add prompt
  const handleAddPrompt = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setActionError(null);

      const response = await fetch(`${API_BASE_URL}/api/content-prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add content prompt');
      }

      // Success
      setActionSuccess('Content prompt added successfully!');
      setShowAddModal(false);

      // Reset form data
      setFormData({ ...DEFAULT_CONTENT_PROMPT });

      // Refresh content prompts
      fetchContentPrompts();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error adding content prompt:', error);
      setActionError(error.message);
    }
  };

  // Handle edit prompt
  const handleEditPrompt = async () => {
    if (!validateForm() || !currentPrompt) {
      return;
    }

    try {
      setActionError(null);

      const response = await fetch(`${API_BASE_URL}/api/content-prompts/${currentPrompt.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update content prompt');
      }

      // Success
      setActionSuccess('Content prompt updated successfully!');
      setShowEditModal(false);

      // Refresh content prompts
      fetchContentPrompts();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating content prompt:', error);
      setActionError(error.message);
    }
  };

  // Handle delete prompt
  const handleDeletePrompt = async () => {
    if (!currentPrompt) {
      return;
    }

    try {
      setActionError(null);

      const response = await fetch(`${API_BASE_URL}/api/content-prompts/${currentPrompt.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete content prompt');
      }

      // Success
      setActionSuccess('Content prompt deleted successfully!');
      setShowDeleteModal(false);

      // Refresh content prompts
      fetchContentPrompts();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error deleting content prompt:', error);
      setActionError(error.message);
    }
  };

  // Open edit modal with current prompt data
  const openEditModal = (prompt) => {
    setCurrentPrompt(prompt);

    // Set form data for Claude format
    setFormData({
      // Common fields
      title: prompt.title || '',  // This is actually the topic field renamed as title in the API
      topic: prompt.title || '',  // Use the same value for topic
      category_id: prompt.category_id || '',
      call_to_action: prompt.call_to_action || '',
      output_format: prompt.output_format || STANDARD_OUTPUT_FORMAT,

      // Claude-specific fields
      content_type: prompt.content_type || 'blog_post',
      purpose: prompt.purpose || 'inform',
      audience: prompt.audience || prompt.target_audience || 'general',
      tone: prompt.tone || 'conversational',
      style: prompt.style || 'descriptive',
      word_count: prompt.word_count || '500',
      persona: prompt.persona || 'expert'
    });

    setShowEditModal(true);
  };

  // Open delete modal with current prompt
  const openDeleteModal = (prompt) => {
    setCurrentPrompt(prompt);
    setShowDeleteModal(true);
  };

  // Open view modal with current prompt
  const openViewModal = (prompt) => {
    // Check if this is a Claude format prompt (has content_type field)
    const isClaudeFormat = prompt.content_type !== undefined;

    // If it's a Claude format prompt, add a flag to indicate this
    if (isClaudeFormat) {
      setCurrentPrompt({
        ...prompt,
        isClaudeFormat: true
      });
    } else {
      setCurrentPrompt(prompt);
    }

    setShowViewModal(true);
  };

  // Generate content with Grok
  const handleGenerateContent = async () => {
    if (!formData.topic) {
      setActionError('Please enter a topic');
      return;
    }

    if (!formData.category_id) {
      setActionError('Please select a category');
      return;
    }

    setIsGeneratingContent(true);
    setActionError(null);

    try {
      const requestData = {
        prompt_type: 'content_generation',
        topic: formData.topic,
        content_type: formData.content_type,
        purpose: formData.purpose,
        audience: formData.audience,
        tone: formData.tone,
        style: formData.style,
        word_count: formData.word_count,
        persona: formData.persona
      };

      console.log('Sending content generation request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate content: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      // Log the data received from the API
      console.log('Content prompt from API:', data.content_prompt);
      console.log('Call to action from API:', data.call_to_action);

      // Log the current form data before update
      console.log('Current form data before update:', formData);
      console.log('Current content_prompt before update:', formData.content_prompt);

      // Get the content_prompt and call_to_action values from the API response
      const contentPromptValue = data.content_prompt || '';
      const callToActionValue = data.call_to_action || '';

      console.log('Content prompt value to set:', contentPromptValue);
      console.log('Call to action value to set:', callToActionValue);

      // Use the helper function to update the content prompt
      updateContentPrompt(contentPromptValue, callToActionValue);

      // Double-check the form data after update
      setTimeout(() => {
        console.log('Form data after update:', formData);
        console.log('Content prompt after update:', formData.content_prompt);

        // Force another update if needed
        if (formData.content_prompt !== contentPromptValue) {
          console.log('Forcing another update because content_prompt is not updated');
          updateContentPrompt(contentPromptValue, callToActionValue);
        }
      }, 100);

      setActionSuccess('Content generated successfully with Grok!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating content with Grok:', error);
      setActionError(error.message || 'Failed to generate content with Grok');
    } finally {
      setIsGeneratingContent(false);
    }
  };

  // Generate content prompt with Claude
  const handleGenerateClaudePrompt = async () => {
    if (!formData.topic) {
      setActionError('Please enter a topic');
      return;
    }

    if (!formData.category_id) {
      setActionError('Please select a category');
      return;
    }

    setIsGeneratingClaudePrompt(true);
    setActionError(null);

    try {
      const requestData = {
        prompt_type: 'claude_prompt',
        topic: formData.topic,
        content_type: formData.content_type,
        purpose: formData.purpose,
        audience: formData.audience,
        tone: formData.tone,
        style: formData.style,
        word_count: formData.word_count,
        persona: formData.persona
      };

      console.log('Sending request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate prompt: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      // Log the data received from the API
      console.log('Content prompt from API:', data.content_prompt);
      console.log('Call to action from API:', data.call_to_action);

      // Get the content_prompt and call_to_action values from the API response
      const contentPromptValue = data.content_prompt || '';
      const callToActionValue = data.call_to_action || '';

      console.log('Content prompt value to set:', contentPromptValue);
      console.log('Call to action value to set:', callToActionValue);

      // Use the helper function to update the content prompt
      updateContentPrompt(contentPromptValue, callToActionValue);

      // Double-check the form data after update
      setTimeout(() => {
        console.log('Form data after update:', formData);
        console.log('Content prompt after update:', formData.content_prompt);

        // Force another update if needed
        if (formData.content_prompt !== contentPromptValue) {
          console.log('Forcing another update because content_prompt is not updated');
          updateContentPrompt(contentPromptValue, callToActionValue);
        }
      }, 100);

      setActionSuccess('Claude-optimized prompt generated successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating with Claude:', error);
      setActionError(error.message || 'Failed to generate content with Claude');
    } finally {
      setIsGeneratingClaudePrompt(false);
    }
  };

  // Generate content prompt with Grok
  const handleGenerateWithGrok = async () => {
    console.log('Generating with Grok...');
    console.log('Form data:', formData);

    if (!formData.title || !formData.category_id) {
      console.log('Missing required fields for Grok generation');
      setFormErrors({
        ...formErrors,
        title: !formData.title ? 'Title is required for Grok generation' : null,
        category_id: !formData.category_id ? 'Category is required for Grok generation' : null
      });
      return;
    }

    setIsGeneratingWithGrok(true);
    setActionError(null);

    try {
      // Get category name
      const category = categories.find(cat => cat.id.toString() === formData.category_id.toString());
      const categoryName = category ? category.name : '';
      console.log('Category name:', categoryName);

      console.log('Calling Grok API with:', {
        title: formData.title,
        category: categoryName,
        description: grokDescription
      });

      const result = await generateContentPrompt({
        title: formData.title,
        category: categoryName,
        description: grokDescription
      });

      console.log('Grok API response:', result);

      setFormData({
        ...formData,
        content_prompt: result.content_prompt,
        call_to_action: result.call_to_action || formData.call_to_action
      });

      setActionSuccess('Content prompt generated successfully with Grok!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setActionSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error generating with Grok:', error);
      setActionError(error.message || 'Failed to generate content with Grok');
    } finally {
      setIsGeneratingWithGrok(false);
    }
  };

  // Reset form data when opening add modal
  const openAddModal = () => {
    setFormData({ ...DEFAULT_CONTENT_PROMPT });
    setFormErrors({});
    setGrokDescription('');
    setActionError(null);
    setActionSuccess(null);
    setShowAddModal(true);
  };

  // Load data on component mount
  useEffect(() => {
    fetchContentPrompts();
    fetchCategories();
    fetchCategoryTitles();
  }, []);





  return (
    <>
      <Container fluid>
        <Row className="mb-4 d-flex align-items-center">
          <Col md={6}>
            <h1>Content Prompts</h1>
          </Col>
          <Col md={6} className="d-flex justify-content-end">
            <InputGroup className="me-2" style={{ maxWidth: '300px' }}>
              <InputGroup.Text>
                <i className="bi bi-search"></i>
              </InputGroup.Text>
              <Form.Control
                placeholder="Search by title or category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
            <Button
              variant="primary"
              onClick={openAddModal}
              className="d-flex align-items-center"
            >
              <AddIcon className="me-2" />
              Add New Prompt
            </Button>
          </Col>
        </Row>

        {actionSuccess && (
          <Row className="mb-4">
            <Col>
              <Alert variant="success" onClose={() => setActionSuccess(null)} dismissible>
                {actionSuccess}
              </Alert>
            </Col>
          </Row>
        )}

        {loading ? (
          <div className="text-center my-5">
            <Spinner animation="border" role="status">
              <span className="visually-hidden">Loading...</span>
            </Spinner>
          </div>
        ) : error ? (
          <>
            <Alert variant="danger">
              <Alert.Heading>Error</Alert.Heading>
              <p>{error}</p>
            </Alert>
            <div className="mt-4">
              <h5>Troubleshooting Steps:</h5>
              <ol>
                <li>Make sure the backend API server is running</li>
                <li>Check if the database is properly configured</li>
                <li>Verify that the required tables exist in the database</li>
                <li>Check the API logs for more detailed error information</li>
              </ol>
            </div>
          </>
        ) : (
          <div className="table-responsive">
            <Table striped bordered hover>
              <thead>
                <tr>
                  <th style={{ width: '5%' }}>ID</th>
                  <th style={{ width: '25%' }}>Title</th>
                  <th style={{ width: '15%' }}>Category</th>
                  <th style={{ width: '15%' }}>Format</th>
                  <th style={{ width: '15%' }}>Generated At</th>
                  <th style={{ width: '25%' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {contentPrompts.length > 0 ? (
                  contentPrompts
                    .filter(prompt =>
                      searchTerm === '' ||
                      prompt.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      prompt.category_name?.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((prompt) => (
                      <tr key={prompt.id}>
                        <td>{prompt.id}</td>
                        <td>{prompt.title}</td>
                        <td>
                          {prompt.category_name ? (
                            <Badge bg="info" pill>{prompt.category_name}</Badge>
                          ) : (
                            <Badge bg="secondary" pill>None</Badge>
                          )}
                        </td>
                        <td>
                          <div>
                            <Badge bg="primary" className="me-1">{prompt.output_format}</Badge>
                            <Badge bg="success">{prompt.video_format}</Badge>
                          </div>
                          <div className="mt-1">
                            <small>Sentences: {prompt.num_sentences}</small>
                          </div>
                        </td>
                        <td>
                          {prompt.generated_at ? new Date(prompt.generated_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td>
                          <Button
                            variant="outline-info"
                            size="sm"
                            className="me-2"
                            onClick={() => openViewModal(prompt)}
                          >
                            <ViewIcon fontSize="small" />
                          </Button>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            className="me-2"
                            onClick={() => openEditModal(prompt)}
                          >
                            <EditIcon fontSize="small" />
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => openDeleteModal(prompt)}
                          >
                            <DeleteIcon fontSize="small" />
                          </Button>
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={6} className="text-center">
                      {searchTerm ? 'No content prompts match your search' : 'No content prompts found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </Table>
          </div>
        )}
      </Container>

      {/* Add Prompt Modal */}
      <Modal show={showAddModal} onHide={() => setShowAddModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Content Prompt</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" className="mb-3">
              {actionError}
            </Alert>
          )}
          {actionSuccess && (
            <Alert variant="success" className="mb-3">
              {actionSuccess}
            </Alert>
          )}
          <Form>
            <Row>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Select Existing Content Prompt <span className="text-danger">*</span></Form.Label>
                  <Form.Select
                    name="existing_prompt_id"
                    onChange={(e) => {
                      if (e.target.value) {
                        fetchContentPromptById(e.target.value);
                      } else {
                        // Reset form data if "Select a prompt" is chosen
                        setFormData({ ...DEFAULT_CONTENT_PROMPT });
                      }
                    }}
                  >
                    <option value="">Select a prompt</option>
                    {contentPromptTopics.map(prompt => (
                      <option key={prompt.id} value={prompt.id}>
                        {prompt.topic}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Select an existing content prompt to view its details.
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            {/* Content Prompt Details */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Content Prompt Details</h6>
              </Card.Header>
              <Card.Body>
                {formData.content_prompt ? (
                  <div>
                    <h6>Content Prompt:</h6>
                    <pre className="p-3 bg-light border rounded" style={{ whiteSpace: 'pre-wrap' }}>
                      {formData.content_prompt}
                    </pre>

                    <h6 className="mt-4">Call to Action:</h6>
                    <pre className="p-3 bg-light border rounded" style={{ whiteSpace: 'pre-wrap' }}>
                      {formData.call_to_action || 'No call to action provided.'}
                    </pre>
                  </div>
                ) : (
                  <Alert variant="info">
                    Select a content prompt from the dropdown above to view its details.
                  </Alert>
                )}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Content Type <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        name="content_type"
                        value={formData.content_type}
                        onChange={handleInputChange}
                        isInvalid={!!formErrors.content_type}
                      >
                        <option value="">Select Content Type</option>
                        <option value="blog_post">Blog Post/Article</option>
                        <option value="video_script">Video Script</option>
                        <option value="story">Story/Narrative</option>
                        <option value="essay">Essay/Academic Writing</option>
                        <option value="social_media">Social Media Content</option>
                        <option value="marketing_copy">Marketing Copy</option>
                        <option value="educational_content">Educational Content</option>
                        <option value="vlog_script">Vlog Script</option>
                        <option value="analysis">Analysis/Commentary</option>
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {formErrors.content_type}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Purpose <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        name="purpose"
                        value={formData.purpose}
                        onChange={handleInputChange}
                        isInvalid={!!formErrors.purpose}
                      >
                        <option value="">Select Purpose</option>
                        <option value="inform">Inform/Educate</option>
                        <option value="persuade">Persuade/Convince</option>
                        <option value="entertain">Entertain</option>
                        <option value="inspire">Inspire/Motivate</option>
                        <option value="analyze">Analyze/Examine</option>
                        <option value="instruct">Instruct/Guide</option>
                        <option value="storytell">Tell a Story</option>
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {formErrors.purpose}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Audience <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        name="audience"
                        value={formData.audience}
                        onChange={handleInputChange}
                        isInvalid={!!formErrors.audience}
                      >
                        <option value="">Select Audience</option>
                        {TARGET_AUDIENCE_OPTIONS.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {formErrors.audience}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Tone <span className="text-danger">*</span></Form.Label>
                      <Form.Select
                        name="tone"
                        value={formData.tone}
                        onChange={handleInputChange}
                        isInvalid={!!formErrors.tone}
                      >
                        <option value="">Select Tone</option>
                        {TONE_OPTIONS.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {formErrors.tone}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Style</Form.Label>
                      <Form.Select
                        name="style"
                        value={formData.style}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Style</option>
                        <option value="descriptive">Descriptive</option>
                        <option value="narrative">Narrative</option>
                        <option value="analytical">Analytical</option>
                        <option value="persuasive">Persuasive</option>
                        <option value="conversational">Conversational</option>
                        <option value="technical">Technical</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Word Count</Form.Label>
                      <Form.Select
                        name="word_count"
                        value={formData.word_count}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Word Count</option>
                        <option value="100">100 words (very short)</option>
                        <option value="200">200 words (short)</option>
                        <option value="500">500 words (medium)</option>
                        <option value="1000">1000 words (long)</option>
                        <option value="1500">1500 words (very long)</option>
                        <option value="custom">Custom length</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Persona</Form.Label>
                      <Form.Select
                        name="persona"
                        value={formData.persona}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Persona</option>
                        <option value="expert">Expert</option>
                        <option value="coach">Coach</option>
                        <option value="friend">Friend</option>
                        <option value="teacher">Teacher</option>
                        <option value="storyteller">Storyteller</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            <Form.Group className="mb-3">
              <Form.Label>Content Prompt</Form.Label>
              <Form.Control
                as="textarea"
                rows={5}
                name="content_prompt"
                value={formData.content_prompt}
                onChange={handleInputChange}
                placeholder="Generated content prompt will appear here"
              />
              <Form.Text className="text-muted">
                This is the main content prompt that will be used to generate content.
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Call to Action</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="call_to_action"
                value={formData.call_to_action}
                onChange={handleInputChange}
                placeholder="Enter a call to action for your content (optional)"
              />
              <Form.Text className="text-muted">
                Add a call to action to encourage user engagement.
              </Form.Text>
            </Form.Group>

            {/* Output Format field removed */}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleAddPrompt}>
            Add Prompt
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Prompt Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Content Prompt</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" className="mb-3">
              {actionError}
            </Alert>
          )}
          <Form>
            <Row>
              <Col md={8}>
                <Form.Group className="mb-3">
                  <Form.Label>Title <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.title}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formErrors.title}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Category <span className="text-danger">*</span></Form.Label>
                  <Form.Select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.category_id}
                  >
                    <option value="">Select Category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {formErrors.category_id}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>

            {/* Show different fields based on format */}
            {formData.content_type ? (
              <>
                {/* Claude Format Fields */}
                <Card className="mb-3">
                  <Card.Header className="bg-light">
                    <h6 className="mb-0">Claude Prompt Configuration</h6>
                  </Card.Header>
                  <Card.Body>
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Content Type <span className="text-danger">*</span></Form.Label>
                          <Form.Select
                            name="content_type"
                            value={formData.content_type}
                            onChange={handleInputChange}
                            isInvalid={!!formErrors.content_type}
                          >
                            <option value="">Select Content Type</option>
                            <option value="blog_post">Blog Post/Article</option>
                            <option value="video_script">Video Script</option>
                            <option value="story">Story/Narrative</option>
                            <option value="essay">Essay/Academic Writing</option>
                            <option value="social_media">Social Media Content</option>
                            <option value="marketing_copy">Marketing Copy</option>
                            <option value="educational_content">Educational Content</option>
                            <option value="vlog_script">Vlog Script</option>
                            <option value="analysis">Analysis/Commentary</option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {formErrors.content_type}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Purpose <span className="text-danger">*</span></Form.Label>
                          <Form.Select
                            name="purpose"
                            value={formData.purpose}
                            onChange={handleInputChange}
                            isInvalid={!!formErrors.purpose}
                          >
                            <option value="">Select Purpose</option>
                            <option value="inform">Inform/Educate</option>
                            <option value="persuade">Persuade/Convince</option>
                            <option value="entertain">Entertain</option>
                            <option value="inspire">Inspire/Motivate</option>
                            <option value="analyze">Analyze/Examine</option>
                            <option value="instruct">Instruct/Guide</option>
                            <option value="storytell">Tell a Story</option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {formErrors.purpose}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Audience <span className="text-danger">*</span></Form.Label>
                          <Form.Select
                            name="audience"
                            value={formData.audience}
                            onChange={handleInputChange}
                            isInvalid={!!formErrors.audience}
                          >
                            <option value="">Select Audience</option>
                            <option value="general">General Audience</option>
                            <option value="beginners">Beginners</option>
                            <option value="intermediate">Intermediate Level</option>
                            <option value="advanced">Advanced/Experts</option>
                            <option value="professionals">Professionals</option>
                            <option value="students">Students</option>
                            <option value="millennials">Millennials</option>
                            <option value="gen_z">Gen Z</option>
                            <option value="business_owners">Business Owners</option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {formErrors.audience}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Tone <span className="text-danger">*</span></Form.Label>
                          <Form.Select
                            name="tone"
                            value={formData.tone}
                            onChange={handleInputChange}
                            isInvalid={!!formErrors.tone}
                          >
                            <option value="">Select Tone</option>
                            <option value="conversational">Conversational</option>
                            <option value="formal">Formal</option>
                            <option value="educational">Educational</option>
                            <option value="entertaining">Entertaining</option>
                            <option value="professional">Professional</option>
                            <option value="humorous">Humorous</option>
                            <option value="inspirational">Inspirational</option>
                            <option value="empathetic">Empathetic</option>
                            <option value="critical">Critical</option>
                            <option value="scholarly">Scholarly</option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {formErrors.tone}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Style</Form.Label>
                          <Form.Select
                            name="style"
                            value={formData.style}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Style</option>
                            <option value="descriptive">Descriptive</option>
                            <option value="minimalist">Minimalist</option>
                            <option value="analytical">Analytical</option>
                            <option value="narrative">Narrative</option>
                            <option value="persuasive">Persuasive</option>
                            <option value="technical">Technical</option>
                            <option value="creative">Creative</option>
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Word Count</Form.Label>
                          <Form.Select
                            name="word_count"
                            value={formData.word_count}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Word Count</option>
                            <option value="100">100 words (Very Short)</option>
                            <option value="200">200 words (Brief)</option>
                            <option value="300">300 words (Short)</option>
                            <option value="500">500 words (Medium)</option>
                            <option value="1000">1000 words (Long)</option>
                            <option value="1500">1500 words (Very Long)</option>
                            <option value="2000">2000 words (Comprehensive)</option>
                            <option value="custom">Custom word count</option>
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>Persona</Form.Label>
                          <Form.Select
                            name="persona"
                            value={formData.persona}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Persona</option>
                            <option value="expert">Subject Matter Expert</option>
                            <option value="journalist">Journalist</option>
                            <option value="teacher">Teacher/Educator</option>
                            <option value="storyteller">Storyteller</option>
                            <option value="coach">Coach/Mentor</option>
                            <option value="analyst">Analyst</option>
                            <option value="researcher">Researcher</option>
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>

                <div className="d-grid gap-2 mb-3">
                  <Row>
                    <Col md={6}>
                      <Button
                        variant="primary"
                        onClick={handleGenerateClaudePrompt}
                        disabled={isGeneratingClaudePrompt}
                        className="w-100"
                      >
                        {isGeneratingClaudePrompt ? (
                          <>
                            <Spinner
                              as="span"
                              animation="border"
                              size="sm"
                              role="status"
                              aria-hidden="true"
                              className="me-2"
                            />
                            Generating Prompt...
                          </>
                        ) : (
                          <>Generate Claude-Optimized Prompt</>
                        )}
                      </Button>
                    </Col>
                    <Col md={6}>
                      <Button
                        variant="success"
                        onClick={handleGenerateContent}
                        disabled={isGeneratingContent}
                        className="w-100"
                      >
                        {isGeneratingContent ? (
                          <>
                            <Spinner
                              as="span"
                              animation="border"
                              size="sm"
                              role="status"
                              aria-hidden="true"
                              className="me-2"
                            />
                            Generating Content...
                          </>
                        ) : (
                          <>Generate Content</>
                        )}
                      </Button>
                    </Col>
                  </Row>
                </div>
              </>
            ) : (
              <>
                {/* Original Format Fields */}
                <Form.Group className="mb-3">
                  <Form.Label>Content Prompt <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={5}
                    name="content_prompt"
                    value={formData.content_prompt}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.content_prompt}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formErrors.content_prompt}
                  </Form.Control.Feedback>
                </Form.Group>
              </>
            )}

            <Form.Group className="mb-3">
              <Form.Label>Call to Action</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="call_to_action"
                value={formData.call_to_action}
                onChange={handleInputChange}
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label className="d-flex align-items-center">
                    Output Format
                    <OverlayTrigger
                      placement="top"
                      overlay={<Tooltip>Standard output format used for all content</Tooltip>}
                    >
                      <InfoIcon fontSize="small" className="ms-2" style={{ cursor: 'pointer', color: '#6c757d' }} />
                    </OverlayTrigger>
                  </Form.Label>
                  <Form.Control
                    type="text"
                    name="output_format"
                    value={OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].name}
                    readOnly
                    disabled
                  />
                  <Form.Text className="text-muted">
                    <Button
                      variant="link"
                      className="p-0"
                      onClick={() => setShowOutputFormatInfo(true)}
                    >
                      View format details
                    </Button>
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Voice ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="voice_id"
                    value={formData.voice_id}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Video Format</Form.Label>
                  <Form.Select
                    name="video_format"
                    value={formData.video_format}
                    onChange={handleInputChange}
                  >
                    {VIDEO_FORMAT_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Tone</Form.Label>
                  <Form.Select
                    name="tone"
                    value={formData.tone}
                    onChange={handleInputChange}
                  >
                    {TONE_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Select the tone for your content
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Goal</Form.Label>
                  <Form.Select
                    name="goal"
                    value={formData.goal}
                    onChange={handleInputChange}
                  >
                    {GOAL_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    What do you want the content to achieve?
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Target Audience</Form.Label>
                  <Form.Select
                    name="target_audience"
                    value={formData.target_audience}
                    onChange={handleInputChange}
                  >
                    {TARGET_AUDIENCE_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Who is this content for?
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Format</Form.Label>
                  <Form.Select
                    name="format_type"
                    value={formData.format_type}
                    onChange={handleInputChange}
                  >
                    {FORMAT_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </Form.Select>
                  <Form.Text className="text-muted">
                    Select the format structure for your content
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Number of Sentences</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    name="num_sentences"
                    value={formData.num_sentences}
                    onChange={handleInputChange}
                    isInvalid={!!formErrors.num_sentences}
                  />
                  <Form.Control.Feedback type="invalid">
                    {formErrors.num_sentences}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3 mt-4">
                  <Form.Check
                    type="checkbox"
                    label="Multiple Topics"
                    name="multiple_topics"
                    checked={formData.multiple_topics}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleEditPrompt}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {actionError && (
            <Alert variant="danger" className="mb-3">
              {actionError}
            </Alert>
          )}
          <p>Are you sure you want to delete the content prompt: <strong>{currentPrompt?.title}</strong>?</p>
          <p className="text-danger">This action cannot be undone.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeletePrompt}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Prompt Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            View Content Prompt
            {currentPrompt?.isClaudeFormat && (
              <Badge bg="primary" className="ms-2">Claude Format</Badge>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentPrompt && (
            <div>
              <Row className="mb-3">
                <Col md={8}>
                  <h5>{currentPrompt.title}</h5>
                </Col>
                <Col md={4} className="text-end">
                  <Badge bg="info" pill>{currentPrompt.category_name || 'No Category'}</Badge>
                </Col>
              </Row>

              {/* Show different content based on format */}
              {currentPrompt.isClaudeFormat ? (
                <>
                  {/* Claude Format Fields */}
                  <Card className="mb-3">
                    <Card.Header className="bg-light">
                      <h6 className="mb-0">Claude Prompt Configuration</h6>
                    </Card.Header>
                    <Card.Body>
                      <Row className="mb-3">
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Content Type:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.content_type || 'Not specified'}</Badge>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Purpose:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.purpose || 'Not specified'}</Badge>
                          </div>
                        </Col>
                      </Row>

                      <Row className="mb-3">
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Audience:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.audience || 'Not specified'}</Badge>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Tone:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.tone || 'Not specified'}</Badge>
                          </div>
                        </Col>
                      </Row>

                      <Row className="mb-3">
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Style:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.style || 'Not specified'}</Badge>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Word Count:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.word_count || 'Not specified'}</Badge>
                          </div>
                        </Col>
                      </Row>

                      <Row className="mb-3">
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Persona:</strong>
                            <Badge bg="info" className="ms-2">{currentPrompt.persona || 'Not specified'}</Badge>
                          </div>
                        </Col>
                        <Col md={6}>
                          <div className="mb-2">
                            <strong>Output Format:</strong>
                            <Badge bg="primary" className="ms-2">{currentPrompt.output_format || STANDARD_OUTPUT_FORMAT}</Badge>
                          </div>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>

                  {currentPrompt.call_to_action && (
                    <div className="mb-3">
                      <h6>Call to Action:</h6>
                      <div className="p-3 bg-light rounded">
                        {currentPrompt.call_to_action}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <>
                  {/* Original Format Fields */}
                  <div className="mb-3">
                    <h6>Content Prompt:</h6>
                    <div className="p-3 bg-light rounded">
                      <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                        {currentPrompt.content_prompt}
                      </pre>
                    </div>
                  </div>

                  {currentPrompt.call_to_action && (
                    <div className="mb-3">
                      <h6>Call to Action:</h6>
                      <div className="p-3 bg-light rounded">
                        {currentPrompt.call_to_action}
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Show original format fields only if not Claude format */}
              {!currentPrompt.isClaudeFormat && (
                <>
                  <Row className="mb-3">
                    <Col md={4}>
                      <div className="mb-2">
                        <strong>Output Format:</strong>
                        <Badge bg="primary" className="ms-2">{OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].name}</Badge>
                        <div className="mt-1">
                          <Button
                            variant="link"
                            className="p-0 text-decoration-none"
                            size="sm"
                            onClick={() => setShowOutputFormatInfo(true)}
                          >
                            View format details
                          </Button>
                        </div>
                      </div>
                    </Col>
                    <Col md={4}>
                      <div className="mb-2">
                        <strong>Video Format:</strong>
                        <Badge bg="success" className="ms-2">{currentPrompt.video_format}</Badge>
                      </div>
                    </Col>
                    <Col md={4}>
                      <div className="mb-2">
                        <strong>Multiple Topics:</strong>
                        <Badge bg={currentPrompt.multiple_topics ? "success" : "secondary"} className="ms-2">
                          {currentPrompt.multiple_topics ? "Yes" : "No"}
                        </Badge>
                      </div>
                    </Col>
                  </Row>

                  <Row className="mb-3">
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Tone:</strong>
                        <Badge bg="info" className="ms-2">{currentPrompt.tone || 'Not specified'}</Badge>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Goal:</strong>
                        <Badge bg="info" className="ms-2">{currentPrompt.goal || 'Not specified'}</Badge>
                      </div>
                    </Col>
                  </Row>

                  <Row className="mb-3">
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Target Audience:</strong>
                        <Badge bg="info" className="ms-2">{currentPrompt.target_audience || 'Not specified'}</Badge>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Format:</strong>
                        <Badge bg="info" className="ms-2">{currentPrompt.format_type || 'Not specified'}</Badge>
                      </div>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Number of Sentences:</strong> {currentPrompt.num_sentences}
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="mb-2">
                        <strong>Voice ID:</strong> {currentPrompt.voice_id || 'Not specified'}
                      </div>
                    </Col>
                  </Row>
                </>
              )}

              <div className="mt-3 text-muted">
                <small>
                  Created: {currentPrompt.generated_at ? new Date(currentPrompt.generated_at).toLocaleString() : 'N/A'}
                </small>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              setShowViewModal(false);
              openEditModal(currentPrompt);
            }}
          >
            Edit
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Output Format Info Modal */}
      <Modal show={showOutputFormatInfo} onHide={() => setShowOutputFormatInfo(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Output Format Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <h5>{OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].name}</h5>
          <p className="text-muted">{OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].description}</p>

          <div className="mt-3">
            <h6>Format Template:</h6>
            <div className="p-3 bg-light rounded">
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                {OUTPUT_FORMAT_TEMPLATES[STANDARD_OUTPUT_FORMAT].template}
              </pre>
            </div>
          </div>

          <div className="mt-4 alert alert-info">
            <strong>Note:</strong> This standard output format is used for all content prompts to ensure consistency.
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowOutputFormatInfo(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ContentManagementCRUD;
