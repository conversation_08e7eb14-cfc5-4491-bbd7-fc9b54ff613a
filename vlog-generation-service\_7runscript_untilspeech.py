import subprocess
import sys
import os

# Get the Python executable from the current virtual environment
python_executable = sys.executable

# List of Python scripts to execute
scripts = ['_1content_importcsv.py', '_2content_generate_prompt.py', '_3content_generate_images.py','_4content_generate_speech.py']
#,'5content-generate-videoimage-speech-moviepy.py','6content-generate-video-subtitle.py']

for script in scripts:
    try:
        # Execute the script using the virtual environment's Python
        result = subprocess.run([python_executable, script], check=True)
        print(f"{script} executed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"An error occurred while executing {script}: {e}")
        break
    except Exception as e:
        print(f"Unexpected error while executing {script}: {e}")
        break
