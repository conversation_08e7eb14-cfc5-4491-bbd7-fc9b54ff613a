"""
Flower monitoring for the VisionFrame AI queue system.
This script starts the Flower monitoring web interface.
"""

import os
import logging
from celery_config import celery_app
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    logger.info("Starting VisionFrame AI Flower monitoring...")
    
    # Start Flower
    os.system('celery -A celery_config.celery_app flower --port=5555')
