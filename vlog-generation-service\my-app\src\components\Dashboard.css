/* Logo animation */
@keyframes pulse {
    0% {
        transform: rotate(45deg) scale(1);
    }
    50% {
        transform: rotate(45deg) scale(1.05);
    }
    100% {
        transform: rotate(45deg) scale(1);
    }
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add these new styles at the top of your CSS file */
.dashboard-scroll-container {
    width: 100%;
    overflow-x: auto;
    min-width: 1700px; /* Match your dashboard-container max-width */
}

/* Customize the horizontal scrollbar */
.dashboard-scroll-container::-webkit-scrollbar {
    height: 12px;
}

.dashboard-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

.dashboard-scroll-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 6px;
}

.dashboard-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Keep all your existing styles below */
.dashboard-container {
    padding: 5px;
    max-width: 1700px;
    margin: 0 auto;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.dashboard-title {
    margin: 0;
    color: #2541b8;
    font-size: 1.8rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.upload-toggle-btn {
    min-width: 150px;
    padding: 10px 20px;
    font-weight: 500;
    background-color: #2541b8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(37, 65, 184, 0.2);
}

.upload-toggle-btn:hover {
    background-color: #1a2f8a;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(37, 65, 184, 0.3);
}

.upload-toggle-btn:active {
    background-color: #152570;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(37, 65, 184, 0.2);
}

.upload-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.content-item {
    margin-bottom: 30px;
    padding: 2px;
    border: 1px solid #ddd;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.images-column {
    width: 112.5px; /* Changed from 225px (50% smaller) */
}

.images-row {
    display: flex;
    gap: 2px;  /* Reduced from 20px */
}

.thumbnail-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    max-width: 112.5px; /* Changed from 225px (50% smaller) */
    margin: 0;
}

.content-images-container {
    display: flex;
    gap: 2px;
    padding-bottom: 2px;
    overflow-x: auto;
    max-width: calc(100vw - 400px); /* Adjusted from 800px (50% smaller) */
}

.thumbnail-image,
.content-image {
    width: 112.5px; /* Changed from 225px (50% smaller) */
    height: 175px; /* Changed from 350px (50% smaller) */
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-item {
    flex: 0 0 350px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;  /* Reduced from 10px */
    padding: 2px;  /* Reduced from 15px */
    background-color: #f8f9fa;
    border-radius: 8px;
}

.image-label,
.thumbnail-label {
    font-weight: bold;
    margin-bottom: 2px;  /* Reduced from 5px */
}

.image-prompt {
    font-size: 0.9em;
    color: #666;
    text-align: center;
    margin-top: 5px;  /* Reduced from 8px */
    width: 225;
}

/* Custom scrollbar for content-images-container */
.content-images-container::-webkit-scrollbar {
    height: 8px;
}

.content-images-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.content-images-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.content-images-container::-webkit-scrollbar-thumb:hover {
    background: #555;
} */

/* Responsive adjustments */



/* Add these styles to your existing CSS */
.script-execution-button {
    margin: 20px 0;
    padding: 10px 20px;
    font-weight: 500;
    background-color: #2541b8;
    color: white;
    border: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(37, 65, 184, 0.2);
}

.script-execution-button:hover:not(:disabled) {
    background-color: #1a2f8a;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(37, 65, 184, 0.3);
}

.script-execution-button:active:not(:disabled) {
    background-color: #152570;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(37, 65, 184, 0.2);
}

.script-execution-button:disabled {
    cursor: not-allowed;
    background-color: #a0aec0;
    box-shadow: none;
}

/* Add fixed widths for other columns */
table th:nth-child(1) { /* ID column */
    width: 5%; /* Changed from 90% to 5% */
    min-width: 5%; /* Added to ensure minimum width */
    max-width: 5%; /* Added to ensure maximum width */
}

table td:nth-child(1) {
    width: 5%;
    min-width: 5%;
    max-width: 5%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

table th:nth-child(2), /* Scenario column */
table th:nth-child(3), /* Advice column */
table th:nth-child(4), /* Thumbnail column */
table th:nth-child(5) { /* Content Images column */
    width: auto;
    min-width: fit-content;
}

/* Fix thumbnail column width */
table th:nth-child(4), /* Thumbnail column header */
table td:nth-child(4) { /* Thumbnail column cells */
    width: 80px;
    padding: 0;
    max-width: 80px;
    min-width: 80px;
}

/* Make sure the table cells respect these widths */
table td {
    max-width: 0;
    overflow-wrap: break-word;
}

/* Add styles for the thumbnail prompt display */
.thumbnail-prompt {
    font-size: 0.9em;
    color: #666;
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    max-width: 225px;
    word-wrap: break-word;
}

.thumbnail-prompt-label {
    font-weight: bold;
    margin-bottom: 4px;
}

/* Add styles for advice content */
.advice-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 600px;
}

.advice-section {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
}

.advice-label {
    font-weight: 600;
    color: #475569;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.advice-content {
    color: #334155;
    font-size: 0.875rem;
    line-height: 1.5;
}

.thumbnail-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    width: 225px;
    max-width: 225px;
    margin: 0;
    padding: 0;
}

.thumbnail-image {
    width: 225px;
    height: 350px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Add table border styles */
table {
    border-collapse: collapse;
}

table th,
table td {
    border-right: 1px solid #dee2e6;
    border-left: 1px solid #dee2e6;
}

/* Remove border from last column to avoid double borders */
table th:last-child,
table td:last-child {
    border-right: none;
}

/* Remove border from first column to avoid double borders */
table th:first-child,
table td:first-child {
    border-left: none;
}

/* Modern Table Styling */
.dashboard-container table,
.visionframe-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.05);
    margin: 24px 0;
}

/* Table Header */
.dashboard-container thead,
.visionframe-table thead {
    background-color: #2541b8;
}

.dashboard-container th,
.visionframe-table th {
    padding: 16px 24px;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #3a57d0;
    white-space: nowrap;
}

/* Table Body */
.dashboard-container tbody tr,
.visionframe-table tbody tr {
    transition: all 0.2s ease;
}

.dashboard-container tbody tr:hover,
.visionframe-table tbody tr:hover {
    background-color: #f0f4ff;
}

.dashboard-container td,
.visionframe-table td {
    padding: 16px 24px;
    color: #334155;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.875rem;
}

/* Last row styling */
.dashboard-container tbody tr:last-child td,
.visionframe-table tbody tr:last-child td {
    border-bottom: none;
}

/* Image Columns */
.images-column {
    width: 225px;
    padding: 12px !important;
}

/* Thumbnail and Content Images */
.thumbnail-container,
.content-images-container {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 8px;
    border: 1px solid #e2e8f0;
}

.thumbnail-image,
.content-image {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.thumbnail-image:hover,
.content-image:hover {
    transform: scale(1.02);
}

/* Custom Scrollbar for Content Images */
.content-images-container::-webkit-scrollbar {
    height: 6px;
}

.content-images-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.content-images-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.content-images-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Responsive adjustments remain unchanged */






