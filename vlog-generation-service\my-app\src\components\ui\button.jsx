import React from 'react';
import { <PERSON><PERSON> as BootstrapButton } from 'react-bootstrap';

export const Button = ({ 
  children, 
  variant = 'primary', 
  className = '', 
  ...props 
}) => {
  const getVariant = () => {
    switch (variant) {
      case 'outline':
        return 'outline-primary';
      case 'destructive':
        return 'danger';
      default:
        return variant;
    }
  };

  return (
    <BootstrapButton
      variant={getVariant()}
      className={className}
      {...props}
    >
      {children}
    </BootstrapButton>
  );
};