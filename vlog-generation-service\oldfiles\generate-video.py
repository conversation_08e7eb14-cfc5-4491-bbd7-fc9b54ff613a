import os
import whisper
import pysrt
from moviepy.editor import (
    ImageSequenceClip, AudioFileClip, TextClip, CompositeVideoClip, ColorClip
)

# Video dimensions for 9:16 aspect ratio (e.g., 720x1280)
VIDEO_WIDTH = 720
VIDEO_HEIGHT = 1280

def transcribe_audio(audio_path, subtitle_path):
    print("Transcribing audio...")
    model = whisper.load_model("small")  # Adjust model for better accuracy
    result = model.transcribe(audio_path)

    subs = pysrt.SubRipFile()
    
    for i, segment in enumerate(result["segments"]):
        start_time = segment["start"]
        end_time = segment["end"]
        text = segment["text"]

        sub = pysrt.SubRipItem(index=i+1, start=pysrt.SubRipTime(seconds=start_time),end=pysrt.SubRipTime(seconds=end_time), text=text)
        subs.append(sub)

    subs.save(subtitle_path, encoding="utf-8")
    print("Subtitles saved!")

def get_sorted_images(image_folder):
    """Get image filenames sorted numerically."""
    images = [f for f in os.listdir(image_folder) if f.endswith((".jpg", ".png"))]
    images.sort(key=lambda x: int(os.path.splitext(x)[0]))  # Sort by number
    return [os.path.join(image_folder, img) for img in images]

def resize_image(image, width, height):
    """Resize image to fit 9:16 while preserving aspect ratio, adding black padding if necessary."""
    img_resized = image.resize(height=height)  # Resize to fit height
    img_w, img_h = img_resized.size  # Get new dimensions

    if img_w < width:
        # Add black padding if width is too small
        padding = (width - img_w) // 2
        img_resized = img_resized.margin(left=padding, right=padding, color=(0, 0, 0))

    return img_resized

def create_video(image_folder, audio_path, subtitle_path, output_path):
    print("Creating video...")

    # Get sorted images
    image_paths = get_sorted_images(image_folder)
    print(f"Using images: {image_paths}")

    # Load audio
    audio = AudioFileClip(audio_path)

    # Calculate image durations
    num_images = len(image_paths)
    image_duration = audio.duration / num_images

    # Load images and resize to 9:16
    images = [resize_image(ImageSequenceClip([img], durations=[image_duration]), VIDEO_WIDTH, VIDEO_HEIGHT) for img in image_paths]

    # Create video clip from images
    video = ImageSequenceClip(images, durations=[image_duration] * num_images)
    video = video.set_audio(audio)

    # Load subtitles
    subtitles = pysrt.open(subtitle_path)
    subtitle_clips = []

    for sub in subtitles:
        text = sub.text.replace("\n", " ")
        start_time = sub.start.ordinal / 1000
        end_time = sub.end.ordinal / 1000

        subtitle = TextClip(text, fontsize=50, color='white', bg_color='black', size=(VIDEO_WIDTH - 100, None))
        subtitle = subtitle.set_position(("center", VIDEO_HEIGHT - 200)).set_start(start_time).set_duration(end_time - start_time)
        subtitle_clips.append(subtitle)

    # Combine video and subtitles
    final_video = CompositeVideoClip([video] + subtitle_clips, size=(VIDEO_WIDTH, VIDEO_HEIGHT))

    # Export video
    final_video.write_videofile(output_path, fps=30, codec='libx264', audio_codec='aac')
    print("Video created successfully!")

# Example usage
image_folder = "images"  # Folder where numbered images are stored
audio_file = "audio.mp3"
subtitle_file = "subtitles.srt"
output_video = "output_video.mp4"

transcribe_audio(audio_file, subtitle_file)
create_video(image_folder, audio_file, subtitle_file, output_video)
