import asyncio
import os
import platform
from dotenv import load_dotenv
from runware import Runware, IImageInference

# Load API Key from .env
load_dotenv()
RUNWARE_API_KEY = os.getenv("RUNWARE_API_KEY")

if not RUNWARE_API_KEY:
    raise ValueError("API Key is missing! Check your .env file or environment variables.")

async def main() -> None:
    # Initialize Runware client
    runware = Runware(api_key=RUNWARE_API_KEY)
    
    # Connect to the API
    await runware.connect()

    # Define image generation parameters
    request_image = IImageInference(
        positivePrompt="realistic photo ofTwo People in Different Cities Looking at the Same MoonAt night, a man and a woman stand on separate balconies in different cities, both gazing at the full moon. One is in a bustling urban city with skyscrapers, while the other is in a quiet, small town with trees and stars. A faint heart shape appears in the sky, symbolizing their connection despite the distance.",
        negativePrompt="blurry, low quality, deformed",
        model="runware:100@1",
        numberResults=1,
        height=2048,
        width=1152,
        CFGScale= 7.5,
    )
    # Request image generation
    images = await runware.imageInference(requestImage=request_image)

    # Output image URLs
    for image in images:
        print(f"Image URL: {image.imageURL}")

# Ensure compatibility with Windows event loop
if __name__ == "__main__":
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
