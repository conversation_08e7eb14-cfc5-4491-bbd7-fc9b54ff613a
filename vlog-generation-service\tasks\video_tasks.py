"""
Video mixing tasks for the VisionFrame AI queue system.
"""

import os
import json
import logging
import subprocess
from celery import shared_task
from .db_utils import get_chunk_data, update_task_status, get_task_by_id
from .task_utils import task_completed, task_failed
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@shared_task(
    name='tasks.video_tasks.mix_components',
    bind=True,
    max_retries=3,
    default_retry_delay=60
)
def mix_components(self, queue_id):
    """Mix all components to create a final video."""
    logger.info(f"Mixing components for task {queue_id}")

    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the chunk data
        chunk_id = task['chunk_id']
        chunk_data = get_chunk_data(chunk_id)

        if not chunk_data:
            error_msg = f"Chunk data not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the previous tasks results
        slideshow_task = get_task_by_id(queue_id - 2)  # Assuming sequential IDs
        subtitle_task = get_task_by_id(queue_id - 1)  # Assuming sequential IDs
        speech_task = get_task_by_id(queue_id - 3)  # Assuming sequential IDs

        if not slideshow_task or slideshow_task['process_step'] != 'create_slideshow':
            error_msg = f"Slideshow task not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        if not subtitle_task or subtitle_task['process_step'] != 'create_subtitles':
            error_msg = f"Subtitle task not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        if not speech_task or speech_task['process_step'] != 'generate_speech':
            error_msg = f"Speech task not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the paths from the previous tasks
        slideshow_result_data = json.loads(slideshow_task['result_data'])
        subtitle_result_data = json.loads(subtitle_task['result_data'])
        speech_result_data = json.loads(speech_task['result_data'])

        if not slideshow_result_data or 'slideshow_path' not in slideshow_result_data:
            error_msg = f"No slideshow found in previous task result for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        if not subtitle_result_data or 'subtitle_path' not in subtitle_result_data:
            error_msg = f"No subtitles found in previous task result for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        if not speech_result_data or 'audio_path' not in speech_result_data:
            error_msg = f"No audio found in previous task result for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Mix the components
        video_path = mix_components_with_ffmpeg(
            slideshow_result_data['slideshow_path'],
            speech_result_data['audio_path'],
            subtitle_result_data['subtitle_path'],
            f"chunk_{chunk_id}_video"
        )

        if not video_path:
            error_msg = f"Failed to mix components for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Update the result data
        result_data = {
            'video_path': video_path,
            'content_id': chunk_data['content']['id'],
            'chunk_id': chunk_id,
            'chunk_order': chunk_data['chunk']['chunk_order']
        }

        # Mark the task as completed
        task_completed.delay(queue_id, result_data)

        return f"Mixed components for chunk {chunk_id}"

    except Exception as e:
        logger.error(f"Error mixing components for task {queue_id}: {str(e)}")

        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            # If max retries exceeded, mark the task as failed
            task_failed.delay(queue_id, str(e))

        return f"Error mixing components for task {queue_id}: {str(e)}"

def mix_components_with_ffmpeg(slideshow_path, audio_path, subtitle_path, video_name):
    """Mix slideshow, audio, and subtitles to create a final video."""
    try:
        # Create the videos directory if it doesn't exist
        os.makedirs('videos', exist_ok=True)

        # Output path for the video
        video_path = f"videos/{video_name}.mp4"

        # FFmpeg command to mix the components
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output file if it exists
            '-i', slideshow_path,  # Input slideshow
            '-i', audio_path,  # Input audio
            '-vf', f"subtitles={subtitle_path}:force_style='FontSize=24,Alignment=2,BorderStyle=3,Outline=1,Shadow=0,MarginV=25'",  # Add subtitles
            '-c:v', 'libx264',  # Video codec
            '-c:a', 'aac',  # Audio codec
            '-shortest',  # End when the shortest input ends
            video_path
        ]

        # Execute the FFmpeg command
        subprocess.run(cmd, check=True)

        logger.info(f"Video created at {video_path}")

        return video_path

    except Exception as e:
        logger.error(f"Error mixing components with FFmpeg: {str(e)}")
        return None
