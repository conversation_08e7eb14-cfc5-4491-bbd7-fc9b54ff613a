"""
Test Redis Connection

This script tests the connection to Redis and verifies that it's working correctly.
"""

import redis
import sys

def test_redis_connection():
    """Test the connection to Redis"""
    try:
        # Connect to Redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        # Ping Redis
        response = r.ping()
        print(f"Redis ping response: {response}")
        
        # Set a test value
        r.set('test_key', 'test_value')
        
        # Get the test value
        value = r.get('test_key')
        print(f"Retrieved test value: {value.decode('utf-8')}")
        
        # Delete the test value
        r.delete('test_key')
        
        print("Redis connection test successful!")
        return True
    except Exception as e:
        print(f"Redis connection test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1)
