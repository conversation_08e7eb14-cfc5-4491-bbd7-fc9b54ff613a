"""
Startup script for the VisionFrame AI queue system.
This script starts all components of the queue system.
"""

import os
import sys
import time
import logging
import subprocess
import threading
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_redis_connection():
    """Check if Redis is available."""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        logger.info("Redis server is available")
        return True
    except:
        return False

def start_redis():
    """Start Redis server or verify connection."""
    logger.info("Checking Redis server...")

    # First check if Redis is already running
    if check_redis_connection():
        logger.info("Redis server is already running")
        return None

    # Try to start Redis server
    try:
        # Check common Redis installation paths
        redis_paths = []

        # Add common Redis installation paths
        # Windows paths
        redis_paths.extend([
            "C:\\ProgramData\\chocolatey\\bin\\redis-server.exe",
            "C:\\Program Files\\Redis\\redis-server.exe",
            os.path.join(os.environ.get('ProgramFiles', 'C:\\Program Files'), 'Redis', 'redis-server.exe')
        ])

        # Unix-like paths (Linux/macOS)
        redis_paths.extend([
            "/usr/bin/redis-server",
            "/usr/local/bin/redis-server",
            "/opt/homebrew/bin/redis-server"
        ])

        # Try each path
        for redis_path in redis_paths:
            if os.path.exists(redis_path):
                try:
                    process = subprocess.Popen([redis_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    logger.info(f"Redis server started from {redis_path}")
                    return process
                except Exception as e:
                    logger.warning(f"Failed to start Redis from {redis_path}: {str(e)}")

        # Try from PATH as last resort
        try:
            process = subprocess.Popen(['redis-server'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            logger.info("Redis server started from PATH")
            return process
        except Exception:
            pass

        # If we get here, Redis is not installed or not in PATH
        logger.warning("Redis server not found. Using memory broker instead.")
        logger.warning("For production use, please install Redis:")

        # Installation instructions for all platforms
        logger.warning("Installation instructions:")
        logger.warning("- Windows: Install with Chocolatey: choco install redis-64")
        logger.warning("- Windows: Or download from https://github.com/microsoftarchive/redis/releases")
        logger.warning("- macOS: Install with Homebrew: brew install redis")
        logger.warning("- Ubuntu/Debian: sudo apt install redis-server")
        logger.warning("- CentOS/RHEL: sudo yum install redis")

        # Update Celery config to use memory broker
        update_celery_config_for_memory_broker()
        return None

    except Exception as e:
        logger.warning(f"Could not start Redis server: {str(e)}")
        logger.warning("Using memory broker instead. For production use, please install Redis.")

        # Update Celery config to use memory broker
        update_celery_config_for_memory_broker()
        return None

def update_celery_config_for_memory_broker():
    """Update Celery config to use memory broker for development."""
    try:
        with open('celery_config.py', 'r') as f:
            content = f.read()

        # Replace Redis broker with memory broker
        content = content.replace(
            "broker='redis://localhost:6379/0',",
            "broker='memory://',"
        )
        content = content.replace(
            "backend='redis://localhost:6379/1',",
            "backend='memory://',"
        )

        with open('celery_config.py', 'w') as f:
            f.write(content)

        logger.info("Updated Celery config to use memory broker")
    except Exception as e:
        logger.error(f"Error updating Celery config: {str(e)}")
        logger.error("Please manually update celery_config.py to use memory broker")

def start_worker():
    """Start Celery worker."""
    logger.info("Starting Celery worker...")

    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'worker',
             '--loglevel=INFO', '--concurrency=4', '-n', 'visionframe_worker@%h',
             '-Q', 'default,image_generation,speech_generation,slideshow_creation,subtitle_creation,video_mixing'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        logger.info("Celery worker started")
        return process
    except Exception as e:
        logger.error(f"Error starting Celery worker: {str(e)}")
        logger.error("Make sure Celery is installed: pip install celery")
        return None

def start_beat():
    """Start Celery beat."""
    logger.info("Starting Celery beat...")

    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'beat', '--loglevel=INFO'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        logger.info("Celery beat started")
        return process
    except Exception as e:
        logger.error(f"Error starting Celery beat: {str(e)}")
        logger.error("Make sure Celery is installed: pip install celery")
        return None

def start_flower():
    """Start Flower monitoring."""
    logger.info("Starting Flower monitoring...")

    try:
        process = subprocess.Popen(
            ['celery', '-A', 'celery_config.celery_app', 'flower', '--port=5555'],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        logger.info("Flower monitoring started at http://localhost:5555")
        return process
    except Exception as e:
        logger.error(f"Error starting Flower monitoring: {str(e)}")
        logger.error("Make sure Flower is installed: pip install flower")
        return None

def monitor_process(process, name):
    """Monitor a process and log its output."""
    while True:
        output = process.stdout.readline()
        if output:
            logger.info(f"{name}: {output.strip().decode('utf-8')}")

        error = process.stderr.readline()
        if error:
            logger.error(f"{name} error: {error.strip().decode('utf-8')}")

        # Check if process is still running
        if process.poll() is not None:
            logger.warning(f"{name} process has terminated with code {process.returncode}")
            break

        time.sleep(0.1)

if __name__ == '__main__':
    logger.info("Starting VisionFrame AI queue system...")

    # Start Redis
    redis_process = start_redis()

    # Wait for Redis to start
    time.sleep(2)

    # Start Celery worker
    worker_process = start_worker()

    # Start Celery beat
    beat_process = start_beat()

    # Start Flower monitoring
    flower_process = start_flower()

    # Monitor processes
    if redis_process:
        threading.Thread(target=monitor_process, args=(redis_process, "Redis"), daemon=True).start()

    threading.Thread(target=monitor_process, args=(worker_process, "Worker"), daemon=True).start()
    threading.Thread(target=monitor_process, args=(beat_process, "Beat"), daemon=True).start()
    threading.Thread(target=monitor_process, args=(flower_process, "Flower"), daemon=True).start()

    logger.info("All components started. Press Ctrl+C to stop.")

    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping all components...")

        # Stop processes
        if redis_process:
            redis_process.terminate()

        worker_process.terminate()
        beat_process.terminate()
        flower_process.terminate()

        logger.info("All components stopped.")
