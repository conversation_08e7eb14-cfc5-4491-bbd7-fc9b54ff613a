<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VisionFrame AI - Professional Video Editor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-900 text-white overflow-hidden">
    <!-- Top Toolbar -->
    <div class="h-12 bg-gray-800 border-b border-gray-700 flex items-center px-4">
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <i class="fas fa-video text-blue-500"></i>
                <span class="font-bold">VisionFrame AI</span>
            </div>
            <div class="h-6 w-px bg-gray-600"></div>
            <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-2 cursor-pointer">
                    <span class="text-sm">Animate</span>
                    <div class="relative">
                        <input type="checkbox" id="animateToggle" class="sr-only" checked>
                        <div class="w-10 h-6 bg-gray-600 rounded-full shadow-inner"></div>
                        <div class="dot absolute w-4 h-4 bg-blue-500 rounded-full shadow -left-1 -top-1 transition"></div>
                    </div>
                </label>
            </div>
        </div>
        <div class="flex-1"></div>
        <div class="flex items-center space-x-2">
            <button id="undoBtn" class="p-2 hover:bg-gray-700 rounded" title="Undo">
                <i class="fas fa-undo"></i>
            </button>
            <button id="redoBtn" class="p-2 hover:bg-gray-700 rounded" title="Redo">
                <i class="fas fa-redo"></i>
            </button>
            <div class="h-6 w-px bg-gray-600"></div>
            <button id="previewBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                <i class="fas fa-play mr-2"></i>Preview
            </button>
            <button id="exportBtn" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-sm">
                <i class="fas fa-download mr-2"></i>Export
            </button>
        </div>
    </div>

    <!-- Main Layout -->
    <div class="flex h-[calc(100vh-3rem)]">
        <!-- Left Panel -->
        <div class="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            <!-- Panel Tabs -->
            <div class="flex border-b border-gray-700">
                <button class="flex-1 py-3 px-4 text-sm font-medium border-b-2 border-blue-500 text-blue-500" data-tab="elements">
                    <i class="fas fa-layer-group mr-2"></i>Elements
                </button>
                <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-400 hover:text-white" data-tab="media">
                    <i class="fas fa-photo-video mr-2"></i>Media
                </button>
                <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-400 hover:text-white" data-tab="animations">
                    <i class="fas fa-magic mr-2"></i>Animations
                </button>
            </div>

            <!-- Elements Tab -->
            <div id="elementsTab" class="flex-1 overflow-y-auto p-4">
                <div class="space-y-4">
                    <!-- Add Elements Section -->
                    <div>
                        <h3 class="text-sm font-semibold mb-3 text-gray-300">Add Elements</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="text">
                                <i class="fas fa-font text-xl mb-2 text-blue-400"></i>
                                <div class="text-xs">Text</div>
                            </button>
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="image">
                                <i class="fas fa-image text-xl mb-2 text-green-400"></i>
                                <div class="text-xs">Image</div>
                            </button>
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="video">
                                <i class="fas fa-video text-xl mb-2 text-purple-400"></i>
                                <div class="text-xs">Video</div>
                            </button>
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="audio">
                                <i class="fas fa-music text-xl mb-2 text-orange-400"></i>
                                <div class="text-xs">Audio</div>
                            </button>
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="shape">
                                <i class="fas fa-shapes text-xl mb-2 text-red-400"></i>
                                <div class="text-xs">Shape</div>
                            </button>
                            <button class="element-btn p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-center" data-type="composition">
                                <i class="fas fa-layer-group text-xl mb-2 text-yellow-400"></i>
                                <div class="text-xs">Composition</div>
                            </button>
                        </div>
                    </div>

                    <!-- Layer Navigator -->
                    <div>
                        <h3 class="text-sm font-semibold mb-3 text-gray-300">Layers</h3>
                        <div id="layerNavigator" class="space-y-1">
                            <!-- Layers will be dynamically added here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Tab -->
            <div id="mediaTab" class="flex-1 overflow-y-auto p-4 hidden">
                <div class="space-y-4">
                    <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center" id="dropZone">
                        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-4"></i>
                        <p class="text-gray-400 mb-2">Drag and drop files here</p>
                        <p class="text-xs text-gray-500">or click to browse</p>
                        <input type="file" id="fileInput" multiple accept="image/*,video/*,audio/*" class="hidden">
                    </div>
                    <div id="mediaLibrary" class="grid grid-cols-2 gap-2">
                        <!-- Media items will be added here -->
                    </div>
                </div>
            </div>

            <!-- Animations Tab -->
            <div id="animationsTab" class="flex-1 overflow-y-auto p-4 hidden">
                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-semibold mb-3 text-gray-300">Entrance</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="fadeIn">
                                Fade In
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideInLeft">
                                Slide In Left
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideInRight">
                                Slide In Right
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideInUp">
                                Slide In Up
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideInDown">
                                Slide In Down
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="scaleIn">
                                Scale In
                            </button>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold mb-3 text-gray-300">Exit</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="fadeOut">
                                Fade Out
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideOutLeft">
                                Slide Out Left
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="slideOutRight">
                                Slide Out Right
                            </button>
                            <button class="animation-preset p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs" data-preset="scaleOut">
                                Scale Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Center Canvas Area -->
        <div class="flex-1 flex flex-col bg-gray-900">
            <!-- Canvas Toolbar -->
            <div class="h-10 bg-gray-800 border-b border-gray-700 flex items-center px-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <button id="zoomOut" class="p-1 hover:bg-gray-700 rounded">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span id="zoomLevel" class="text-xs text-gray-400">100%</span>
                        <button id="zoomIn" class="p-1 hover:bg-gray-700 rounded">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="zoomFit" class="px-2 py-1 text-xs hover:bg-gray-700 rounded">Fit</button>
                    </div>
                    <div class="h-4 w-px bg-gray-600"></div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-400">Resolution:</span>
                        <select id="resolutionSelect" class="bg-gray-700 text-xs px-2 py-1 rounded">
                            <option value="1920x1080">1920×1080 (HD)</option>
                            <option value="1280x720">1280×720 (HD)</option>
                            <option value="1080x1920">1080×1920 (Vertical)</option>
                            <option value="1080x1080">1080×1080 (Square)</option>
                        </select>
                    </div>
                </div>
                <div class="flex-1"></div>
                <div class="flex items-center space-x-2">
                    <button id="gridToggle" class="p-1 hover:bg-gray-700 rounded text-xs">
                        <i class="fas fa-th"></i>
                    </button>
                    <button id="snapToggle" class="p-1 hover:bg-gray-700 rounded text-xs">
                        <i class="fas fa-magnet"></i>
                    </button>
                </div>
            </div>

            <!-- Canvas Container -->
            <div class="flex-1 relative overflow-hidden">
                <div id="canvasContainer" class="w-full h-full flex items-center justify-center">
                    <div id="canvas" class="relative bg-black shadow-2xl" style="width: 640px; height: 360px;">
                        <!-- Canvas content will be rendered here -->
                        <div id="canvasGrid" class="absolute inset-0 opacity-20 hidden"></div>
                        <div id="canvasElements" class="absolute inset-0">
                            <!-- Elements will be rendered here -->
                        </div>
                        <!-- Selection handles -->
                        <div id="selectionBox" class="absolute border-2 border-blue-500 hidden">
                            <div class="absolute w-2 h-2 bg-blue-500 -top-1 -left-1"></div>
                            <div class="absolute w-2 h-2 bg-blue-500 -top-1 -right-1"></div>
                            <div class="absolute w-2 h-2 bg-blue-500 -bottom-1 -left-1"></div>
                            <div class="absolute w-2 h-2 bg-blue-500 -bottom-1 -right-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Properties Panel -->
        <div class="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
            <!-- Properties Tabs -->
            <div class="flex border-b border-gray-700">
                <button class="flex-1 py-3 px-4 text-sm font-medium border-b-2 border-blue-500 text-blue-500" data-tab="properties">
                    <i class="fas fa-cog mr-2"></i>Properties
                </button>
                <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-400 hover:text-white" data-tab="keyframes">
                    <i class="fas fa-key mr-2"></i>Keyframes
                </button>
            </div>

            <!-- Properties Content -->
            <div id="propertiesContent" class="flex-1 overflow-y-auto p-4">
                <div id="noSelection" class="text-center text-gray-400 mt-8">
                    <i class="fas fa-mouse-pointer text-3xl mb-4"></i>
                    <p>Select an element to edit properties</p>
                </div>

                <!-- Element Properties (hidden by default) -->
                <div id="elementProperties" class="hidden space-y-4">
                    <!-- Arrange Section -->
                    <div class="property-section">
                        <h4 class="property-section-title">
                            <i class="fas fa-arrows-alt mr-2"></i>Arrange
                        </h4>
                        <div class="property-section-content">
                            <div class="property-row">
                                <label>Time</label>
                                <input type="number" id="propTime" step="0.1" class="property-input">
                            </div>
                            <div class="property-row">
                                <label>Duration</label>
                                <input type="number" id="propDuration" step="0.1" class="property-input">
                            </div>
                            <div class="property-row-group">
                                <div class="property-row">
                                    <label>X</label>
                                    <input type="number" id="propX" class="property-input">
                                </div>
                                <div class="property-row">
                                    <label>Y</label>
                                    <input type="number" id="propY" class="property-input">
                                </div>
                            </div>
                            <div class="property-row-group">
                                <div class="property-row">
                                    <label>Width</label>
                                    <input type="number" id="propWidth" class="property-input">
                                </div>
                                <div class="property-row">
                                    <label>Height</label>
                                    <input type="number" id="propHeight" class="property-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transform Section -->
                    <div class="property-section">
                        <h4 class="property-section-title">
                            <i class="fas fa-sync-alt mr-2"></i>Transform
                        </h4>
                        <div class="property-section-content">
                            <div class="property-row-group">
                                <div class="property-row">
                                    <label>Anchor X</label>
                                    <input type="number" id="propAnchorX" value="50" class="property-input">
                                </div>
                                <div class="property-row">
                                    <label>Anchor Y</label>
                                    <input type="number" id="propAnchorY" value="50" class="property-input">
                                </div>
                            </div>
                            <div class="property-row-group">
                                <div class="property-row">
                                    <label>Scale X</label>
                                    <input type="number" id="propScaleX" value="100" class="property-input">
                                </div>
                                <div class="property-row">
                                    <label>Scale Y</label>
                                    <input type="number" id="propScaleY" value="100" class="property-input">
                                </div>
                            </div>
                            <div class="property-row">
                                <label>Rotation</label>
                                <input type="number" id="propRotation" value="0" class="property-input">
                            </div>
                            <div class="property-row">
                                <label>Opacity</label>
                                <input type="range" id="propOpacity" min="0" max="100" value="100" class="property-slider">
                                <span class="property-value">100%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Element-specific properties will be added dynamically -->
                    <div id="specificProperties"></div>

                    <!-- Effects Section -->
                    <div class="property-section">
                        <h4 class="property-section-title">
                            <i class="fas fa-magic mr-2"></i>Effects
                        </h4>
                        <div class="property-section-content">
                            <div class="property-row">
                                <label>Shadow</label>
                                <input type="checkbox" id="propShadow" class="property-checkbox">
                            </div>
                            <div class="property-row">
                                <label>Blur</label>
                                <input type="range" id="propBlur" min="0" max="20" value="0" class="property-slider">
                                <span class="property-value">0px</span>
                            </div>
                            <div class="property-row">
                                <label>Border Radius</label>
                                <input type="range" id="propBorderRadius" min="0" max="50" value="0" class="property-slider">
                                <span class="property-value">0px</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Timeline -->
    <div class="h-64 bg-gray-800 border-t border-gray-700 flex flex-col">
        <!-- Timeline Header -->
        <div class="h-10 bg-gray-700 border-b border-gray-600 flex items-center px-4">
            <div class="flex items-center space-x-4">
                <button id="timelinePlayBtn" class="p-2 hover:bg-gray-600 rounded">
                    <i class="fas fa-play"></i>
                </button>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-400">Time:</span>
                    <input type="text" id="currentTimeInput" value="00:00.0" class="bg-gray-600 text-xs px-2 py-1 rounded w-16">
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-400">Duration:</span>
                    <input type="text" id="durationInput" value="00:10.0" class="bg-gray-600 text-xs px-2 py-1 rounded w-16">
                </div>
            </div>
            <div class="flex-1"></div>
            <div class="flex items-center space-x-2">
                <button id="timelineZoomOut" class="p-1 hover:bg-gray-600 rounded">
                    <i class="fas fa-search-minus"></i>
                </button>
                <span class="text-xs text-gray-400">Zoom</span>
                <button id="timelineZoomIn" class="p-1 hover:bg-gray-600 rounded">
                    <i class="fas fa-search-plus"></i>
                </button>
            </div>
        </div>

        <!-- Timeline Content -->
        <div class="flex-1 flex">
            <!-- Track Labels -->
            <div class="w-48 bg-gray-700 border-r border-gray-600 overflow-y-auto">
                <div id="timelineTrackLabels">
                    <!-- Track labels will be added here -->
                </div>
            </div>

            <!-- Timeline Tracks -->
            <div class="flex-1 relative overflow-auto">
                <div id="timelineRuler" class="h-8 bg-gray-600 border-b border-gray-500 relative">
                    <!-- Time ruler will be generated here -->
                </div>
                <div id="timelineTracks" class="relative">
                    <!-- Timeline tracks will be generated here -->
                </div>
                <!-- Playhead -->
                <div id="timelinePlayhead" class="absolute top-0 bottom-0 w-0.5 bg-red-500 z-20 pointer-events-none">
                    <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 transform rotate-45"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Text Modal -->
    <div id="textModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-lg w-96 border border-gray-600">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-font text-blue-400 mr-2"></i>Add Text Element
            </h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm mb-1 text-gray-300">Text Content</label>
                    <input type="text" id="textContent" placeholder="Enter your text..."
                           class="w-full p-3 bg-gray-700 border border-gray-600 rounded focus:border-blue-500 focus:outline-none">
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm mb-1 text-gray-300">Font Size</label>
                        <input type="number" id="textFontSize" value="24" min="8" max="200"
                               class="w-full p-2 bg-gray-700 border border-gray-600 rounded">
                    </div>
                    <div>
                        <label class="block text-sm mb-1 text-gray-300">Color</label>
                        <input type="color" id="textColor" value="#ffffff"
                               class="w-full h-10 bg-gray-700 border border-gray-600 rounded">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button id="cancelText" class="flex-1 p-3 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                        Cancel
                    </button>
                    <button id="confirmText" class="flex-1 p-3 bg-blue-600 hover:bg-blue-700 rounded transition-colors">
                        Add Text
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Shape Modal -->
    <div id="shapeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-lg w-96 border border-gray-600">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-shapes text-red-400 mr-2"></i>Add Shape Element
            </h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm mb-1 text-gray-300">Shape Type</label>
                    <select id="shapeType" class="w-full p-3 bg-gray-700 border border-gray-600 rounded">
                        <option value="rectangle">Rectangle</option>
                        <option value="circle">Circle</option>
                        <option value="triangle">Triangle</option>
                        <option value="star">Star</option>
                    </select>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm mb-1 text-gray-300">Width</label>
                        <input type="number" id="shapeWidth" value="200" min="10" max="1000"
                               class="w-full p-2 bg-gray-700 border border-gray-600 rounded">
                    </div>
                    <div>
                        <label class="block text-sm mb-1 text-gray-300">Height</label>
                        <input type="number" id="shapeHeight" value="200" min="10" max="1000"
                               class="w-full p-2 bg-gray-700 border border-gray-600 rounded">
                    </div>
                </div>
                <div>
                    <label class="block text-sm mb-1 text-gray-300">Fill Color</label>
                    <input type="color" id="shapeFillColor" value="#3b82f6"
                           class="w-full h-10 bg-gray-700 border border-gray-600 rounded">
                </div>
                <div class="flex space-x-2">
                    <button id="cancelShape" class="flex-1 p-3 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                        Cancel
                    </button>
                    <button id="confirmShape" class="flex-1 p-3 bg-red-600 hover:bg-red-700 rounded transition-colors">
                        Add Shape
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="creatomate-script.js"></script>
</body>
</html>
