<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VisionFrame AI - Video Editor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-900 text-white">
    <div class="flex h-screen">
        <!-- Left Sidebar - Element Controls -->
        <div class="w-80 bg-gray-800 p-4 overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">VisionFrame AI Editor</h2>

            <!-- Project Settings -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Project Settings</h3>
                <div class="space-y-2">
                    <div>
                        <label class="block text-sm">Duration (seconds)</label>
                        <input type="number" id="duration" value="10" min="1" max="60"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div>
                        <label class="block text-sm">Width</label>
                        <input type="number" id="width" value="1920"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div>
                        <label class="block text-sm">Height</label>
                        <input type="number" id="height" value="1080"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                </div>
            </div>

            <!-- Add Elements -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Add Elements</h3>
                <div class="space-y-2">
                    <button id="addText" class="w-full p-2 bg-blue-600 hover:bg-blue-700 rounded">
                        Add Text
                    </button>
                    <button id="addImage" class="w-full p-2 bg-green-600 hover:bg-green-700 rounded">
                        Add Image
                    </button>
                    <button id="addVideo" class="w-full p-2 bg-purple-600 hover:bg-purple-700 rounded">
                        Add Video
                    </button>
                </div>
            </div>

            <!-- Elements List -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2">Elements</h3>
                <div id="elementsList" class="space-y-2">
                    <!-- Elements will be dynamically added here -->
                </div>
            </div>

            <!-- Project Actions -->
            <div class="mb-6">
                <div class="space-y-2">
                    <button id="loadSample" class="w-full p-2 bg-yellow-600 hover:bg-yellow-700 rounded">
                        Load Sample Project
                    </button>
                    <button id="clearProject" class="w-full p-2 bg-gray-600 hover:bg-gray-700 rounded">
                        Clear Project
                    </button>
                    <button id="exportJson" class="w-full p-3 bg-red-600 hover:bg-red-700 rounded font-semibold">
                        Export to JSON
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col">
            <!-- Preview Area -->
            <div class="flex-1 bg-black relative overflow-hidden">
                <div id="previewContainer" class="w-full h-full relative">
                    <!-- Preview elements will be rendered here -->
                </div>

                <!-- Timeline Overlay -->
                <div class="absolute bottom-0 left-0 right-0 bg-gray-800 bg-opacity-90 p-4">
                    <div class="flex items-center space-x-4">
                        <button id="playPause" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded">
                            Play
                        </button>
                        <div class="flex-1">
                            <input type="range" id="timeline" min="0" max="10" step="0.1" value="0"
                                   class="w-full">
                        </div>
                        <span id="timeDisplay" class="text-sm">0.0s / 10.0s</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Sidebar - Element Properties -->
        <div class="w-80 bg-gray-800 p-4 overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">Element Properties</h3>
            <div id="propertiesPanel">
                <p class="text-gray-400">Select an element to edit properties</p>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="textModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-lg w-96">
            <h3 class="text-lg font-semibold mb-4">Add Text Element</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm mb-1">Text Content</label>
                    <input type="text" id="textContent" placeholder="Enter text..."
                           class="w-full p-2 bg-gray-700 rounded">
                </div>
                <div class="flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm mb-1">X Position (%)</label>
                        <input type="number" id="textX" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm mb-1">Y Position (%)</label>
                        <input type="number" id="textY" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button id="cancelText" class="flex-1 p-2 bg-gray-600 hover:bg-gray-700 rounded">
                        Cancel
                    </button>
                    <button id="confirmText" class="flex-1 p-2 bg-blue-600 hover:bg-blue-700 rounded">
                        Add Text
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-lg w-96">
            <h3 class="text-lg font-semibold mb-4">Add Image Element</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm mb-1">Image URL</label>
                    <input type="url" id="imageUrl" placeholder="https://example.com/image.jpg"
                           class="w-full p-2 bg-gray-700 rounded">
                </div>
                <div class="flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm mb-1">X Position (%)</label>
                        <input type="number" id="imageX" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm mb-1">Y Position (%)</label>
                        <input type="number" id="imageY" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button id="cancelImage" class="flex-1 p-2 bg-gray-600 hover:bg-gray-700 rounded">
                        Cancel
                    </button>
                    <button id="confirmImage" class="flex-1 p-2 bg-green-600 hover:bg-green-700 rounded">
                        Add Image
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 p-6 rounded-lg w-96">
            <h3 class="text-lg font-semibold mb-4">Add Video Element</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm mb-1">Video URL</label>
                    <input type="url" id="videoUrl" placeholder="https://example.com/video.mp4"
                           class="w-full p-2 bg-gray-700 rounded">
                </div>
                <div class="flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm mb-1">Start Time (s)</label>
                        <input type="number" id="videoStart" value="0" min="0" step="0.1"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm mb-1">End Time (s)</label>
                        <input type="number" id="videoEnd" value="5" min="0" step="0.1"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <div class="flex-1">
                        <label class="block text-sm mb-1">X Position (%)</label>
                        <input type="number" id="videoX" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm mb-1">Y Position (%)</label>
                        <input type="number" id="videoY" value="50" min="0" max="100"
                               class="w-full p-2 bg-gray-700 rounded">
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button id="cancelVideo" class="flex-1 p-2 bg-gray-600 hover:bg-gray-700 rounded">
                        Cancel
                    </button>
                    <button id="confirmVideo" class="flex-1 p-2 bg-purple-600 hover:bg-purple-700 rounded">
                        Add Video
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
