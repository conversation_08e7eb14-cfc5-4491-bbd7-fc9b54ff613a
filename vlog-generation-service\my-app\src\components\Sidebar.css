.sidebar {
    width: 280px;
    min-height: 100vh;
    background-color: #ffffff;
    padding: 24px 16px;
    border-right: 1px solid #eaeaea;
    flex-shrink: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.sidebar-header {
    padding: 0 12px 16px 12px;
    margin-bottom: 16px;
    border-bottom: 1px solid #eaeaea;
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Logo styling */
.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes pulse {
    0% {
        transform: rotate(45deg) scale(1);
    }
    50% {
        transform: rotate(45deg) scale(1.05);
    }
    100% {
        transform: rotate(45deg) scale(1);
    }
}

.app-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2541b8;
    margin: 0;
    letter-spacing: 0.5px;
}

.sidebar .flex-column {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.sidebar-divider {
    height: 1px;
    background-color: #eaeaea;
    margin: 8px 16px;
    opacity: 0.6;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #666;
    border-radius: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
    font-weight: 500;
}

.sidebar-link:hover {
    background-color: #f5f5f5;
    color: #1a1a1a;
}

.sidebar-link.active {
    background-color: #f0f7ff;
    color: #0066ff;
}

.sidebar-icon {
    font-size: 20px !important;
    margin-right: 12px;
    opacity: 0.9;
    color: inherit;
    transition: all 0.2s ease;
}

.sidebar-link span {
    font-size: 15px;
}

/* Hover effects */
.sidebar-link:hover .sidebar-icon {
    transform: translateX(2px);
    transition: transform 0.2s ease;
    opacity: 1;
}

/* Active state enhancements */
.sidebar-link.active .sidebar-icon {
    color: #0066ff !important;
    opacity: 1;
}

/* Submenu styles */
.sidebar-submenu-container {
    display: flex;
    flex-direction: column;
}

.sidebar-sublink {
    display: flex;
    align-items: center;
    padding: 10px 16px 10px 40px;
    color: #666;
    border-radius: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

.sidebar-sublink:hover {
    background-color: #f5f5f5;
    color: #1a1a1a;
}

.sidebar-sublink.active {
    background-color: #f0f7ff;
    color: #0066ff;
}

.sidebar-sublink .sidebar-icon {
    font-size: 18px !important;
    margin-right: 10px;
}

.sidebar-link .ms-auto {
    margin-left: auto;
    font-size: 20px;
}

/* User Profile Styles */
.user-profile {
    margin-top: auto;
    padding-top: 16px;
}

.user-profile-divider {
    height: 1px;
    background-color: #eaeaea;
    margin-bottom: 8px;
    opacity: 0.6;
}

.user-dropdown-toggle {
    width: 100%;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    color: inherit !important;
    text-align: left;
}

.user-dropdown-toggle::after {
    display: none !important;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.user-info:hover {
    background-color: #f5f5f5;
}

.user-avatar {
    margin-right: 12px;
}

.avatar-icon {
    font-size: 40px !important;
    color: #0066ff;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: #1a1a1a;
}

.user-role {
    font-size: 12px;
    color: #666;
}

.user-dropdown-menu {
    width: 220px;
    padding: 8px;
    margin-top: 8px !important;
    border-radius: 8px;
    border: 1px solid #eaeaea;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.dropdown-icon {
    font-size: 18px !important;
    margin-right: 12px;
    color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 240px;
        padding: 20px 12px;
    }

    .sidebar-link {
        padding: 10px 14px;
    }

    .avatar-icon {
        font-size: 32px !important;
    }

    .user-name {
        font-size: 13px;
    }

    .user-role {
        font-size: 11px;
    }
}

