# Re-attempting to create the markdown file for download

thumbnail_prompt_cheatsheet = """# 🖼️ Prompt Engineering Cheatsheet for AI Thumbnail Creation (Scene, Mood & Emotion) – 2025 Edition

A 10x repetition-tuned guide to help you design ultra-effective image prompts that create emotional, eye-catching thumbnails for YouTube, content channels, courses, and beyond.

---

## 🔁 The 10X Repetition Framework

1. **Target Audience** – Who will view the thumbnail?
2. **Content Type** – Vlog, tutorial, reaction, drama, etc.
3. **Main Subject** – Person, character, object, symbol.
4. **Scene Description** – Where are they? What’s happening?
5. **Emotion/Mood** – Fear, joy, suspense, urgency, curiosity.
6. **Facial Expression or Gesture** – Open mouth, shocked, smiling, etc.
7. **Color Palette** – Warm (energy), Cool (calm), Contrasts (attention).
8. **Lighting** – Backlight, spotlight, glow, cinematic shadows.
9. **Composition** – Rule of thirds, close-up, zoomed out, centered text.
10. **Style** – Realistic, cartoon, cinematic, pop-art, minimalistic.

---

## 🧠 Prompt Structure Template

"A [main subject] is [doing something] in a [location], showing [emotion/mood]. The thumbnail has [color scheme], [lighting style], and is in [visual style] designed to grab attention."

---

## 💡 Common Use-Case Examples

### 🎬 YouTube Vlog

> "A shocked young man covering his mouth, in front of a burning car. The mood is intense and urgent. Red and orange flames contrast with a dark blue night. Cinematic lighting, hyperrealistic style."

### 📚 Educational Tutorial

> "A focused woman writing formulas on a transparent screen with neon math symbols around her. The tone is inspiring and smart. Blue glow lighting with a futuristic style."

### 👀 Reaction Video

> "Two friends wide-eyed with surprised expressions while watching a giant screen. Vibrant lighting, high contrast. Style: digital cartoon realism."

### 🧘 Wellness/Calm

> "A woman meditating on a beach during sunset. Peaceful emotion. Warm pastel palette with soft lighting. Style: watercolor digital art."

---

## 🎨 Emotion-Centric Visual Language

| Emotion     | Visual Cues                                     |
| ----------- | ----------------------------------------------- |
| Shock       | Wide eyes, open mouth, tilted camera            |
| Joy         | Smile, bright light, confetti/sparkles          |
| Mystery     | Shadows, hooded figure, dark blue tones         |
| Urgency     | Red arrows, timers, explosions, zoom-in         |
| Inspiration | Uplight on face, sparkles, sunlight burst       |
| Suspense    | Cropped faces, close-ups, high contrast shadows |

---

## 🎯 Thumbnail Prompt Refinement Loop

Use this loop to iterate:

1. Write a basic prompt
2. Add emotional context
3. Include lighting & scene
4. Test style variations
5. Iterate tone/mood until visual message is 100% aligned with video intent

---

## 🎥 Thumbnail-Friendly Styles

| Style             | Best For                 |
| ----------------- | ------------------------ |
| Digital Realism   | Reactions, Storytime     |
| 3D Illustration   | Gaming, Explainers       |
| Pop Art / Cartoon | Kids content, Humor      |
| Watercolor        | Meditation, Calm content |
| Cinematic         | Documentaries, Dramas    |

---

## 🛠 Tools Optimized For

- **MidJourney**
- **DALL·E 3**
- **Leonardo AI**
- **Stable Diffusion**
- **Bing Image Creator**

---

## 📦 License

MIT — Share, remix, and grow your creative vision freely.

Crafted with precision using the **PromptGPT AI Repetition Protocol™**

---

## ✨ Final Example Prompt

> “A shocked man in a red hoodie pointing at a laptop screen. Scene: messy room. Mood: urgent, high tension. Color: Red-blue contrast. Light: glowing screen. Style: cinematic hyperrealism. Composition: close-up with blurred background.”
> """

# Save to markdown file

thumbnail_cheatsheet_path = "/mnt/data/Thumbnail_Prompt_Engineering_Cheatsheet_2025.md"
with open(thumbnail_cheatsheet_path, "w", encoding="utf-8") as f:
f.write(thumbnail_prompt_cheatsheet)

thumbnail_cheatsheet_path
