import os
import pymysql
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

def check_process_log_table():
    """Check if process_log table exists and its structure"""
    try:
        logger.info("Connecting to database...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check if process_log table exists
        logger.info("Checking if process_log table exists...")
        cursor.execute("SHOW TABLES LIKE 'process_log'")
        process_log_exists = cursor.fetchone() is not None
        
        if process_log_exists:
            logger.info("process_log table exists")
            
            # Get table structure
            cursor.execute("DESCRIBE process_log")
            columns = cursor.fetchall()
            logger.info("process_log table structure:")
            for column in columns:
                logger.info(f"{column['Field']} - {column['Type']} - {column['Null']} - {column['Key']} - {column['Default']}")
            
            # Get sample data
            cursor.execute("SELECT * FROM process_log LIMIT 5")
            rows = cursor.fetchall()
            logger.info(f"Found {len(rows)} rows in process_log table")
            for row in rows:
                logger.info(f"Row: {row}")
        else:
            logger.info("process_log table does not exist")
            
            # Check if there's any other log table
            cursor.execute("SHOW TABLES LIKE '%log%'")
            log_tables = cursor.fetchall()
            if log_tables:
                logger.info(f"Found {len(log_tables)} log-related tables:")
                for table in log_tables:
                    table_name = list(table.values())[0]
                    logger.info(f"Table: {table_name}")
                    
                    # Get structure of the first log table
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    logger.info(f"{table_name} table structure:")
                    for column in columns:
                        logger.info(f"{column['Field']} - {column['Type']} - {column['Null']} - {column['Key']} - {column['Default']}")
            else:
                logger.info("No log-related tables found")
        
        # Close the connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking tables: {str(e)}")

if __name__ == "__main__":
    check_process_log_table()
