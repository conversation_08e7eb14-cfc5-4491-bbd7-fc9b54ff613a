import os
import pymysql
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

def check_tables():
    """Check content_prompt and content_prompt_claude tables"""
    try:
        logger.info("Connecting to database...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check if tables exist
        logger.info("Checking if content_prompt table exists...")
        cursor.execute("SHOW TABLES LIKE 'content_prompt'")
        content_prompt_exists = cursor.fetchone() is not None
        
        logger.info("Checking if content_prompt_claude table exists...")
        cursor.execute("SHOW TABLES LIKE 'content_prompt_claude'")
        content_prompt_claude_exists = cursor.fetchone() is not None
        
        logger.info(f"content_prompt table exists: {content_prompt_exists}")
        logger.info(f"content_prompt_claude table exists: {content_prompt_claude_exists}")
        
        # Check content in tables
        if content_prompt_exists:
            logger.info("Checking content in content_prompt table...")
            cursor.execute("SELECT id, title FROM content_prompt")
            content_prompts = cursor.fetchall()
            logger.info(f"Found {len(content_prompts)} records in content_prompt table")
            for prompt in content_prompts:
                logger.info(f"ID: {prompt['id']}, Title: {prompt['title']}")
        
        if content_prompt_claude_exists:
            logger.info("Checking content in content_prompt_claude table...")
            cursor.execute("SELECT id, topic FROM content_prompt_claude")
            claude_prompts = cursor.fetchall()
            logger.info(f"Found {len(claude_prompts)} records in content_prompt_claude table")
            for prompt in claude_prompts:
                logger.info(f"ID: {prompt['id']}, Topic: {prompt['topic']}")
        
        # Check for specific ID 14
        if content_prompt_exists:
            logger.info("Checking if ID 14 exists in content_prompt table...")
            cursor.execute("SELECT id, title FROM content_prompt WHERE id = 14")
            prompt_14 = cursor.fetchone()
            if prompt_14:
                logger.info(f"Found ID 14 in content_prompt table: {prompt_14['title']}")
            else:
                logger.info("ID 14 does not exist in content_prompt table")
        
        if content_prompt_claude_exists:
            logger.info("Checking if ID 14 exists in content_prompt_claude table...")
            cursor.execute("SELECT id, topic FROM content_prompt_claude WHERE id = 14")
            claude_14 = cursor.fetchone()
            if claude_14:
                logger.info(f"Found ID 14 in content_prompt_claude table: {claude_14['topic']}")
            else:
                logger.info("ID 14 does not exist in content_prompt_claude table")
        
        # Close the connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking tables: {str(e)}")

if __name__ == "__main__":
    check_tables()
