from moviepy.editor import ImageClip, concatenate_videoclips, AudioFileClip
from pathlib import Path
import os
import asyncio
import aiomysql
import os
from dotenv import load_dotenv
from datetime import datetime
import logging
import traceback
from typing import Optional, Any

load_dotenv()
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'db': os.getenv('DB_DATABASE'),
}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def log_event(pool: aiomysql.Pool, event_type: str, message: str, status: str = "INFO", error: Exception = None) -> None:
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def create_db_pool():
    return await aiomysql.create_pool(**DB_CONFIG)

async def generate_video_from_images_and_audio(
    content_id: str,
    base_dir: str = None,
    image_dir: str = "content-images",
    speech_dir: str = "content-speech",
    output_dir: str = "content-video",
    fps: int = 24
) -> str:
    """
    Generate a video from a sequence of images and an audio file asynchronously.
    
    Args:
        content_id (str): The ID of the content to process
        base_dir (str, optional): Base directory of the project
        image_dir (str, optional): Directory containing images
        speech_dir (str, optional): Directory containing speech audio
        output_dir (str, optional): Directory for output video
        fps (int, optional): Frames per second for output video
        
    Returns:
        str: Path to the generated video file
        
    Raises:
        ValueError: If no images are found or if required files are missing
    """
    try:
        # Run CPU-intensive operations in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, 
            lambda: _generate_video_sync(
                content_id, 
                base_dir, 
                image_dir, 
                speech_dir, 
                output_dir, 
                fps
            )
        )
    except Exception as e:
        raise Exception(f"Failed to generate video: {str(e)}")

def _generate_video_sync(
    content_id: str,
    base_dir: str,
    image_dir: str,
    speech_dir: str,
    output_dir: str,
    fps: int
) -> str:
    """Synchronous part of video generation."""
    video = None
    audio = None
    try:
        # Set base directory if not provided
        if base_dir is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            
        # Get paths for images and thumbnail
        image_directory = Path(os.path.join(base_dir, image_dir, content_id))
        thumbnail_path = Path(os.path.join(base_dir, "content-thumbnail", f"{content_id}", f"{content_id}.jpg"))
        audio_path = os.path.join(base_dir, speech_dir, f"{content_id}.mp3")
        output_path = os.path.join(base_dir, output_dir, f"{content_id}.mp4")
        
        # Ensure directories exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Validate input files exist
        if not image_directory.exists():
            raise ValueError(f"Image directory not found: {image_directory}")
        if not thumbnail_path.exists():
            raise ValueError(f"Thumbnail not found: {thumbnail_path}")
        if not os.path.exists(audio_path):
            raise ValueError(f"Audio file not found: {audio_path}")
            
        # Load the audio file
        audio = AudioFileClip(audio_path)
        
        # Get all image files and sort them
        image_files = sorted(image_directory.glob('*.jpg'))
        
        # Validate we have images
        num_images = len(image_files)
        if num_images == 0:
            raise ValueError(f"No images found in directory: {image_directory}")
            
        # Calculate duration for each image
        # First 2 seconds for thumbnail and distribute remaining time among other images
        thumbnail_duration = 2.0  # 2 seconds for thumbnail
        remaining_duration = audio.duration - thumbnail_duration
        image_duration = remaining_duration / num_images
        
        # Create thumbnail clip for the first 2 seconds
        thumbnail_clip = ImageClip(str(thumbnail_path)).set_duration(thumbnail_duration)
        
        # Create image clips for the remaining duration
        image_clips = [
            ImageClip(str(img)).set_duration(image_duration) 
            for img in image_files
        ]
        
        # Combine all clips with thumbnail at the start
        all_clips = [thumbnail_clip] + image_clips
        
        # Create video from images
        video = concatenate_videoclips(all_clips, method="compose")
        
        # Add audio to video
        video = video.set_audio(audio)
        
        # Export the final video
        video.write_videofile(output_path, fps=fps)
        
        return output_path
        
    finally:
        if video:
            video.close()
        if audio:
            audio.close()

async def get_pending_slideshow_content(pool):
    """
    Get all content IDs where slideshow hasn't been created yet.
    """
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT id 
                    FROM generated_content 
                    WHERE slideshow_created = 0 
                    ORDER BY id
                """)
                results = await cursor.fetchall()
                return [str(row[0]) for row in results]
    except Exception as e:
        print(f"Error fetching pending slideshow content: {e}")
        return []

async def verify_content_resources(content_id: str, base_dir: str = None) -> tuple[bool, str]:
    """
    Verify that all necessary resources exist for the given content ID.
    Returns (success, message).
    """
    if base_dir is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))

    # Define directories
    image_dir = "content-images"
    speech_dir = "content-speech"
    
    # Check paths
    image_directory = Path(os.path.join(base_dir, image_dir, content_id))
    audio_path = os.path.join(base_dir, speech_dir, f"{content_id}.mp3")
    
    # Verify image directory exists and contains images
    if not image_directory.exists():
        return False, f"Image directory not found: {image_directory}"
    
    image_files = list(image_directory.glob('*.jpg'))
    if not image_files:
        return False, f"No images found in directory: {image_directory}"
    
    # Verify audio file exists
    if not os.path.exists(audio_path):
        return False, f"Audio file not found: {audio_path}"
    
    return True, "All resources available"

async def mark_slideshow_created(pool, content_id: str):
    """
    Mark the content as having slideshow created.
    """
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    UPDATE generated_content 
                    SET slideshow_created = 1 
                    WHERE id = %s
                """, (content_id,))
                await conn.commit()
    except Exception as e:
        print(f"Error marking slideshow as created: {e}")

async def process_pending_slideshows(pool: aiomysql.Pool) -> None:
    """Process all pending slideshows with comprehensive logging"""
    try:
        # Log process start
        await log_event(pool, "SLIDESHOW_PROCESS_START", "Starting slideshow generation process")
        
        pending_content_ids = await get_pending_slideshow_content(pool)
        
        if not pending_content_ids:
            await log_event(pool, "SLIDESHOW_NO_PENDING", "No pending slideshows found")
            return
        
        await log_event(
            pool, 
            "SLIDESHOW_PENDING_COUNT", 
            f"Found {len(pending_content_ids)} pending slideshows"
        )
        
        for content_id in pending_content_ids:
            try:
                # Log content processing start
                await log_event(
                    pool,
                    "SLIDESHOW_CONTENT_START",
                    f"Starting processing for content ID: {content_id}"
                )
                
                # Verify resources
                resources_ok, message = await verify_content_resources(content_id)
                if not resources_ok:
                    await log_event(
                        pool,
                        "SLIDESHOW_RESOURCES_MISSING",
                        f"Content ID {content_id}: {message}",
                        "ERROR"
                    )
                    continue
                
                await log_event(
                    pool,
                    "SLIDESHOW_RESOURCES_VERIFIED",
                    f"Resources verified for content ID: {content_id}"
                )
                
                # Generate video
                try:
                    output_video = await generate_video_from_images_and_audio(
                        content_id=content_id,
                        image_dir="content-images",
                        speech_dir="content-speech",
                        output_dir="content-video"
                    )
                    
                    await log_event(
                        pool,
                        "SLIDESHOW_GENERATION_SUCCESS",
                        f"Successfully generated video for content ID {content_id}: {output_video}"
                    )
                    
                except Exception as e:
                    await log_event(
                        pool,
                        "SLIDESHOW_GENERATION_ERROR",
                        f"Failed to generate video for content ID {content_id}",
                        "ERROR",
                        e
                    )
                    continue
                
                # Mark as processed
                try:
                    await mark_slideshow_created(pool, content_id)
                    await log_event(
                        pool,
                        "SLIDESHOW_MARK_COMPLETE",
                        f"Marked content ID {content_id} as completed"
                    )
                except Exception as e:
                    await log_event(
                        pool,
                        "SLIDESHOW_MARK_ERROR",
                        f"Failed to mark content ID {content_id} as completed",
                        "ERROR",
                        e
                    )
                
            except Exception as e:
                await log_event(
                    pool,
                    "SLIDESHOW_CONTENT_ERROR",
                    f"Unexpected error processing content ID {content_id}",
                    "ERROR",
                    e
                )
                continue
                
    except Exception as e:
        await log_event(
            pool,
            "SLIDESHOW_PROCESS_ERROR",
            "Critical error in slideshow generation process",
            "ERROR",
            e
        )
    finally:
        await log_event(
            pool,
            "SLIDESHOW_PROCESS_END",
            "Ending slideshow generation process"
        )

async def main():
    """Main function with error handling and logging"""
    pool = None
    try:
        pool = await create_db_pool()
        await log_event(pool, "SCRIPT_START", "Starting video generation script")
        
        await process_pending_slideshows(pool)
        
        await log_event(pool, "SCRIPT_END", "Video generation script completed successfully")
        
    except Exception as e:
        if pool:
            await log_event(
                pool,
                "SCRIPT_ERROR",
                "Critical error in main script execution",
                "ERROR",
                e
            )
        else:
            logger.error("Failed to create database pool", exc_info=e)
    finally:
        if pool:
            pool.close()
            await pool.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())
