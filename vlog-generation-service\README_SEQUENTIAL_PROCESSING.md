# Sequential Task Processing in VisionFrame AI

This document explains how the VisionFrame AI queue system processes tasks sequentially by ID.

## Overview

The queue system is designed to process tasks from the `process_queue` table in sequential order by ID. This ensures that:

1. Tasks are processed in the order they were created
2. Tasks for the same content chunk are processed in the correct sequence
3. System resources are used efficiently by processing one task at a time

## How Sequential Processing Works

### 1. Task Retrieval

The `get_pending_tasks` function in `db_utils.py` retrieves pending tasks from the `process_queue` table:

```python
def get_pending_tasks(limit=1):
    """Get pending tasks from the process_queue table."""
    # ...
    cursor.execute("""
        SELECT * FROM process_queue
        WHERE status = 'pending'
        ORDER BY id ASC
        LIMIT %s
    """, (limit,))
    # ...
```

Key features:

- Tasks are ordered by `id ASC` to ensure sequential processing
- Only one task is retrieved at a time (`limit=1`)

### 2. Task Dispatching

The `check_pending_tasks` function in `queue_manager.py` dispatches tasks one at a time:

```python
@shared_task(name='tasks.queue_manager.check_pending_tasks')
def check_pending_tasks():
    """Check for pending tasks and dispatch them sequentially by ID."""
    # ...
    pending_tasks = get_pending_tasks(limit=1)
    # ...
    task = pending_tasks[0]
    dispatch_task.delay(task['id'])

    # Schedule another check after this task is dispatched
    check_pending_tasks.apply_async(countdown=5)
    # ...
```

Key features:

- Only one task is dispatched at a time
- After dispatching a task, another check is scheduled after a short delay
- This ensures tasks are processed one by one in ID order

### 3. Task Completion

When a task completes, the `task_completed` function in `task_utils.py` activates the next task in the sequence:

```python
@shared_task(name='tasks.task_utils.task_completed')
def task_completed(queue_id, result_data=None):
    """Handle task completion and activate the next task in the sequence."""
    # ...
    # Activate the next task in the sequence
    chunk_id = task['chunk_id']
    step_order = task['step_order']

    next_task = activate_next_task(chunk_id, step_order)
    # ...
```

Key features:

- When a task completes, the next task in the sequence for the same chunk is activated
- The next task's status is changed from 'waiting' to 'pending'
- The next task will be picked up by the `check_pending_tasks` function

### 4. Periodic Checking

The Celery beat scheduler periodically triggers the `check_pending_tasks` function:

```python
# Beat schedule
beat_schedule = {
    'check-pending-tasks-every-10-seconds': {
        'task': 'tasks.queue_manager.check_pending_tasks',
        'schedule': 10.0,
    },
}
```

Key features:

- The scheduler checks for pending tasks every 10 seconds
- This ensures that tasks are processed even if the self-scheduling mechanism fails

## Task States and Timestamps

Tasks in the `process_queue` table can have the following states:

1. **pending**: Task is ready to be processed
2. **processing**: Task is currently being processed (sets `start_time`)
3. **completed**: Task has been successfully completed (sets `end_time`)
4. **failed**: Task has failed (sets `end_time`)
5. **waiting**: Task is waiting for a prerequisite task to complete

Each task has the following timestamp fields:

- **start_time**: Set when the task status changes to 'processing'
- **end_time**: Set when the task status changes to 'completed' or 'failed'
- **created_at**: Set when the task is created
- **updated_at**: Updated whenever the task is modified

These timestamps are used to:

1. Track how long each task takes to complete
2. Calculate execution statistics for different types of tasks
3. Identify long-running tasks that might be stuck
4. Optimize the processing pipeline based on performance data

## Task Sequence

For each content chunk, tasks are processed in the following sequence:

1. Tasks are ordered by the `step_order` field
2. When a task completes, the next task in the sequence is activated
3. Tasks with the same `step_order` but different `chunk_id` are processed in order of their `id`

## Monitoring

You can monitor the sequential processing of tasks using the following methods:

1. Check the logs for messages like:

   - "Found pending task: {task_id}"
   - "Dispatched task {task_id}"
   - "Task {task_id} completed, next task {next_task_id} activated"
   - "Task {task_id} execution time: {seconds} seconds"

2. Query the `process_queue` table:

   ```sql
   SELECT id, chunk_id, process_step, status, step_order, created_at, start_time, end_time
   FROM process_queue
   ORDER BY id ASC;
   ```

3. Use the API endpoints:

   ```
   GET /api/queue/status
   GET /api/queue/stats
   ```

4. Use the command-line interface:
   ```bash
   python media_queue_bridge.py get_status
   python media_queue_bridge.py get_stats
   ```

## Task Execution Statistics

The system tracks execution times for all tasks and provides statistics through the `/api/queue/stats` endpoint. This includes:

1. **Execution Statistics by Process Step**:

   - Total number of tasks
   - Average execution time
   - Minimum execution time
   - Maximum execution time

2. **Status Counts**:

   - Number of tasks in each status (pending, processing, completed, failed, waiting)

3. **Currently Processing Tasks**:

   - Task ID
   - Process step
   - Chunk ID
   - Start time
   - Running time

4. **Tasks with Missing Timestamps**:
   - Count of tasks that have completed or failed but are missing start_time or end_time

These statistics can be used to:

1. Identify bottlenecks in the processing pipeline
2. Optimize resource allocation
3. Detect stuck or long-running tasks
4. Monitor the overall health of the queue system

## Troubleshooting

If tasks are not being processed sequentially:

1. Check if the Celery worker is running
2. Check if the Celery beat scheduler is running
3. Check if there are any errors in the logs
4. Verify that the `process_queue` table has tasks with status 'pending'
5. Restart the queue system
