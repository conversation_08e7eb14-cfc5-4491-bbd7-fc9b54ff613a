# Task Queue and Media Generation Integration

This document describes the integration between the task queue system and the media generation service in VisionFrame AI.

## Overview

The integration allows content chunks to be processed through the media generation pipeline using the task queue system. This provides several benefits:

1. **Asynchronous Processing**: Content chunks are processed in the background without blocking the user interface
2. **Parallel Processing**: Multiple chunks can be processed simultaneously
3. **Error Handling**: Failed tasks can be retried or resubmitted
4. **Monitoring**: Processing status can be tracked and monitored
5. **Memory Optimization**: Each chunk is processed independently, releasing memory after completion

## Components

The integration consists of the following components:

### 1. Media Tasks (`tasks/media_tasks.py`)

This module provides Celery tasks for media generation:

- `generate_media`: Processes a content chunk through the entire media generation pipeline
- `run_pipeline_step`: Runs a specific step of the media generation pipeline for a content chunk

### 2. Queue API Endpoints (`queue_api.py`)

New API endpoints for media generation:

- `POST /api/queue/media/submit`: Submit chunks to the media generation pipeline
- `POST /api/queue/media/step`: Submit chunks to a specific step of the pipeline
- `GET /api/queue/media/steps`: Get the available steps in the pipeline

### 3. Media Queue Bridge (`media_queue_bridge.py`)

A utility module that provides functions to interact with the queue API:

- `submit_chunks_to_media_pipeline`: Submit chunks to the media generation pipeline
- `submit_chunks_to_pipeline_step`: Submit chunks to a specific step of the pipeline
- `get_pipeline_steps`: Get the available steps in the pipeline
- `get_queue_status`: Get the status of the processing queue
- `retry_task`: Retry a failed task
- `clear_queue`: Clear the processing queue

## Usage

### Command Line Interface

The media queue bridge provides a command-line interface for interacting with the queue:

```bash
# Submit chunks to the media generation pipeline
python media_queue_bridge.py submit_media 1 2 3

# Submit chunks to a specific step of the pipeline
python media_queue_bridge.py submit_step "Generate Images" 1 2 3

# Get the available steps in the pipeline
python media_queue_bridge.py get_steps

# Get the status of the processing queue
python media_queue_bridge.py get_status

# Retry a failed task
python media_queue_bridge.py retry 123

# Clear the processing queue
python media_queue_bridge.py clear
```

### Python API

You can also use the media queue bridge as a Python module:

```python
from media_queue_bridge import (
    submit_chunks_to_media_pipeline,
    submit_chunks_to_pipeline_step,
    get_pipeline_steps,
    get_queue_status,
    retry_task,
    clear_queue
)

# Submit chunks to the media generation pipeline
result = submit_chunks_to_media_pipeline([1, 2, 3])

# Submit chunks to a specific step of the pipeline
result = submit_chunks_to_pipeline_step([1, 2, 3], "Generate Images")

# Get the available steps in the pipeline
steps = get_pipeline_steps()

# Get the status of the processing queue
status = get_queue_status()

# Retry a failed task
result = retry_task(123)

# Clear the processing queue
result = clear_queue()
```

## Process Flow

1. **Submission**: Content chunks are submitted to the queue through the API or bridge
2. **Queuing**: The chunks are added to the process_queue table with status 'pending'
3. **Processing**: The queue manager checks for pending tasks and dispatches them to the appropriate workers
4. **Execution**: The media tasks execute the media generation pipeline for each chunk
5. **Completion**: The tasks update the status of the chunks and queue items upon completion
6. **Memory Release**: Memory is released after each step and chunk to optimize resource usage

## Error Handling

1. **Task Retries**: Failed tasks can be automatically retried up to a configurable number of times
2. **Manual Retry**: Failed tasks can be manually retried through the API or bridge
3. **Error Logging**: Errors are logged to both file and database for debugging
4. **Status Tracking**: The status of each task is tracked in the process_queue table

## Memory Optimization

The integration optimizes memory usage in several ways:

1. **Chunk-based Processing**: Each chunk is processed independently
2. **Step-by-step Processing**: Each step releases memory after completion
3. **Garbage Collection**: Explicit garbage collection is performed after each step and chunk
4. **Object Deletion**: Large objects are explicitly deleted when no longer needed

## Configuration

The integration can be configured through environment variables:

- `API_BASE_URL`: The base URL of the API (default: http://localhost:5000)
- `DB_HOST`: The database host (default: localhost)
- `DB_USER`: The database user (default: root)
- `DB_PASSWORD`: The database password
- `DB_DATABASE`: The database name (default: vlog_generator)

## Database Schema

The integration uses the following database tables:

### process_queue

```sql
CREATE TABLE process_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_id INT,
    chunk_id INT NOT NULL,
    process_step VARCHAR(50) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'waiting') DEFAULT 'pending',
    step_order INT NOT NULL,
    result_data JSON,
    error_message TEXT,
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chunk_id) REFERENCES content_chunk(id) ON DELETE CASCADE
)
```

### content_chunk

```sql
CREATE TABLE content_chunk (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_id INT NOT NULL,
    chunk_order INT NOT NULL,
    text TEXT NOT NULL,
    processing_status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (content_id) REFERENCES generated_content(id)
)
```

### event_logs

```sql
CREATE TABLE event_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'INFO',
    message TEXT,
    error_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
