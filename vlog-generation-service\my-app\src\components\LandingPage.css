/* LandingPage.css */

.landing-page {
  font-family: 'Poppins', sans-serif;
}

.landing-page h1,
.landing-page h2,
.landing-page h3,
.landing-page h4,
.landing-page h5 {
  font-weight: 700;
}

.landing-page p {
  font-weight: 400;
  line-height: 1.6;
}

/* Hero Section */
.hero-content h1 {
  animation: fadeInUp 1s ease-out;
  letter-spacing: 0.5px;
}

.hero-content p {
  animation: fadeInUp 1s ease-out 0.2s;
  animation-fill-mode: both;
  letter-spacing: 0.3px;
}

.hero-content .d-flex {
  animation: fadeInUp 1s ease-out 0.4s;
  animation-fill-mode: both;
}

.hero-text-container {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: 1px solid #f0f4ff;
}

.hero-image-container {
  animation: fadeIn 1.5s ease-out;
  transition: transform 0.5s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.hero-image-container:hover {
  transform: scale(1.02);
}

.hero-content Badge {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.hero-content Badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  background-color: #1a2f8a !important;
}

/* Feature Cards */
.feature-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-item:hover img {
  transform: scale(1.1);
}

.feature-image-container img {
  transition: transform 0.5s ease;
}

.coming-soon-badge {
  animation: pulse 2s infinite;
}

/* Steps Section */
.step-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 5px solid transparent;
}

.step-item:hover {
  transform: translateX(10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.step-item:nth-child(1) {
  border-left-color: #0d6efd; /* primary */
}

.step-item:nth-child(2) {
  border-left-color: #198754; /* success */
}

.step-item:nth-child(3) {
  border-left-color: #dc3545; /* danger */
}

/* Project Details */
.project-details {
  transition: transform 0.3s ease;
}

.project-details:hover {
  transform: translateY(-5px);
}

/* CTA Section */
.cta-content {
  animation: fadeIn 1s ease-out;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(10px);
  }
  50% {
    transform: translateY(0) translateX(25px);
  }
  75% {
    transform: translateY(15px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }

  .hero-section h1 {
    font-size: 2.5rem;
  }

  .step-item:hover {
    transform: translateX(0);
  }
}
