import pymysql
import csv
import re
from dotenv import load_dotenv
import os
from datetime import datetime

def clean_scenario(scenario):
    """Remove numbering from the beginning of the scenario."""
    # Remove patterns like "1.", "2.", etc. from the start of the string
    cleaned = re.sub(r'^\d+\.\s*', '', scenario.strip())
    return cleaned

def scenario_exists(cursor, scenario):
    """Check if a scenario already exists in the database."""
    cursor.execute(
        "SELECT COUNT(*) as count FROM generated_content WHERE scenario = %s",
        (scenario,)
    )
    result = cursor.fetchone()
    return result['count'] > 0

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

# Path to CSV file - using relative path
CSV_FILE_PATH = os.path.join("content-csv", "content1.csv")

# Function to log events to database
def log_event(cursor, event_type, message, status="INFO", error=None):
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute(
            "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
            "VALUES (%s, %s, %s, %s, %s)",
            (timestamp, event_type, status, message, str(error) if error else None)
        )
    except Exception as e:
        print(f"Failed to log event: {e}")

def read_csv_with_fallback_encoding(file_path):
    """Try different encodings to read the CSV file"""
    encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']
    
    for encoding in encodings:
        try:
            # Read the entire content of the file
            with open(file_path, encoding=encoding) as file:
                content = list(csv.reader(file))
                return content  # Return the entire content as a list
        except UnicodeDecodeError:
            continue
    
    raise UnicodeDecodeError(
        "Unable to read the CSV file. Please ensure it's saved with UTF-8 encoding."
    )

try:
    # Verify if file exists
    if not os.path.exists(CSV_FILE_PATH):
        print(f"Error: CSV file not found at {os.path.abspath(CSV_FILE_PATH)}")
        print("Please ensure the file exists and the path is correct")
        exit(1)

    # Connect to MySQL database
    conn = pymysql.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    )
    cursor = conn.cursor()

    # SQL query to insert data
    insert_query = """
    INSERT INTO generated_content 
    (scenario, empathetic_advice, practical_advice, thumbnail_prompt)
    VALUES (%s, %s, %s, %s)
    """

    try:
        # Read the CSV content
        csv_content = read_csv_with_fallback_encoding(CSV_FILE_PATH)
        if not csv_content:
            raise Exception("CSV file is empty")
            
        header = csv_content[0]  # Get header
        rows = csv_content[1:]   # Get all rows except header
        
        print(f"CSV Header: {header}")  # Debug print
        print(f"Number of columns in header: {len(header)}")
        
        rows_processed = 0
        rows_successful = 0
        rows_skipped = 0
        
        for row in rows:
            rows_processed += 1
            print(f"\nProcessing row {rows_processed}: {row}")
            print(f"Number of columns in row: {len(row)}")
            
            # Ensure row has exactly 4 values
            if len(row) != 4:
                print(f"Warning: Row {rows_processed} has {len(row)} columns instead of 4")
                print(f"Row content: {row}")
                continue
            
            try:
                # Extract the 4 required columns
                scenario = clean_scenario(row[0])  # Clean the scenario before using it
                empathetic_advice = row[1]
                practical_advice = row[2]
                thumbnail_prompt = row[3]
                
                # Check if scenario already exists
                if scenario_exists(cursor, scenario):
                    print(f"Skipping row {rows_processed}: Scenario already exists")
                    log_event(cursor, "DUPLICATE_SCENARIO", f"Skipped duplicate scenario: {scenario}")
                    rows_skipped += 1
                    continue
                
                # Log the cleaning operation
                log_event(cursor, "SCENARIO_CLEANED", f"Original: {row[0]} -> Cleaned: {scenario}")
                
                # Create tuple of values for insertion
                values = (scenario, empathetic_advice, practical_advice, thumbnail_prompt)
                
                # Execute insert
                cursor.execute(insert_query, values)
                rows_successful += 1
                print(f"Successfully inserted row {rows_processed}")
                
            except Exception as e:
                print(f"Error inserting row {rows_processed}: {e}")
                print(f"Row data: {row}")
                log_event(cursor, "ROW_PROCESSING_ERROR", f"Error processing row {rows_processed}", "ERROR", e)
                continue

        # Commit the transaction
        conn.commit()
        print(f"\nImport Summary:")
        print(f"Total rows processed: {rows_processed}")
        print(f"Successfully inserted: {rows_successful}")
        print(f"Skipped (duplicates): {rows_skipped}")
        print(f"Failed rows: {rows_processed - rows_successful - rows_skipped}")

    except Exception as e:
        print(f"Error processing CSV: {e}")
        raise

except pymysql.MySQLError as e:
    print(f"MySQL Error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
    print(f"Error type: {type(e)}")
finally:
    if 'conn' in locals() and conn:
        conn.close()
        print("Database connection closed")
