#!/usr/bin/env python3
"""
Check what's available in MoviePy installation
"""

print("🔍 Checking MoviePy Installation")
print("=" * 40)

try:
    import moviepy
    print(f"✅ MoviePy imported successfully")
    print(f"📦 MoviePy version: {moviepy.__version__}")
    print(f"📁 MoviePy location: {moviepy.__file__}")
    
    print("\n🔍 Available in moviepy module:")
    moviepy_attrs = [attr for attr in dir(moviepy) if not attr.startswith('_')]
    for attr in sorted(moviepy_attrs):
        print(f"  - {attr}")
    
except ImportError as e:
    print(f"❌ MoviePy import failed: {e}")
    exit(1)

print("\n" + "=" * 40)
print("🔍 Testing specific imports:")

# Test different import methods
import_tests = [
    ("moviepy.editor", "from moviepy.editor import VideoFileClip"),
    ("moviepy", "from moviepy import VideoFileClip"),
    ("moviepy.video.io.VideoFileClip", "from moviepy.video.io.VideoFileClip import VideoFileClip"),
    ("moviepy.video.VideoClip", "from moviepy.video.VideoClip import VideoClip"),
]

for module_name, import_statement in import_tests:
    try:
        exec(import_statement)
        print(f"✅ {import_statement}")
    except ImportError as e:
        print(f"❌ {import_statement} - {e}")

print("\n" + "=" * 40)
print("🔍 Checking moviepy submodules:")

try:
    import moviepy.video
    print("✅ moviepy.video available")
    
    try:
        import moviepy.video.io
        print("✅ moviepy.video.io available")
        
        try:
            from moviepy.video.io.VideoFileClip import VideoFileClip
            print("✅ VideoFileClip from moviepy.video.io.VideoFileClip")
        except ImportError as e:
            print(f"❌ VideoFileClip from moviepy.video.io.VideoFileClip - {e}")
            
    except ImportError as e:
        print(f"❌ moviepy.video.io - {e}")
        
except ImportError as e:
    print(f"❌ moviepy.video - {e}")

try:
    import moviepy.audio
    print("✅ moviepy.audio available")
except ImportError as e:
    print(f"❌ moviepy.audio - {e}")

print("\n" + "=" * 40)
print("🔍 Checking for editor module:")

try:
    import moviepy.editor
    print("✅ moviepy.editor module exists")
    
    editor_attrs = [attr for attr in dir(moviepy.editor) if not attr.startswith('_')]
    print(f"📋 Available in moviepy.editor ({len(editor_attrs)} items):")
    for attr in sorted(editor_attrs):
        print(f"  - {attr}")
        
except ImportError as e:
    print(f"❌ moviepy.editor module not found: {e}")

print("\n" + "=" * 40)
print("🔍 Alternative import attempts:")

alternatives = [
    "from moviepy.video.io.VideoFileClip import VideoFileClip",
    "from moviepy.audio.io.AudioFileClip import AudioFileClip", 
    "from moviepy.video.VideoClip import ImageClip",
    "from moviepy.video.VideoClip import ColorClip",
    "from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip",
    "from moviepy.video.compositing.concatenate import concatenate_videoclips",
]

working_imports = []
for import_stmt in alternatives:
    try:
        exec(import_stmt)
        print(f"✅ {import_stmt}")
        working_imports.append(import_stmt)
    except ImportError as e:
        print(f"❌ {import_stmt} - {e}")

if working_imports:
    print(f"\n🎉 Found {len(working_imports)} working import(s)!")
    print("💡 Use these imports in your code:")
    for imp in working_imports:
        print(f"  {imp}")
else:
    print("\n⚠️ No working imports found. MoviePy installation may be incomplete.")

print("\n" + "=" * 40)
print("✅ MoviePy diagnostic complete!")
