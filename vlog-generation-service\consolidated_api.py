import os
import sys
import logging
import json
import requests
import traceback
import pymysql
import datetime
import re
from flask import Flask, jsonify, request, Blueprint
from flask_cors import CORS
from dotenv import load_dotenv
import asyncio
from generate_chunks import generate_chunks_for_content

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

# API setup for multiple providers
XAI_API_KEY = os.getenv('XAI_API_KEY')
DEEPSEEK_API_KEY = os.getenv('DEEKSEEK_API_KEY')  # Note: Using the env var name as defined in .env
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# API URLs
GROK_API_URL = "https://api.x.ai/v1/chat/completions"
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"

# Configure detailed logging
logger.info(f"XAI_API_KEY configured: {XAI_API_KEY is not None}")
logger.info(f"DEEPSEEK_API_KEY configured: {DEEPSEEK_API_KEY is not None}")
logger.info(f"GEMINI_API_KEY configured: {GEMINI_API_KEY is not None}")
logger.info(f"GROK_API_URL: {GROK_API_URL}")
logger.info(f"Database configured: {DB_HOST}, {DB_USER}, {DB_NAME}")

# Create Flask app
app = Flask(__name__)

# Enable CORS for all routes
CORS(app, resources={
    r"/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Cache-Control", "Pragma", "Accept"]
    }
})

# Create Grok Blueprint
grok_bp = Blueprint('grok', __name__, url_prefix='/api/grok')

# Create Content Blueprint
content_bp = Blueprint('content', __name__, url_prefix='/api')

# Helper functions
def check_table_exists(cursor, table_name):
    """Check if a table exists in the database"""
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None

# Initialize database tables
def init_db():
    """Initialize database tables if they don't exist"""
    conn = None
    cursor = None
    try:
        logger.info("Initializing database tables...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if category table exists
        if not check_table_exists(cursor, 'category'):
            logger.info("Creating category table...")
            cursor.execute("""
                CREATE TABLE category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)

            # Insert some default categories
            cursor.execute("""
                INSERT INTO category (name, description, status) VALUES
                ('General', 'General purpose content', 'active'),
                ('Lifestyle', 'Lifestyle related content', 'active'),
                ('Health', 'Health and wellness content', 'active'),
                ('Technology', 'Technology related content', 'active'),
                ('Relationships & Love Advice', 'Relationship and love advice content', 'active')
            """)
            conn.commit()
            logger.info("Category table created and populated successfully")
        else:
            # Check if status column exists in category table
            try:
                cursor.execute("SELECT status FROM category LIMIT 1")
            except Exception:
                # Add status column if it doesn't exist
                logger.info("Adding status column to category table...")
                cursor.execute("ALTER TABLE category ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'")
                conn.commit()
                logger.info("Status column added to category table successfully")

        # Check if category_title table exists
        if not check_table_exists(cursor, 'category_title'):
            logger.info("Creating category_title table...")
            cursor.execute("""
                CREATE TABLE category_title (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    category_id INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    is_used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES category(id) ON DELETE CASCADE
                )
            """)
            conn.commit()
            logger.info("Category_title table created successfully")

        # Check if content_prompt_claude table exists
        if not check_table_exists(cursor, 'content_prompt_claude'):
            logger.info("Creating content_prompt_claude table...")
            cursor.execute("""
                CREATE TABLE content_prompt_claude (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    topic VARCHAR(255) NOT NULL,
                    category_id INT,
                    content_type VARCHAR(50) DEFAULT 'blog_post',
                    purpose VARCHAR(50) DEFAULT 'inform',
                    audience VARCHAR(100) DEFAULT 'general',
                    tone VARCHAR(50) DEFAULT 'conversational',
                    style VARCHAR(50) DEFAULT 'descriptive',
                    word_count VARCHAR(50) DEFAULT '500',
                    persona VARCHAR(50) DEFAULT 'expert',
                    content_prompt TEXT,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'standard',
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES category(id)
                )
            """)
            conn.commit()
            logger.info("Content_prompt_claude table created successfully")

        # Check if content_prompt table exists (keep for backward compatibility)
        if not check_table_exists(cursor, 'content_prompt'):
            logger.info("Creating content_prompt table...")
            cursor.execute("""
                CREATE TABLE content_prompt (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    category_id INT,
                    content_prompt TEXT NOT NULL,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'standard',
                    voice_id VARCHAR(100),
                    video_format VARCHAR(50) DEFAULT '1min',
                    tone VARCHAR(50),
                    goal VARCHAR(50),
                    target_audience VARCHAR(100),
                    format_type VARCHAR(50),
                    multiple_topics BOOLEAN DEFAULT FALSE,
                    num_sentences INT DEFAULT 5,
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES category(id)
                )
            """)
            conn.commit()
            logger.info("Content_prompt table created successfully")
        else:
            # Check if new columns exist in content_prompt table
            try:
                cursor.execute("SELECT tone FROM content_prompt LIMIT 1")
            except Exception:
                # Add new columns if they don't exist
                logger.info("Adding new columns to content_prompt table...")
                cursor.execute("ALTER TABLE content_prompt ADD COLUMN tone VARCHAR(50) AFTER video_format, ADD COLUMN goal VARCHAR(50) AFTER tone, ADD COLUMN target_audience VARCHAR(100) AFTER goal, ADD COLUMN format_type VARCHAR(50) AFTER target_audience")
                conn.commit()
                logger.info("New columns added to content_prompt table successfully")

        # Check if generated_content table exists
        if not check_table_exists(cursor, 'generated_content'):
            logger.info("Creating generated_content table...")
            cursor.execute("""
                CREATE TABLE generated_content (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    content_prompt_id INT,
                    title VARCHAR(255),
                    content_text TEXT,
                    thumbnail_prompt TEXT,
                    speech_duration INT,
                    speech_location TEXT,
                    num_images INT,
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (content_prompt_id) REFERENCES content_prompt(id) ON DELETE SET NULL
                )
            """)
            conn.commit()
            logger.info("Generated_content table created successfully")

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Initialize database on startup
init_db()

# Helper function for generating prompts based on cheatsheets
def generate_cheatsheet_prompt(data):
    """Generate prompts for content or image creation based on cheatsheets"""
    logger.info(f"Generating cheatsheet prompt for type: {data.get('prompt_type')}")

    prompt_type = data.get('prompt_type')
    topic = data.get('topic', '')
    content_type = data.get('content_type', 'blog post')
    audience = data.get('audience', 'general audience')
    tone = data.get('tone', 'informative')
    emotion = data.get('emotion', 'neutral')
    style = data.get('style', 'digital realism')
    purpose = data.get('purpose', 'inform')
    word_count = data.get('word_count', '500')
    persona = data.get('persona', 'expert')

    # Advanced thumbnail parameters
    provider = data.get('provider', 'xai')
    main_subject = data.get('main_subject', 'person')
    subject_details = data.get('subject_details', '')
    subject_position = data.get('subject_position', 'center')
    scene_environment = data.get('scene_environment', '')
    primary_emotion = data.get('primary_emotion', 'curiosity')
    facial_expression = data.get('facial_expression', 'neutral')
    psychological_trigger = data.get('psychological_trigger', 'curiosity_gap')
    color_palette = data.get('color_palette', 'vibrant')
    lighting = data.get('lighting', 'dramatic')
    composition = data.get('composition', 'rule_of_thirds')
    visual_style = data.get('visual_style', 'photorealistic')
    include_text = data.get('include_text', True)
    text_content = data.get('text_content', '')
    text_style = data.get('text_style', 'bold_contrast')
    aspect_ratio = data.get('aspect_ratio', '16_9')
    resolution = data.get('resolution', 'high')
    platform = data.get('platform', 'youtube')

    if not topic:
        return jsonify({'error': 'Topic is required'}), 400

    try:
        # Different prompt templates based on the type
        if prompt_type == 'content_creation':
            # Use the C.A.T.S. framework from the cheatsheet
            prompt = f"""
            Act as a professional content creator specializing in {content_type}s.

            Create a detailed prompt that will generate high-quality content about "{topic}" for {audience}.
            Keep the tone {tone} and follow the C.A.T.S. framework (Content Type + Audience + Tone + Style).

            Structure your prompt with:
            1. A compelling hook
            2. 3-5 key points to cover
            3. A clear call to action

            Make the prompt specific, actionable, and designed to generate engaging content.
            Return only the prompt text without any explanations or meta-commentary.
            """
        elif prompt_type == 'image_generation':
            # Use the S.M.A.R.T.A.R.T framework from the cheatsheet
            prompt = f"""
            Act as a professional image prompt engineer.

            Create a detailed image generation prompt about "{topic}" following the S.M.A.R.T.A.R.T framework:
            Subject + Mood + Action + Realism + Time + Atmosphere + Rendering + Tone

            The prompt should be highly descriptive, including:
            - Clear subject description
            - Emotional mood/tone ({emotion})
            - Lighting and atmosphere
            - Style and rendering specifications ({style})
            - Camera angle or perspective (if relevant)

            Make the prompt specific, visual, and designed to generate a striking image.
            Return only the prompt text without any explanations or meta-commentary.
            """
        elif prompt_type == 'thumbnail_generation':
            # Use the 10X Repetition Framework from the thumbnail cheatsheet
            prompt = f"""
            Act as a professional thumbnail designer for {content_type}.

            Create a detailed prompt for generating an eye-catching thumbnail image about "{topic}".
            The thumbnail should evoke a {emotion} emotion and use a {style} visual style.

            Follow the 10X Repetition Framework for thumbnail creation:
            1. Target Audience: {audience}
            2. Content Type: {content_type}
            3. Main Subject: Clearly describe the main subject related to {topic}
            4. Scene Description: Describe an engaging scene
            5. Emotion/Mood: {emotion}
            6. Facial Expression or Gesture: Include appropriate expressions if people are involved
            7. Color Palette: Suggest colors that evoke {emotion}
            8. Lighting: Describe lighting that enhances the mood
            9. Composition: Suggest a composition that draws attention
            10. Style: {style}

            IMPORTANT: Also create a short, attention-grabbing text overlay (1-4 words) that would appear on the thumbnail.
            The text should be catchy, relevant to the topic, and evoke the {emotion} emotion.

            Format your response as a JSON object with two fields:
            1. "image_prompt": A detailed prompt paragraph for the image generation
            2. "text_overlay": The short text to overlay on the thumbnail

            Make the image prompt specific, visual, and designed to create a thumbnail that will get clicks.
            Make the text overlay bold, catchy, and emotionally resonant.
            """
        elif prompt_type == 'claude_prompt':
            # Map content type values to readable labels
            content_type_map = {
                'blog_post': 'Blog Post/Article',
                'video_script': 'Video Script',
                'story': 'Story/Narrative',
                'essay': 'Essay/Academic Writing',
                'social_media': 'Social Media Content',
                'marketing_copy': 'Marketing Copy',
                'educational_content': 'Educational Content',
                'vlog_script': 'Vlog Script',
                'analysis': 'Analysis/Commentary'
            }

            # Get the readable content type
            content_type_label = content_type_map.get(content_type, content_type)

            # Use the Ultimate Prompt Engineering Cheatsheet for Claude
            # Format word count appropriately
            word_count_display = f"{word_count}-word" if word_count.isdigit() else f"approximately {word_count} words"

            prompt = f"""
            Act as a prompt engineering expert specializing in creating prompts for Claude AI.

            Create a detailed, well-structured prompt for Claude to generate a {word_count_display} {content_type_label} about "{topic}".

            The prompt should follow the Ultimate Prompt Engineering Cheatsheet for Content Creators and include:

            1. Clear instructions for Claude to act as a {persona} in {content_type_label}
            2. Specific purpose: To {purpose} the audience about {topic}
            3. Target audience: {audience}
            4. Tone: {tone}
            5. Writing style: {style}
            6. Word count: Approximately {word_count} words
            7. Structure requirements with numbered sections
            8. Specific elements to include (examples, data points, etc.)
            9. Format specifications
            10. Any additional parameters that would make the content more effective

            Make the prompt highly specific, well-structured, and designed to generate high-quality content from Claude.
            Include advanced techniques like chain-of-thought reasoning where appropriate.
            Format the prompt in a way that's easy to copy and paste directly to Claude.

            Return only the prompt text without any explanations, introductions, or meta-commentary.
            """
        else:  # advanced_thumbnail
            # Select the appropriate API based on provider
            api_provider = provider.lower()

            # Format aspect ratio for display
            aspect_ratio_display = aspect_ratio.replace('_', ':')

            # Base prompt template for all providers
            base_prompt = f"""
            Act as a professional thumbnail designer specializing in high-CTR thumbnails for {platform}.

            Create a detailed prompt for generating a thumbnail image about "{topic}" for {content_type} content targeted at {audience} audience.

            IMPORTANT STYLE GUIDANCE: If the color palette is 'dark' and the text style is 'bold_contrast', create a thumbnail with a strong contrast black and red background. The text should be in large, bold, uppercase white and red letters. If there's a question mark in the text overlay, split it into a main question (larger text) and subtitle (smaller text). Include a person holding out a heart while another person turns away if appropriate for the topic.

            The thumbnail should follow the Ultimate Thumbnail Prompt Engineering Master Guide framework with these specifications:

            CORE FOUNDATIONS:
            - Core Message: {topic} - {content_type}

            SUBJECT & SCENE:
            - Main Subject: {main_subject} {subject_details if subject_details else ''}
            - Subject Positioning: {subject_position}
            - Scene Environment: {scene_environment if scene_environment else 'appropriate for the topic'}

            EMOTIONAL & PSYCHOLOGICAL TRIGGERS:
            - Primary Emotion: {primary_emotion}
            - Facial Expression: {facial_expression}
            - Psychological Pattern Interrupt: {psychological_trigger}

            VISUAL DESIGN ELEMENTS:
            - Color Palette: {color_palette}
            - Lighting: {lighting}
            - Composition: {composition}
            - Visual Style: {visual_style}

            TECHNICAL SPECIFICATIONS:
            - Aspect Ratio: {aspect_ratio_display}
            - Resolution: {resolution}
            - Platform Optimization: {platform}
            """

            # Provider-specific additions
            if api_provider == 'xai':
                prompt = base_prompt + f"""

                FORMAT YOUR RESPONSE AS A JSON OBJECT with these fields:
                1. "image_prompt": A detailed, specific prompt for XAI/Grok to generate the thumbnail image
                2. "text_overlay": {f'The text "{text_content}" styled as {text_style}' if text_content else f'A short, attention-grabbing text overlay in {text_style} style'}

                The image_prompt should be highly detailed and specific, focusing on creating a thumbnail that will achieve high CTR on {platform}.
                {'' if include_text else 'Do not include any text overlay in the image itself.'}
                """
            elif api_provider == 'deepseek':
                prompt = base_prompt + f"""

                FORMAT YOUR RESPONSE AS A JSON OBJECT with these fields:
                1. "image_prompt": A detailed, specific prompt optimized for DeepSeek image generation
                2. "text_overlay": {f'The text "{text_content}" styled as {text_style}' if text_content else f'A short, attention-grabbing text overlay in {text_style} style'}

                For DeepSeek, focus on clear, detailed descriptions and avoid overly complex instructions.
                Use simple, direct language that clearly describes the visual elements.
                {'' if include_text else 'Do not include any text overlay in the image itself.'}
                """
            else:  # gemini
                prompt = base_prompt + f"""

                FORMAT YOUR RESPONSE AS A JSON OBJECT with these fields:
                1. "image_prompt": A detailed, specific prompt optimized for Gemini image generation
                2. "text_overlay": {f'The text "{text_content}" styled as {text_style}' if text_content else f'A short, attention-grabbing text overlay in {text_style} style'}

                For Gemini, use descriptive language that focuses on the mood, style, and composition.
                Include specific details about lighting, colors, and emotional impact.
                {'' if include_text else 'Do not include any text overlay in the image itself.'}
                """

        # Select the appropriate API based on provider
        if prompt_type == 'advanced_thumbnail':
            selected_provider = provider.lower()
            logger.info(f"Using provider: {selected_provider} for advanced thumbnail generation")

            if selected_provider == 'deepseek' and DEEPSEEK_API_KEY:
                # Call DeepSeek API
                headers = {
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1000
                }
                api_url = DEEPSEEK_API_URL
                logger.info(f"Using DEEPSEEK_API_KEY: {DEEPSEEK_API_KEY[:5]}...{DEEPSEEK_API_KEY[-5:] if len(DEEPSEEK_API_KEY) > 10 else ''}")
            elif selected_provider == 'gemini' and GEMINI_API_KEY:
                # Call Gemini API
                headers = {
                    "Content-Type": "application/json"
                }
                payload = {
                    "contents": [{"parts": [{"text": prompt}]}]
                }
                api_url = f"{GEMINI_API_URL}?key={GEMINI_API_KEY}"
                logger.info(f"Using GEMINI_API_KEY: {GEMINI_API_KEY[:5]}...{GEMINI_API_KEY[-5:] if len(GEMINI_API_KEY) > 10 else ''}")
            else:
                # Default to XAI/Grok API
                headers = {
                    "Authorization": f"Bearer {XAI_API_KEY}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "model": "grok-2-vision-latest",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1000
                }
                api_url = GROK_API_URL
                logger.info(f"Using XAI_API_KEY: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")
        else:
            # For all other prompt types, use XAI/Grok API
            headers = {
                "Authorization": f"Bearer {XAI_API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "grok-2-vision-latest",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 1000
            }
            api_url = GROK_API_URL
            logger.info(f"Using XAI_API_KEY: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")

        # Make the API call
        response = requests.post(api_url, headers=headers, json=payload, timeout=30)

        if response.status_code != 200:
            error_text = response.text
            provider_name = "Grok" if prompt_type != 'advanced_thumbnail' else provider.capitalize()
            logger.error(f"{provider_name} API error: {error_text}")

            # Check if this is an insufficient balance error for DeepSeek or Gemini
            if prompt_type == 'advanced_thumbnail' and provider.lower() in ['deepseek', 'gemini']:
                if 'Insufficient Balance' in error_text or 'quota' in error_text.lower() or 'limit' in error_text.lower():
                    logger.warning(f"{provider_name} API has insufficient balance. Falling back to XAI/Grok API.")

                    # Fall back to XAI/Grok API
                    try:
                        fallback_headers = {
                            "Authorization": f"Bearer {XAI_API_KEY}",
                            "Content-Type": "application/json"
                        }
                        fallback_payload = {
                            "model": "grok-2-vision-latest",
                            "messages": [{"role": "user", "content": prompt}],
                            "max_tokens": 1000
                        }

                        logger.info(f"Falling back to XAI/Grok API with key: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")
                        fallback_response = requests.post(GROK_API_URL, headers=fallback_headers, json=fallback_payload, timeout=30)

                        if fallback_response.status_code == 200:
                            response = fallback_response
                            logger.info("Successfully fell back to XAI/Grok API")
                            # Set a flag to indicate fallback was used
                            # This will be accessed later via locals().get('provider_fallback', False)
                            provider_fallback = True
                        else:
                            fallback_error = fallback_response.text
                            logger.error(f"Fallback to XAI/Grok API also failed: {fallback_error}")
                            return jsonify({'error': f'Failed to generate prompt with {provider_name} (insufficient balance) and fallback to Grok also failed: {fallback_error}'}), 500
                    except Exception as e:
                        logger.error(f"Error in fallback to XAI/Grok API: {str(e)}")
                        return jsonify({'error': f'Failed to generate prompt with {provider_name} (insufficient balance) and fallback to Grok also failed: {str(e)}'}), 500
                else:
                    return jsonify({'error': f'Failed to generate prompt with {provider_name}: {error_text}'}), 500
            else:
                return jsonify({'error': f'Failed to generate prompt with {provider_name}: {error_text}'}), 500

        # Process the response based on provider
        data = response.json()

        if prompt_type == 'advanced_thumbnail' and provider.lower() == 'gemini' and GEMINI_API_KEY:
            # Gemini has a different response format
            try:
                # Try to extract the text from the Gemini response
                candidates = data.get("candidates", [])
                if candidates and len(candidates) > 0:
                    content = candidates[0].get("content", {})
                    parts = content.get("parts", [])
                    if parts and len(parts) > 0:
                        generated_text = parts[0].get("text", "")
                    else:
                        generated_text = ""
                else:
                    # Check for error in the response
                    if "error" in data:
                        error_msg = data.get("error", {}).get("message", "Unknown error")
                        logger.error(f"Gemini API error in response: {error_msg}")
                        generated_text = f"Error: {error_msg}"
                    else:
                        generated_text = ""

                if not generated_text:
                    logger.error(f"Unexpected Gemini response format: {data}")
                    generated_text = "Error: Unable to parse Gemini response"

                # Log the successful response for debugging
                if generated_text and not generated_text.startswith("Error:"):
                    logger.info(f"Successfully parsed Gemini response with {len(generated_text)} characters")
            except Exception as e:
                logger.error(f"Error parsing Gemini response: {str(e)}")
                logger.error(f"Response data: {data}")
                generated_text = "Error: Unable to parse Gemini response"
        else:
            # XAI/Grok and DeepSeek have similar response formats
            try:
                generated_text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                if not generated_text:
                    logger.error(f"Unexpected response format: {data}")
                    generated_text = "Error: Unable to parse response"
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}")
                logger.error(f"Response data: {data}")
                generated_text = "Error: Unable to parse response"

        # Clean the response text
        generated_text = generated_text.strip()
        if generated_text.startswith('```'):
            # Remove markdown code blocks if present
            generated_text = '\n'.join(line for line in generated_text.split('\n')
                                if not line.startswith('```') and not line.endswith('```'))

        # For thumbnail generation or advanced thumbnail, try to parse the JSON response
        if prompt_type in ['thumbnail_generation', 'advanced_thumbnail']:
            try:
                # Try to parse the JSON response
                if generated_text.startswith('{') and generated_text.endswith('}'):
                    parsed_json = json.loads(generated_text)

                    # Extract the image prompt and text overlay
                    image_prompt = parsed_json.get('image_prompt', '')
                    text_overlay = parsed_json.get('text_overlay', '')

                    if not image_prompt:
                        logger.warning("No image_prompt found in the response")
                        image_prompt = generated_text  # Fallback to the full text

                    # Check if we need to include provider fallback information
                    provider_fallback_flag = locals().get('provider_fallback', False)

                    response_data = {
                        'success': True,
                        'generated_prompt': image_prompt,
                        'text_overlay': text_overlay,
                        'prompt_type': prompt_type,
                        'topic': topic
                    }

                    # Add provider fallback information if applicable
                    if provider_fallback_flag:
                        response_data['provider_fallback'] = True
                        response_data['original_provider'] = provider
                        response_data['fallback_provider'] = 'xai'

                    return jsonify(response_data)
                else:
                    logger.warning("Response is not in JSON format")
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                # Continue with the normal response if JSON parsing fails

        # Default response for non-thumbnail or if JSON parsing failed
        return jsonify({
            'success': True,
            'generated_prompt': generated_text,
            'prompt_type': prompt_type,
            'topic': topic
        })

    except Exception as e:
        logger.error(f"Error generating cheatsheet prompt: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Grok API Routes
@grok_bp.route('/test', methods=['GET'])
def test_endpoint():
    """Simple test endpoint to verify the blueprint is working"""
    return jsonify({'message': 'Grok API blueprint is working!'}), 200

@grok_bp.route('/status', methods=['GET'])
def check_status():
    """Check if Grok API is configured and available"""
    logger.info("Status endpoint called")
    if not XAI_API_KEY:
        logger.info("XAI_API_KEY not configured")
        return jsonify({'available': False, 'reason': 'API key not configured'}), 200

    logger.info("XAI_API_KEY is configured, returning available=true")
    return jsonify({'available': True}), 200

@grok_bp.route('/generate-prompt', methods=['POST'])
def generate_prompt():
    """Generate content prompt using Grok AI"""
    logger.info("Received request to generate prompt with Grok")

    if not XAI_API_KEY:
        logger.error("XAI_API_KEY not configured")
        return jsonify({'error': 'Grok API key not configured'}), 500

    logger.info(f"Using XAI_API_KEY: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")

    # Check if this is a cheatsheet prompt request
    data = request.json
    prompt_type = data.get('prompt_type')

    # Log the request data for debugging
    logger.info(f"Received data for prompt generation: {data}")

    if prompt_type in ['content_creation', 'image_generation', 'thumbnail_generation', 'claude_prompt', 'advanced_thumbnail']:
        return generate_cheatsheet_prompt(data)

    # Continue with the original prompt generation logic

    try:
        data = request.json
        logger.info(f"Received data: {data}")

        title = data.get('title', '')
        category = data.get('category', '')
        description = data.get('description', '')

        logger.info(f"Processing request - Title: {title}, Category: {category}")

        if not title:
            logger.warning("Title is required but not provided")
            return jsonify({'error': 'Title is required'}), 400

        # Create prompt for Grok - using a structured JSON format
        # Check if user provided specific instructions in the description
        if description.strip():
            # If user provided specific instructions, use them to guide the content generation
            prompt = f"""
            Return only a JSON object.

            INSTRUCTION:
            Create a [type of content: TikTok script, YouTube video outline, blog post, Instagram carousel, email newsletter, etc.] in the {category} niche.

            TITLE: {title}

            TONE: [Pick one: Friendly, Empathetic, Funny, Sarcastic, Motivational, Professional, etc.]

            GOAL: [What you want the content to do — Educate? Entertain? Inspire? Solve a pain point? Promote something?]

            TARGET AUDIENCE: [Who is this for? Be specific — age, mindset, challenge, lifestyle, relationship status, etc.]

            FORMAT:

            Hook (attention-grabber: a question, stat, relatable line)

            Key Points / Main Ideas (tips, advice, steps, quotes, etc.)

            Closing Advice or CTA (motivate, guide next steps, or invite action)

            Additional context: {description}

            Return the response in this exact JSON format, and include the selected tone, goal, target audience, and format in the response:
            {{
              "response": {{
                "Content": "A comprehensive content prompt based on the user's specific instructions (5-10 sentences)",
                "Call To Action": "A compelling call to action that specifically asks viewers to like the video, subscribe to the channel, and engage with the content. Make it persuasive and relevant to the topic.",
                "Thumbnail Image Prompt": "A detailed description for a thumbnail image that would work well for this content",
                "Tone": "The selected tone for this content",
                "Goal": "The selected goal for this content",
                "Target Audience": "The selected target audience for this content",
                "Format": "The selected format for this content"
              }}
            }}
            """
        else:
            # If no specific instructions, generate general content without the specific format
            prompt = f"""
            Return only a JSON object.

            INSTRUCTION:
            Create a [type of content: TikTok script, YouTube video outline, blog post, Instagram carousel, email newsletter, etc.] in the {category} niche.

            TITLE: {title}

            TONE: [Pick one: Friendly, Empathetic, Funny, Sarcastic, Motivational, Professional, etc.]

            GOAL: [What you want the content to do — Educate? Entertain? Inspire? Solve a pain point? Promote something?]

            TARGET AUDIENCE: [Who is this for? Be specific — age, mindset, challenge, lifestyle, relationship status, etc.]

            FORMAT:

            Hook (attention-grabber: a question, stat, relatable line)

            Key Points / Main Ideas (tips, advice, steps, quotes, etc.)

            Closing Advice or CTA (motivate, guide next steps, or invite action)

            Return the response in this exact JSON format, and include the selected tone, goal, target audience, and format in the response:
            {{
              "response": {{
                "Content": "A comprehensive content prompt that covers the main points for a vlog about this topic (5-10 sentences)",
                "Call To Action": "A compelling call to action that specifically asks viewers to like the video, subscribe to the channel, and engage with the content. Make it persuasive and relevant to the topic.",
                "Thumbnail Image Prompt": "A detailed description for a thumbnail image that would work well for this content",
                "Tone": "The selected tone for this content",
                "Goal": "The selected goal for this content",
                "Target Audience": "The selected target audience for this content",
                "Format": "The selected format for this content"
              }}
            }}
            """

        # Call Grok API
        logger.info(f"Calling Grok API at {GROK_API_URL}")
        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "grok-2-vision-latest",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 1000
        }

        logger.info(f"Payload structure prepared (not showing full content)")

        try:
            logger.info("Sending request to Grok API...")
            response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=30)
            logger.info(f"Received response with status code: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Grok API error: {error_text}")
                return jsonify({'error': f'Failed to generate content with Grok: {error_text}'}), 500
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception: {str(e)}")
            return jsonify({'error': f'Network error when calling Grok API: {str(e)}'}), 500

        # Process the response
        try:
            data = response.json()
            generated_text = data["choices"][0]["message"]["content"]
            logger.info(f"Raw response received, length: {len(generated_text)} characters")

            # Clean the response text
            generated_text = generated_text.strip()
            if generated_text.startswith('```json'):
                generated_text = generated_text[7:]  # Remove ```json prefix
            elif generated_text.startswith('```'):
                generated_text = generated_text[3:]  # Remove ``` prefix

            if generated_text.endswith('```'):
                generated_text = generated_text[:-3]  # Remove ``` suffix

            # Parse the JSON response
            try:
                response_json = json.loads(generated_text)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON. Raw text: {generated_text[:100]}...")
                logger.error(f"JSON parse error: {str(e)}")
                return jsonify({'error': f'Invalid JSON response from Grok API: {str(e)}'}), 500

            # Validate response structure
            if "response" not in response_json:
                logger.error("Missing 'response' key in JSON")
                return jsonify({'error': 'Invalid response format from Grok API'}), 500

            # Extract the content
            response_data = response_json["response"]
            content = response_data.get("Content") or response_data.get("content", "")
            call_to_action = response_data.get("Call To Action") or response_data.get("call_to_action", "")
            thumbnail_prompt = response_data.get("Thumbnail Image Prompt") or response_data.get("thumbnail_prompt", "")

            # Use the content directly without specific formatting
            content_prompt = content

            logger.info("Successfully extracted content from Grok response")

            return jsonify({
                'content_prompt': content_prompt,
                'call_to_action': call_to_action,  # Using the specific call to action
                'title': title,
                'thumbnail_prompt': thumbnail_prompt
            })

        except Exception as e:
            logger.error(f"Error processing Grok response: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error processing Grok response: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"Unexpected error in generate_prompt: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

# Content API Routes
@content_bp.route('/content-prompts/topics', methods=['GET'])
def get_content_prompt_topics():
    conn = None
    cursor = None
    try:
        logger.info("Fetching content prompt topics")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if content_prompt_claude table exists
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')

        if content_prompt_claude_exists:
            logger.info("Using content_prompt_claude table for topics")
            cursor.execute('''
                SELECT
                    id,
                    topic
                FROM content_prompt_claude
                ORDER BY topic ASC
            ''')

            results = cursor.fetchall()
            return jsonify(results)
        else:
            logger.warning("content_prompt_claude table does not exist")
            return jsonify([])  # Return empty array

    except Exception as e:
        logger.error(f"Error fetching content prompt topics: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/content-prompts', methods=['GET'])
def get_content_prompts():
    conn = None
    cursor = None
    try:
        logger.info("Fetching content prompts")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')
        category_exists = check_table_exists(cursor, 'category')

        # Prioritize the new Claude format table if it exists
        if content_prompt_claude_exists:
            logger.info("Using content_prompt_claude table")
            if category_exists:
                cursor.execute('''
                    SELECT
                        cp.id,
                        cp.topic as title,
                        cp.content_type,
                        cp.purpose,
                        cp.audience,
                        cp.tone,
                        cp.style,
                        cp.word_count,
                        cp.persona,
                        cp.content_prompt,
                        cp.call_to_action,
                        cp.output_format,
                        cp.generated_at
                    FROM content_prompt_claude cp
                    ORDER BY cp.generated_at DESC
                ''')
            else:
                cursor.execute('''
                    SELECT
                        id,
                        topic as title,
                        content_type,
                        purpose,
                        audience,
                        tone,
                        style,
                        word_count,
                        persona,
                        content_prompt,
                        call_to_action,
                        output_format,
                        generated_at
                    FROM content_prompt_claude
                    ORDER BY generated_at DESC
                ''')
        elif content_prompt_exists:
            # Fall back to the original content_prompt table
            logger.info("Falling back to content_prompt table")
            if category_exists:
                # Both tables exist, use the original query
                cursor.execute('''
                    SELECT
                        cp.id,
                        cp.title,
                        cp.category_id,
                        cp.content_prompt,
                        cp.call_to_action,
                        cp.output_format,
                        cp.voice_id,
                        cp.video_format,
                        cp.tone,
                        cp.goal,
                        cp.target_audience,
                        cp.format_type,
                        cp.multiple_topics,
                        cp.num_sentences,
                        cp.generated_at,
                        c.name as category_name,
                        c.description as category_description
                    FROM content_prompt cp
                    LEFT JOIN category c ON cp.category_id = c.id
                    ORDER BY cp.generated_at DESC
                ''')
            else:
                # Only content_prompt exists, don't join with category
                logger.warning("category table does not exist, querying without join")
                cursor.execute('''
                    SELECT
                        id,
                        title,
                        category_id,
                        content_prompt,
                        call_to_action,
                        output_format,
                        voice_id,
                        video_format,
                        tone,
                        goal,
                        target_audience,
                        format_type,
                        multiple_topics,
                        num_sentences,
                        generated_at,
                        NULL as category_name,
                        NULL as category_description
                    FROM content_prompt
                    ORDER BY generated_at DESC
                ''')
        else:
            logger.warning("Neither content_prompt nor content_prompt_claude table exists")
            return jsonify([])  # Return empty array instead of error

        results = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for item in results:
            if item.get('generated_at') and isinstance(item['generated_at'], (datetime.date, datetime.datetime)):
                item['generated_at'] = item['generated_at'].isoformat()

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error fetching content prompts: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/content-prompts/<int:prompt_id>', methods=['GET'])
def get_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Fetching content prompt with ID: {prompt_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')
        category_exists = check_table_exists(cursor, 'category')

        # Prioritize the new Claude format table if it exists
        if content_prompt_claude_exists:
            logger.info(f"Checking content_prompt_claude table for ID: {prompt_id}")
            if category_exists:
                cursor.execute('''
                    SELECT
                        cp.id,
                        cp.topic as title,
                        cp.content_type,
                        cp.purpose,
                        cp.audience,
                        cp.tone,
                        cp.style,
                        cp.word_count,
                        cp.persona,
                        cp.content_prompt,
                        cp.call_to_action,
                        cp.output_format,
                        cp.generated_at
                    FROM content_prompt_claude cp
                    WHERE cp.id = %s
                ''', (prompt_id,))
            else:
                cursor.execute('''
                    SELECT
                        id,
                        topic as title,
                        content_type,
                        purpose,
                        audience,
                        tone,
                        style,
                        word_count,
                        persona,
                        content_prompt,
                        call_to_action,
                        output_format,
                        generated_at
                    FROM content_prompt_claude
                    WHERE id = %s
                ''', (prompt_id,))

            result = cursor.fetchone()

            # If not found in claude table, try the original table
            if not result and content_prompt_exists:
                logger.info(f"Not found in claude table, checking content_prompt table for ID: {prompt_id}")
                if category_exists:
                    cursor.execute('''
                        SELECT
                            cp.id,
                            cp.title,
                            cp.category_id,
                            cp.content_prompt,
                            cp.call_to_action,
                            cp.output_format,
                            cp.voice_id,
                            cp.video_format,
                            cp.tone,
                            cp.goal,
                            cp.target_audience,
                            cp.format_type,
                            cp.multiple_topics,
                            cp.num_sentences,
                            cp.generated_at,
                            c.name as category_name,
                            c.description as category_description
                        FROM content_prompt cp
                        LEFT JOIN category c ON cp.category_id = c.id
                        WHERE cp.id = %s
                    ''', (prompt_id,))
                else:
                    cursor.execute('''
                        SELECT
                            id,
                            title,
                            category_id,
                            content_prompt,
                            call_to_action,
                            output_format,
                            voice_id,
                            video_format,
                            tone,
                            goal,
                            target_audience,
                            format_type,
                            multiple_topics,
                            num_sentences,
                            generated_at,
                            NULL as category_name,
                            NULL as category_description
                        FROM content_prompt
                        WHERE id = %s
                    ''', (prompt_id,))
                result = cursor.fetchone()
        elif content_prompt_exists:
            # Fall back to the original content_prompt table
            logger.info(f"Checking content_prompt table for ID: {prompt_id}")
            if category_exists:
                cursor.execute('''
                    SELECT
                        cp.id,
                        cp.title,
                        cp.category_id,
                        cp.content_prompt,
                        cp.call_to_action,
                        cp.output_format,
                        cp.voice_id,
                        cp.video_format,
                        cp.tone,
                        cp.goal,
                        cp.target_audience,
                        cp.format_type,
                        cp.multiple_topics,
                        cp.num_sentences,
                        cp.generated_at,
                        c.name as category_name,
                        c.description as category_description
                    FROM content_prompt cp
                    LEFT JOIN category c ON cp.category_id = c.id
                    WHERE cp.id = %s
                ''', (prompt_id,))
            else:
                cursor.execute('''
                    SELECT
                        id,
                        title,
                        category_id,
                        content_prompt,
                        call_to_action,
                        output_format,
                        voice_id,
                        video_format,
                        tone,
                        goal,
                        target_audience,
                        format_type,
                        multiple_topics,
                        num_sentences,
                        generated_at,
                        NULL as category_name,
                        NULL as category_description
                    FROM content_prompt
                    WHERE id = %s
                ''', (prompt_id,))
            result = cursor.fetchone()
        else:
            logger.warning("Neither content_prompt nor content_prompt_claude table exists")
            return jsonify({'error': 'Content prompt tables do not exist'}), 404

        if not result:
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

        # Convert datetime objects to strings for JSON serialization
        if result.get('generated_at') and isinstance(result['generated_at'], (datetime.date, datetime.datetime)):
            result['generated_at'] = result['generated_at'].isoformat()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error fetching content prompt: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/content-prompts', methods=['POST'])
def create_content_prompt():
    conn = None
    cursor = None
    try:
        # Get the raw request body
        raw_body = request.get_data(as_text=True)
        logger.info(f"Raw request body: {raw_body}")

        # Parse the JSON data
        data = request.json
        logger.info(f"Received data for content prompt creation: {data}")

        # Log the content_prompt field separately with full details
        if 'content_prompt' in data and data['content_prompt']:
            logger.info(f"Full content_prompt: {data['content_prompt'][:500]}...")
            if len(data['content_prompt']) > 500:
                logger.info(f"...continued: {data['content_prompt'][500:1000]}...")
                if len(data['content_prompt']) > 1000:
                    logger.info(f"...continued: {data['content_prompt'][1000:1500]}...")
                    if len(data['content_prompt']) > 1500:
                        logger.info(f"...continued: {data['content_prompt'][1500:]}")
        else:
            logger.info("No content_prompt field found in request data or it is empty.")

        # We no longer need category_id
        # Get the topic from the request data
        topic = data.get('topic', data.get('title', ''))
        if not topic:
            error_msg = "Topic is required"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if the content_prompt_claude table exists
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')

        if not content_prompt_claude_exists:
            # Create the content_prompt_claude table if it doesn't exist
            logger.info("content_prompt_claude table does not exist. Creating it...")
            cursor.execute("""
                CREATE TABLE content_prompt_claude (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    topic VARCHAR(255) NOT NULL,
                    content_type VARCHAR(50) DEFAULT 'blog_post',
                    purpose VARCHAR(50) DEFAULT 'inform',
                    audience VARCHAR(100) DEFAULT 'general',
                    tone VARCHAR(50) DEFAULT 'conversational',
                    style VARCHAR(50) DEFAULT 'descriptive',
                    word_count VARCHAR(50) DEFAULT '500',
                    persona VARCHAR(50) DEFAULT 'expert',
                    content_prompt TEXT,
                    call_to_action TEXT,
                    output_format VARCHAR(50) DEFAULT 'standard',
                    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
            logger.info("content_prompt_claude table created successfully!")
            content_prompt_claude_exists = True

        # Validate required fields
        required_fields = ['topic', 'content_type', 'purpose', 'audience', 'tone', 'style', 'word_count', 'persona']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            error_msg = f"Missing required fields: {', '.join(missing_fields)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Check if a prompt with the same topic already exists
        try:
            logger.info("Checking if a prompt with the same topic already exists")
            logger.info(f"Topic: {topic}")
            cursor.execute("SELECT id FROM content_prompt_claude WHERE topic = %s", (topic,))
            existing_prompt = cursor.fetchone()

            # Log request data
            logger.info(f"Topic: {data.get('topic')}")
            logger.info(f"Content Type: {data.get('content_type')}")
            logger.info(f"Purpose: {data.get('purpose')}")
            logger.info(f"Audience: {data.get('audience')}")
            logger.info(f"Tone: {data.get('tone')}")
            logger.info(f"Style: {data.get('style')}")
            logger.info(f"Word Count: {data.get('word_count')}")
            logger.info(f"Persona: {data.get('persona')}")
            logger.info(f"Content prompt data: {data.get('content_prompt', 'EMPTY')}")
            logger.info(f"Call to action data: {data.get('call_to_action', 'EMPTY')}")
            logger.info(f"Output Format: {data.get('output_format')}")
            logger.info(f"All data: {data}")

            if existing_prompt:
                # Update existing prompt
                prompt_id = existing_prompt['id']
                logger.info(f"Found existing prompt with ID {prompt_id}. Updating it.")
                cursor.execute(
                '''
                UPDATE content_prompt_claude
                SET
                    content_type = %s,
                    purpose = %s,
                    audience = %s,
                    tone = %s,
                    style = %s,
                    word_count = %s,
                    persona = %s,
                    content_prompt = %s,
                    call_to_action = %s,
                    output_format = %s,
                    generated_at = CURRENT_TIMESTAMP
                WHERE id = %s
                ''',
                (
                    data.get('content_type'),
                    data.get('purpose'),
                    data.get('audience'),
                    data.get('tone'),
                    data.get('style'),
                    data.get('word_count'),
                    data.get('persona'),
                    data.get('content_prompt'),
                    data.get('call_to_action'),
                    data.get('output_format'),
                    prompt_id
                )
                )
                conn.commit()
                logger.info(f"Successfully updated content prompt with ID: {prompt_id}")
                return jsonify({'success': True, 'id': prompt_id, 'table': 'content_prompt_claude', 'action': 'updated'})
            else:
                # Insert new prompt
                logger.info("No existing prompt found. Creating a new one.")
                cursor.execute(
                '''
                INSERT INTO content_prompt_claude
                    (topic, content_type, purpose, audience,
                    tone, style, word_count, persona, content_prompt, call_to_action, output_format)
                VALUES
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''',
                (
                    topic,  # Use the topic we extracted earlier
                    data.get('content_type'),
                    data.get('purpose'),
                    data.get('audience'),
                    data.get('tone'),
                    data.get('style'),
                    data.get('word_count'),
                    data.get('persona'),
                    data.get('content_prompt'),
                    data.get('call_to_action'),
                    data.get('output_format')
                )
                )
                conn.commit()
                new_id = cursor.lastrowid
                logger.info(f"Successfully created content prompt in Claude format with ID: {new_id}")
                return jsonify({'success': True, 'id': new_id, 'table': 'content_prompt_claude', 'action': 'created'})
        except Exception as e:
            logger.error(f"Error inserting into content_prompt_claude: {str(e)}")
            conn.rollback()
            return jsonify({'error': f"Failed to insert data into content_prompt_claude table: {str(e)}"}), 500
    except Exception as e:
        error_msg = f"Error in create_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/content-prompts/<int:prompt_id>', methods=['PUT'])
def update_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Updating content prompt with ID {prompt_id}: {data}")

        # Convert category_id to integer
        try:
            category_id = int(data['category_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid category_id: {data.get('category_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')

        # First check if the prompt exists in the Claude table
        if content_prompt_claude_exists:
            cursor.execute("SELECT id FROM content_prompt_claude WHERE id = %s", (prompt_id,))
            claude_prompt = cursor.fetchone()

            if claude_prompt:
                # Validate required fields
                required_fields = ['topic', 'content_type', 'purpose', 'audience', 'tone', 'style', 'word_count', 'persona']
                missing_fields = [field for field in required_fields if not data.get(field)]

                if missing_fields:
                    error_msg = f"Missing required fields: {', '.join(missing_fields)}"
                    logger.error(error_msg)
                    return jsonify({'error': error_msg}), 400

                try:
                    logger.info("Executing query to update content prompt in Claude format table")
                    logger.info(f"Topic: {data.get('topic')}")
                    logger.info(f"Content Type: {data.get('content_type')}")
                    logger.info(f"Purpose: {data.get('purpose')}")
                    logger.info(f"Audience: {data.get('audience')}")
                    logger.info(f"Tone: {data.get('tone')}")
                    logger.info(f"Style: {data.get('style')}")
                    logger.info(f"Word Count: {data.get('word_count')}")
                    logger.info(f"Persona: {data.get('persona')}")
                    logger.info(f"Content prompt data: {data.get('content_prompt', 'EMPTY')}")
                    logger.info(f"Call to action data: {data.get('call_to_action', 'EMPTY')}")
                    logger.info(f"Output Format: {data.get('output_format')}")
                    logger.info(f"All data: {data}")
                    cursor.execute(
                        '''
                        UPDATE content_prompt_claude
                        SET
                            topic = %s,
                            content_type = %s,
                            purpose = %s,
                            audience = %s,
                            tone = %s,
                            style = %s,
                            word_count = %s,
                            persona = %s,
                            content_prompt = %s,
                            call_to_action = %s,
                            output_format = %s
                        WHERE id = %s
                        ''',
                        (
                            data.get('topic', data.get('title', '')),  # Use topic or fall back to title
                            data.get('content_type'),
                            data.get('purpose'),
                            data.get('audience'),
                            data.get('tone'),
                            data.get('style'),
                            data.get('word_count'),
                            data.get('persona'),
                            data.get('content_prompt'),
                            data.get('call_to_action'),
                            data.get('output_format'),
                            prompt_id
                        )
                    )
                    conn.commit()

                    if cursor.rowcount == 0:
                        return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

                    logger.info(f"Successfully updated content prompt in Claude format with ID: {prompt_id}")
                    return jsonify({'success': True, 'id': prompt_id, 'table': 'content_prompt_claude'})
                except Exception as e:
                    conn.rollback()
                    error_msg = f"Error updating content prompt in Claude format: {str(e)}"
                    logger.error(error_msg)
                    # If update in Claude table fails, don't try the original table
                    return jsonify({'error': error_msg}), 500

        # If not found in Claude table or Claude table doesn't exist, try the original table
        if content_prompt_exists:
            cursor.execute("SELECT id FROM content_prompt WHERE id = %s", (prompt_id,))
            if not cursor.fetchone():
                return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

            # Ensure num_sentences is an integer
            try:
                num_sentences = int(data.get('num_sentences', 5))
            except (ValueError, TypeError):
                error_msg = f"Invalid num_sentences: {data.get('num_sentences', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

            # Convert multiple_topics to integer (0 or 1) for MySQL
            multiple_topics = 1 if data.get('multiple_topics') else 0

            try:
                logger.info("Executing query to update content prompt in original table")
                cursor.execute(
                    '''
                    UPDATE content_prompt
                    SET
                        title = %s,
                        category_id = %s,
                        content_prompt = %s,
                        call_to_action = %s,
                        output_format = %s,
                        voice_id = %s,
                        video_format = %s,
                        tone = %s,
                        goal = %s,
                        target_audience = %s,
                        format_type = %s,
                        multiple_topics = %s,
                        num_sentences = %s
                    WHERE id = %s
                    ''',
                    (
                        data.get('title', data.get('topic', '')),  # Use title or fall back to topic
                        category_id,
                        data.get('content_prompt', ''),
                        data.get('call_to_action', ''),
                        data.get('output_format', 'standard'),
                        data.get('voice_id', ''),
                        data.get('video_format', '1min'),
                        data.get('tone', ''),
                        data.get('goal', ''),
                        data.get('target_audience', ''),
                        data.get('format_type', ''),
                        multiple_topics,
                        num_sentences,
                        prompt_id
                    )
                )
                conn.commit()

                if cursor.rowcount == 0:
                    return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

                logger.info(f"Successfully updated content prompt with ID: {prompt_id}")
                return jsonify({'success': True, 'id': prompt_id, 'table': 'content_prompt'})
            except Exception as e:
                conn.rollback()
                error_msg = f"Error updating content prompt: {str(e)}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in update_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/content-prompts/<int:prompt_id>', methods=['DELETE'])
def delete_content_prompt(prompt_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Deleting content prompt with ID: {prompt_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        content_prompt_exists = check_table_exists(cursor, 'content_prompt')
        content_prompt_claude_exists = check_table_exists(cursor, 'content_prompt_claude')

        deleted = False

        # First try to delete from the Claude table if it exists
        if content_prompt_claude_exists:
            cursor.execute("SELECT id FROM content_prompt_claude WHERE id = %s", (prompt_id,))
            if cursor.fetchone():
                try:
                    cursor.execute("DELETE FROM content_prompt_claude WHERE id = %s", (prompt_id,))
                    conn.commit()

                    if cursor.rowcount > 0:
                        logger.info(f"Successfully deleted content prompt from Claude table with ID: {prompt_id}")
                        deleted = True
                except Exception as e:
                    conn.rollback()
                    error_msg = f"Error deleting from content_prompt_claude: {str(e)}"
                    logger.error(error_msg)
                    # Continue to try deleting from the original table

        # Then try to delete from the original table if it exists and we haven't deleted from Claude table
        if content_prompt_exists and not deleted:
            cursor.execute("SELECT id FROM content_prompt WHERE id = %s", (prompt_id,))
            if not cursor.fetchone():
                if not deleted:  # Only return error if we haven't deleted from Claude table
                    return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404
            else:
                try:
                    cursor.execute("DELETE FROM content_prompt WHERE id = %s", (prompt_id,))
                    conn.commit()

                    if cursor.rowcount > 0:
                        logger.info(f"Successfully deleted content prompt from original table with ID: {prompt_id}")
                        deleted = True
                except Exception as e:
                    conn.rollback()
                    error_msg = f"Error deleting from content_prompt: {str(e)}"
                    logger.error(error_msg)
                    if not deleted:  # Only return error if we haven't deleted from Claude table
                        return jsonify({'error': error_msg}), 500

        if deleted:
            return jsonify({'success': True})
        else:
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404
    except Exception as e:
        error_msg = f"Error in delete_content_prompt: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/categories/<int:category_id>/status', methods=['PUT'])
def update_category_status(category_id):
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Updating status for category ID {category_id}: {data}")

        if not data or 'status' not in data:
            return jsonify({'error': 'Status is required'}), 400

        status = data['status']
        if status not in ['active', 'inactive']:
            return jsonify({'error': 'Status must be either "active" or "inactive"'}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if category exists
        cursor.execute("SELECT id FROM category WHERE id = %s", (category_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Category with ID {category_id} not found'}), 404

        # Update category status
        cursor.execute(
            "UPDATE category SET status = %s WHERE id = %s",
            (status, category_id)
        )
        conn.commit()

        logger.info(f"Successfully updated status for category ID {category_id} to {status}")
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        error_msg = f"Error updating category status: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/categories', methods=['GET'])
def get_categories():
    conn = None
    cursor = None
    try:
        logger.info("Fetching categories")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Get status filter from query parameters (default to active only)
        status_filter = request.args.get('status', 'active')

        if status_filter == 'all':
            # Fetch all categories regardless of status
            cursor.execute('''
                SELECT id, name, description, status
                FROM category
                ORDER BY name ASC
            ''')
        else:
            # Fetch only active categories by default
            cursor.execute('''
                SELECT id, name, description, status
                FROM category
                WHERE status = 'active'
                ORDER BY name ASC
            ''')

        categories = cursor.fetchall()
        logger.info(f"Successfully fetched {len(categories)} categories with status filter: {status_filter}")

        # Convert datetime objects to strings to ensure JSON serialization works
        for item in categories:
            for key, value in item.items():
                if isinstance(value, (datetime.date, datetime.datetime)):
                    item[key] = value.isoformat()

        return jsonify(categories)
    except Exception as e:
        error_msg = f"Error fetching categories: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Root endpoint
@app.route('/')
def index():
    return jsonify({
        'message': 'Vlog Generation API Server',
        'endpoints': {
            'content_prompts': '/api/content-prompts',
            'generated_content': '/api/generated-content',
            'categories': '/api/categories',
            'category_status': '/api/categories/<category_id>/status',
            'category_titles': '/api/category-titles/<category_id>',
            'generate_titles': '/api/generate-titles/<category_id>',
            'grok': '/api/grok/generate-prompt',
            'cheatsheet_prompts': '/api/grok/generate-prompt (with prompt_type=content_creation, image_generation, or thumbnail_generation)',
            'claude_prompts': '/api/grok/generate-prompt (with prompt_type=claude_prompt)'
        }
    })

# Category title endpoints
@content_bp.route('/category-titles/<int:category_id>', methods=['GET'])
def get_category_titles(category_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Fetching titles for category ID: {category_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if category exists
        cursor.execute("SELECT id FROM category WHERE id = %s", (category_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Category with ID {category_id} not found'}), 404

        # Get titles for the category
        cursor.execute("""
            SELECT id, title, is_used
            FROM category_title
            WHERE category_id = %s
            ORDER BY is_used ASC, title ASC
        """, (category_id,))

        titles = cursor.fetchall()
        logger.info(f"Successfully fetched {len(titles)} titles for category ID: {category_id}")

        return jsonify(titles)
    except Exception as e:
        error_msg = f"Error fetching category titles: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/category-titles', methods=['POST'])
def create_category_title():
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Received data for category title creation: {data}")

        # Validate required fields
        if not data.get('category_id'):
            return jsonify({'error': 'Category ID is required'}), 400
        if not data.get('title'):
            return jsonify({'error': 'Title is required'}), 400

        # Convert category_id to integer
        try:
            category_id = int(data['category_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid category_id: {data.get('category_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if category exists
        cursor.execute("SELECT id FROM category WHERE id = %s", (category_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Category with ID {category_id} not found'}), 404

        # Check if title already exists for this category
        cursor.execute(
            "SELECT id FROM category_title WHERE category_id = %s AND title = %s",
            (category_id, data['title'])
        )
        if cursor.fetchone():
            return jsonify({'error': f'Title already exists for this category'}), 409

        # Insert new title
        try:
            cursor.execute(
                "INSERT INTO category_title (category_id, title) VALUES (%s, %s)",
                (category_id, data['title'])
            )
            conn.commit()
            new_id = cursor.lastrowid
            logger.info(f"Successfully created category title with ID: {new_id}")
            return jsonify({'success': True, 'id': new_id})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error creating category title: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in create_category_title: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generate-titles/<int:category_id>', methods=['POST'])
def generate_titles_for_category(category_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Generating titles for category ID: {category_id}")

        # Get category name
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        cursor.execute("SELECT name FROM category WHERE id = %s", (category_id,))
        category = cursor.fetchone()
        if not category:
            return jsonify({'error': f'Category with ID {category_id} not found'}), 404

        category_name = category['name']

        # Generate titles using Grok
        if not XAI_API_KEY:
            return jsonify({'error': 'Grok API key not configured'}), 500

        # Create prompt for Grok
        prompt = f"""
        Generate 10 engaging and specific title ideas for vlogs about {category_name}.
        Return only a JSON array of strings, with each string being a title.
        The titles should be specific, not generic, and should appeal to viewers.
        For example, instead of "Health Tips", use "5 Morning Habits That Boost Your Metabolism".

        Return the response in this exact JSON format:
        {{"titles": ["Title 1", "Title 2", "Title 3", ... ]}}
        """

        # Call Grok API
        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "grok-2-vision-latest",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 1000
        }

        try:
            response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=30)

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Grok API error: {error_text}")
                return jsonify({'error': f'Failed to generate titles with Grok: {error_text}'}), 500

            # Process the response
            data = response.json()
            generated_text = data["choices"][0]["message"]["content"]

            # Clean the response text
            generated_text = generated_text.strip()
            if generated_text.startswith('```json'):
                generated_text = generated_text[7:]  # Remove ```json prefix
            elif generated_text.startswith('```'):
                generated_text = generated_text[3:]  # Remove ``` prefix

            if generated_text.endswith('```'):
                generated_text = generated_text[:-3]  # Remove ``` suffix

            # Parse the JSON response
            try:
                response_json = json.loads(generated_text)
                titles = response_json.get("titles", [])

                if not titles:
                    return jsonify({'error': 'No titles generated'}), 500

                # Save titles to database
                saved_titles = []
                for title in titles:
                    # Check if title already exists
                    cursor.execute(
                        "SELECT id FROM category_title WHERE category_id = %s AND title = %s",
                        (category_id, title)
                    )
                    if not cursor.fetchone():
                        cursor.execute(
                            "INSERT INTO category_title (category_id, title) VALUES (%s, %s)",
                            (category_id, title)
                        )
                        saved_titles.append({
                            'id': cursor.lastrowid,
                            'title': title,
                            'is_used': False
                        })

                conn.commit()
                logger.info(f"Successfully generated and saved {len(saved_titles)} titles for category ID: {category_id}")

                return jsonify({
                    'success': True,
                    'titles': saved_titles,
                    'category': category_name
                })
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON. Raw text: {generated_text[:100]}...")
                logger.error(f"JSON parse error: {str(e)}")
                return jsonify({'error': f'Invalid JSON response from Grok API: {str(e)}'}), 500
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception: {str(e)}")
            return jsonify({'error': f'Network error when calling Grok API: {str(e)}'}), 500
    except Exception as e:
        error_msg = f"Error generating titles: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/category-titles/<int:title_id>/mark-used', methods=['PUT'])
def mark_title_as_used(title_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Marking title ID {title_id} as used")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if title exists
        cursor.execute("SELECT id FROM category_title WHERE id = %s", (title_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Title with ID {title_id} not found'}), 404

        # Mark title as used
        cursor.execute("UPDATE category_title SET is_used = TRUE WHERE id = %s", (title_id,))
        conn.commit()

        logger.info(f"Successfully marked title ID {title_id} as used")
        return jsonify({'success': True})
    except Exception as e:
        error_msg = f"Error marking title as used: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Generated Content API Routes
@content_bp.route('/generated-content', methods=['GET'])
def get_generated_contents():
    conn = None
    cursor = None
    try:
        logger.info("Fetching generated content")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        cursor.execute('''
            SELECT
                id,
                content_prompt_id,
                title,
                content_text,
                thumbnail_prompt,
                speech_duration,
                speech_location,
                num_images,
                generated_at
            FROM generated_content
            ORDER BY generated_at DESC
        ''')

        results = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for item in results:
            if item.get('generated_at') and isinstance(item['generated_at'], (datetime.date, datetime.datetime)):
                item['generated_at'] = item['generated_at'].isoformat()

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error fetching generated content: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generated-content/<int:content_id>', methods=['GET'])
def get_generated_content(content_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Fetching generated content with ID: {content_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        cursor.execute('''
            SELECT
                gc.id,
                gc.content_prompt_id,
                gc.title,
                gc.content_text,
                gc.thumbnail_prompt,
                gc.speech_duration,
                gc.speech_location,
                gc.num_images,
                gc.generated_at,
                cp.title as prompt_title,
                c.name as category_name
            FROM generated_content gc
            LEFT JOIN content_prompt cp ON gc.content_prompt_id = cp.id
            LEFT JOIN category c ON cp.category_id = c.id
            WHERE gc.id = %s
        ''', (content_id,))

        result = cursor.fetchone()

        if not result:
            return jsonify({'error': f'Generated content with ID {content_id} not found'}), 404

        # Convert datetime objects to strings for JSON serialization
        if result.get('generated_at') and isinstance(result['generated_at'], (datetime.date, datetime.datetime)):
            result['generated_at'] = result['generated_at'].isoformat()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error fetching generated content: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generated-content/check-title', methods=['POST'])
def check_title_exists():
    """Check if a title already exists in the generated_content table"""
    conn = None
    cursor = None
    try:
        data = request.json
        title = data.get('title', '')

        if not title:
            return jsonify({'error': 'Title is required'}), 400

        logger.info(f"Checking if title exists: {title}")

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if the title exists (case insensitive)
        cursor.execute("""
            SELECT id, title FROM generated_content
            WHERE LOWER(title) = LOWER(%s)
        """, (title,))

        result = cursor.fetchone()

        if result:
            logger.info(f"Title already exists with ID: {result['id']}")
            return jsonify({
                'exists': True,
                'id': result['id'],
                'title': result['title']
            })
        else:
            logger.info("Title does not exist")
            return jsonify({'exists': False})

    except Exception as e:
        error_msg = f"Error checking title existence: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generated-content', methods=['POST'])
def create_generated_content():
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Received data for generated content creation: {data}")
        logger.info(f"Content text length: {len(data.get('content_text', ''))}")
        logger.info(f"Title: {data.get('title', '')}")
        logger.info(f"Content prompt ID: {data.get('content_prompt_id', '')}")
        logger.info(f"Request headers: {request.headers}")

        # Truncate title if it's too long
        title = data.get('title', '')
        if len(title) > 250:
            logger.warning(f"Title too long ({len(title)} chars), truncating to 250 chars")
            title = title[:247] + '...'
            data['title'] = title

        # Validate that content_prompt_id is provided and valid
        if not data.get('content_prompt_id'):
            error_msg = "Content prompt ID is required"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Convert content_prompt_id to integer
        try:
            content_prompt_id = int(data['content_prompt_id'])
        except (ValueError, TypeError):
            error_msg = f"Invalid content_prompt_id: {data.get('content_prompt_id', 'None')}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Convert numeric fields to integers if they exist
        speech_duration = None
        if data.get('speech_duration'):
            try:
                speech_duration = int(data['speech_duration'])
            except (ValueError, TypeError):
                error_msg = f"Invalid speech_duration: {data.get('speech_duration', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

        num_images = None
        if data.get('num_images'):
            try:
                num_images = int(data['num_images'])
            except (ValueError, TypeError):
                error_msg = f"Invalid num_images: {data.get('num_images', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

        # Initialize database connection and cursor
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if tables exist
        cursor.execute("SHOW TABLES LIKE 'content_prompt'")
        content_prompt_exists = cursor.fetchone() is not None

        cursor.execute("SHOW TABLES LIKE 'content_prompt_claude'")
        content_prompt_claude_exists = cursor.fetchone() is not None

        # Verify that the content_prompt_id exists in the content_prompt table
        prompt_exists = False

        if content_prompt_exists:
            cursor.execute("SELECT id FROM content_prompt WHERE id = %s", (content_prompt_id,))
            if cursor.fetchone():
                prompt_exists = True
                logger.info(f"Content prompt ID {content_prompt_id} found in content_prompt table")

        # If not found in content_prompt, check if it exists in content_prompt_claude
        # and if so, create a copy in content_prompt
        if not prompt_exists and content_prompt_claude_exists:
            cursor.execute("SELECT * FROM content_prompt_claude WHERE id = %s", (content_prompt_id,))
            claude_prompt = cursor.fetchone()

            if claude_prompt:
                logger.info(f"Content prompt ID {content_prompt_id} found in content_prompt_claude table")
                logger.info(f"Creating a copy in content_prompt table...")

                # Create a copy in the content_prompt table with the same ID
                try:
                    # First check if there's a conflict with the ID in content_prompt
                    cursor.execute("SELECT MAX(id) as max_id FROM content_prompt")
                    result = cursor.fetchone()
                    max_id = result['max_id'] if result and result['max_id'] else 0

                    # If the claude prompt ID is greater than the max ID in content_prompt,
                    # we can safely insert with the same ID
                    if content_prompt_id > max_id:
                        cursor.execute(
                            '''
                            INSERT INTO content_prompt
                            (id, title, category_id, content_prompt, call_to_action, output_format, tone, generated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                            ''',
                            (
                                content_prompt_id,
                                claude_prompt.get('topic', ''),
                                claude_prompt.get('category_id'),
                                claude_prompt.get('content_prompt', ''),
                                claude_prompt.get('call_to_action', ''),
                                claude_prompt.get('output_format', 'standard'),
                                claude_prompt.get('tone', 'conversational')
                            )
                        )
                        conn.commit()
                        prompt_exists = True
                        logger.info(f"Successfully created a copy of content prompt ID {content_prompt_id} in content_prompt table")
                    else:
                        # If there's a conflict, we need to create a new entry with a new ID
                        cursor.execute(
                            '''
                            INSERT INTO content_prompt
                            (title, category_id, content_prompt, call_to_action, output_format, tone, generated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, NOW())
                            ''',
                            (
                                claude_prompt.get('topic', ''),
                                claude_prompt.get('category_id'),
                                claude_prompt.get('content_prompt', ''),
                                claude_prompt.get('call_to_action', ''),
                                claude_prompt.get('output_format', 'standard'),
                                claude_prompt.get('tone', 'conversational')
                            )
                        )
                        conn.commit()
                        new_id = cursor.lastrowid
                        logger.info(f"Created a new entry in content_prompt table with ID {new_id} (original ID was {content_prompt_id})")

                        # Update the content_prompt_id to use the new ID
                        content_prompt_id = new_id
                        data['content_prompt_id'] = new_id
                        prompt_exists = True
                except Exception as e:
                    logger.error(f"Error creating copy in content_prompt table: {str(e)}")
                    conn.rollback()

        if not prompt_exists:
            error_msg = f"Content prompt ID {content_prompt_id} does not exist in either content_prompt or content_prompt_claude table"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 400

        # Check if the title already exists (as a double-check on the backend)
        title = data.get('title', '')
        if title:
            cursor.execute("""
                SELECT id, title FROM generated_content
                WHERE LOWER(title) = LOWER(%s)
            """, (title,))

            existing_title = cursor.fetchone()
            if existing_title:
                logger.warning(f"Title already exists in database with ID: {existing_title['id']}")
                # We don't block the creation here since the frontend already handles this
                # Just log it for auditing purposes

        try:
            logger.info("Executing query to insert generated content")
            logger.info(f"Inserting title into title column: {data.get('title', '')[:100]}...")
            logger.info(f"Inserting content into content_text column: {data.get('content_text', '')[:100]}...")
            logger.info(f"Content prompt ID: {content_prompt_id} (required and validated)")
            cursor.execute(
                '''
                INSERT INTO generated_content
                    (content_prompt_id, title, content_text, thumbnail_prompt,
                    speech_duration, speech_location, num_images)
                VALUES
                    (%s, %s, %s, %s, %s, %s, %s)
                ''',
                (
                    content_prompt_id,
                    data.get('title', ''),
                    data.get('content_text', ''),
                    data.get('thumbnail_prompt', ''),
                    speech_duration,
                    data.get('speech_location', ''),
                    num_images
                )
            )
            conn.commit()
            new_id = cursor.lastrowid
            logger.info(f"Successfully created generated content with ID: {new_id}")
            return jsonify({'success': True, 'id': new_id})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error creating generated content: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in create_generated_content: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generated-content/<int:content_id>', methods=['PUT'])
def update_generated_content(content_id):
    conn = None
    cursor = None
    try:
        data = request.json
        logger.info(f"Updating generated content with ID {content_id}: {data}")

        # Convert content_prompt_id to integer if it exists
        content_prompt_id = None
        if data.get('content_prompt_id'):
            try:
                content_prompt_id = int(data['content_prompt_id'])
            except (ValueError, TypeError):
                error_msg = f"Invalid content_prompt_id: {data.get('content_prompt_id', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

        # Convert numeric fields to integers if they exist
        speech_duration = None
        if data.get('speech_duration'):
            try:
                speech_duration = int(data['speech_duration'])
            except (ValueError, TypeError):
                error_msg = f"Invalid speech_duration: {data.get('speech_duration', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

        num_images = None
        if data.get('num_images'):
            try:
                num_images = int(data['num_images'])
            except (ValueError, TypeError):
                error_msg = f"Invalid num_images: {data.get('num_images', 'None')}"
                logger.error(error_msg)
                return jsonify({'error': error_msg}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if generated content exists
        cursor.execute("SELECT id FROM generated_content WHERE id = %s", (content_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Generated content with ID {content_id} not found'}), 404

        try:
            logger.info("Executing query to update generated content")
            cursor.execute(
                '''
                UPDATE generated_content
                SET
                    content_prompt_id = %s,
                    title = %s,
                    content_text = %s,
                    thumbnail_prompt = %s,
                    speech_duration = %s,
                    speech_location = %s,
                    num_images = %s
                WHERE id = %s
                ''',
                (
                    content_prompt_id,
                    data.get('title', ''),
                    data.get('content_text', ''),
                    data.get('thumbnail_prompt', ''),
                    speech_duration,
                    data.get('speech_location', ''),
                    num_images,
                    content_id
                )
            )
            conn.commit()

            if cursor.rowcount == 0:
                return jsonify({'error': f'Generated content with ID {content_id} not found'}), 404

            logger.info(f"Successfully updated generated content with ID: {content_id}")
            return jsonify({'success': True, 'id': content_id})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error updating generated content: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in update_generated_content: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@content_bp.route('/generated-content/<int:content_id>', methods=['DELETE'])
def delete_generated_content(content_id):
    conn = None
    cursor = None
    try:
        logger.info(f"Deleting generated content with ID: {content_id}")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Check if generated content exists
        cursor.execute("SELECT id FROM generated_content WHERE id = %s", (content_id,))
        if not cursor.fetchone():
            return jsonify({'error': f'Generated content with ID {content_id} not found'}), 404

        try:
            cursor.execute("DELETE FROM generated_content WHERE id = %s", (content_id,))
            conn.commit()

            if cursor.rowcount == 0:
                return jsonify({'error': f'Generated content with ID {content_id} not found'}), 404

            logger.info(f"Successfully deleted generated content with ID: {content_id}")
            return jsonify({'success': True})
        except Exception as e:
            conn.rollback()
            error_msg = f"Error deleting generated content: {str(e)}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in delete_generated_content: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@grok_bp.route('/generate-content', methods=['POST'])
def generate_content():
    """Generate content using Grok AI based on a content prompt"""
    logger.info("Received request to generate content with Grok")

    conn = None
    cursor = None

    try:
        if not XAI_API_KEY:
            logger.error("XAI_API_KEY not configured")
            return jsonify({'error': 'Grok API key not configured'}), 500

        data = request.json
        prompt_id = data.get('prompt_id')

        if not prompt_id:
            return jsonify({'error': 'Content prompt ID is required'}), 400

        # Connect to the database
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # Get the content prompt details
        cursor.execute("""
            SELECT cp.*, c.name as category_name
            FROM content_prompt cp
            LEFT JOIN category c ON cp.category_id = c.id
            WHERE cp.id = %s
        """, (prompt_id,))

        prompt_data = cursor.fetchone()

        if not prompt_data:
            return jsonify({'error': f'Content prompt with ID {prompt_id} not found'}), 404

        # Create the prompt for Grok
        prompt = f"""
        Generate a unique, specific title and detailed content based on the following content prompt:

        {prompt_data['content_prompt']}

        Category: {prompt_data['category_name'] if prompt_data['category_name'] else 'General'}

        {f"Call to Action: {prompt_data['call_to_action']}" if prompt_data.get('call_to_action') else ''}

        Tone: {prompt_data.get('tone', 'Friendly')}
        Goal: {prompt_data.get('goal', 'Educate')}
        Target Audience: {prompt_data.get('target_audience', 'General audience')}
        Format: {prompt_data.get('format_type', 'Standard')}
        {f"Video Format: {prompt_data['video_format']}" if prompt_data.get('video_format') else ''}
        {f"Voice ID: {prompt_data['voice_id']}" if prompt_data.get('voice_id') else ''}
        {f"Multiple Topics: {'Yes' if prompt_data['multiple_topics'] else 'No'}" if prompt_data.get('multiple_topics') is not None else ''}
        {f"Number of Sentences: {prompt_data['num_sentences']}" if prompt_data.get('num_sentences') else ''}

        First, create a unique and specific title that narrows down the topic to something very specific within the broader category.
        Then, generate detailed content that directly addresses this specific title.

        Return the response in this exact JSON format:
        {{
          "title": "A unique, specific title for this content",
          "content": "The generated content text that directly addresses the specific title",
          "thumbnail_prompt": "A detailed description for a thumbnail image that would work well for this content"
        }}
        """

        # Call Grok API
        logger.info(f"Calling Grok API at {GROK_API_URL}")

        payload = {
            "model": "grok-2-vision-latest",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }

        logger.info("Payload structure prepared (not showing full content)")

        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }

        logger.info("Sending request to Grok API...")
        response = requests.post(GROK_API_URL, json=payload, headers=headers)

        logger.info(f"Received response with status code: {response.status_code}")

        if response.status_code != 200:
            error_msg = f"Grok API returned error: {response.text}"
            logger.error(error_msg)
            return jsonify({'error': error_msg}), 500

        # Parse the response
        response_data = response.json()
        logger.info(f"Raw response received, length: {len(str(response_data))} characters")

        # Extract the content from the response
        content = response_data['choices'][0]['message']['content']
        logger.info("Successfully extracted content from Grok response")
        logger.info(f"Raw content from Grok: {content[:200]}...")

        # Parse the JSON content
        try:
            # Check if the content is wrapped in markdown code blocks
            if content.strip().startswith('```') and '```' in content:
                # Extract the JSON part from the markdown code block
                content_parts = content.split('```')
                if len(content_parts) >= 3:  # Should have at least 3 parts: before, json, after
                    json_content = content_parts[1]
                    # Remove the 'json' language identifier if present
                    if json_content.strip().startswith('json'):
                        json_content = json_content.strip()[4:].strip()

                    # Try to parse the JSON content
                    try:
                        content_json = json.loads(json_content)
                        # Extract the nested response if it exists
                        if 'response' in content_json:
                            content_json = content_json['response']

                        # Extract the content fields
                        generated_title = content_json.get('title', '')
                        generated_content = content_json.get('content', content_json.get('Content', ''))
                        thumbnail_prompt = content_json.get('thumbnail_prompt', content_json.get('Thumbnail Image Prompt', ''))

                        # Truncate title if it's too long
                        if len(generated_title) > 250:
                            logger.warning(f"Title too long ({len(generated_title)} chars), truncating to 250 chars")
                            generated_title = generated_title[:247] + '...'

                        # Return the generated content with title
                        return jsonify({
                            'title': generated_title,
                            'content': generated_content,
                            'thumbnail_prompt': thumbnail_prompt
                        })
                    except json.JSONDecodeError:
                        logger.error(f"Error parsing JSON from markdown code block")
                        # Fall through to the next parsing attempt

            # Try to parse the content as JSON directly
            content_json = json.loads(content)
            generated_title = content_json.get('title', '')
            generated_content = content_json.get('content', '')
            thumbnail_prompt = content_json.get('thumbnail_prompt', '')

            # Return the generated content with title
            return jsonify({
                'title': generated_title,
                'content': generated_content,
                'thumbnail_prompt': thumbnail_prompt
            })
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON from Grok response: {str(e)}")

            # Try to extract title and content using regex patterns
            title_match = re.search(r'"title":\s*"([^"]+)"', content) or re.search(r'"Title":\s*"([^"]+)"', content)
            content_match = re.search(r'"content":\s*"([^"]+)"', content) or re.search(r'"Content":\s*"([^"]+)"', content)
            thumbnail_match = re.search(r'"thumbnail_prompt":\s*"([^"]+)"', content) or re.search(r'"Thumbnail Image Prompt":\s*"([^"]+)"', content)

            title = title_match.group(1) if title_match else prompt_data['title']
            content_text = content_match.group(1) if content_match else content
            thumbnail = thumbnail_match.group(1) if thumbnail_match else ''

            # If JSON parsing fails, return the extracted content or raw content
            return jsonify({
                'title': title,
                'content': content_text,
                'thumbnail_prompt': thumbnail
            })
    except Exception as e:
        error_msg = f"Error generating content: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@grok_bp.route('/generate-content-from-prompt', methods=['POST'])
def generate_content_from_prompt():
    """Generate content using Grok AI directly from a provided prompt"""
    logger.info("Received request to generate content with Grok from direct prompt")

    try:
        # Get the data from the request
        data = request.json
        logger.info(f"Received data: {data}")

        # Extract the content prompt and number of responses
        content_prompt = data.get('content_prompt')
        num_responses = data.get('num_responses', 1)  # Default to 1 if not specified

        # Validate inputs
        if not content_prompt:
            logger.warning("Content prompt is required but not provided")
            return jsonify({'error': 'Content prompt is required'}), 400

        # Convert num_responses to integer and validate
        try:
            num_responses = int(num_responses)
            if num_responses < 1:
                logger.warning(f"Invalid num_responses: {num_responses}, setting to 1")
                num_responses = 1
            elif num_responses > 5:  # Limit to 5 responses to prevent abuse
                logger.warning(f"num_responses too high: {num_responses}, limiting to 5")
                num_responses = 5
        except (ValueError, TypeError):
            logger.warning(f"Invalid num_responses: {num_responses}, setting to 1")
            num_responses = 1

        logger.info(f"Generating {num_responses} response(s) for content prompt")

        # Create the prompt for Grok based on number of responses requested
        if num_responses == 1:
            # Single response format
            prompt = f"""
            Generate a unique, specific title and detailed content based on the following content prompt:

            {content_prompt}

            First, create a unique, specific, and CONCISE title (maximum 250 characters) that narrows down the topic to something very specific.

            Then, generate detailed content that directly addresses this specific title. Format the content specifically for speech generation with these requirements:

            1. Use short, clear sentences that are easy to read aloud
            2. Include natural pauses and transitions between sections
            3. Avoid complex punctuation or formatting that would be difficult to read aloud
            4. Break content into short paragraphs (2-3 sentences each)
            5. Use conversational language with contractions (e.g., "don't" instead of "do not")
            6. Avoid lengthy lists, tables, or other visual elements that don't translate well to speech
            7. Include phonetic spelling for any uncommon words or names in [brackets]
            8. Mark emphasis with *asterisks* for words that should be stressed
            9. Include occasional (pause) markers where natural pauses should occur
            10. End with a clear call-to-action that works well when spoken

            Return the response in this exact JSON format:
            {{
              "title": "A unique, specific, and concise title for this content (max 250 characters)",
              "content": "The speech-optimized content that follows all the formatting guidelines above"
            }}
            """
        else:
            # Multiple responses format
            prompt = f"""
            Generate {num_responses} unique variations of title and content based on the following content prompt:

            {content_prompt}

            For each variation:
            1. Create a unique, specific, and CONCISE title (maximum 250 characters) that narrows down the topic to something very specific.
            2. Generate detailed content that directly addresses this specific title.

            Format the content specifically for speech generation with these requirements:
            - Use short, clear sentences that are easy to read aloud
            - Include natural pauses and transitions between sections
            - Avoid complex punctuation or formatting that would be difficult to read aloud
            - Break content into short paragraphs (2-3 sentences each)
            - Use conversational language with contractions (e.g., "don't" instead of "do not")
            - Avoid lengthy lists, tables, or other visual elements that don't translate well to speech
            - Include phonetic spelling for any uncommon words or names in [brackets]
            - Mark emphasis with *asterisks* for words that should be stressed
            - Include occasional (pause) markers where natural pauses should occur
            - End with a clear call-to-action that works well when spoken

            Return the response as an array of {num_responses} objects in this exact JSON format:
            [
              {{
                "title": "A unique, specific, and concise title for this content (max 250 characters)",
                "content": "The speech-optimized content that follows all the formatting guidelines above"
              }},
              {{
                "title": "Another unique title for a different approach to the same topic",
                "content": "Different content that addresses the same topic in a unique way"
              }},
              ... (additional objects as needed to reach {num_responses} total)
            ]
            """

        logger.info(f"Created prompt for {num_responses} response(s)")

        # Call Grok API
        logger.info(f"Calling Grok API at {GROK_API_URL}")

        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": "grok-2-vision-latest",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }

        # Make the API call
        response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=60)

        if response.status_code != 200:
            error_text = response.text
            logger.error(f"Grok API error: {error_text}")
            return jsonify({'error': f'Failed to generate content with Grok: {error_text}'}), 500

        # Process the response
        data = response.json()
        content = data["choices"][0]["message"]["content"]

        # Try to extract JSON from the content
        # First, try to extract JSON from markdown code blocks
        if num_responses == 1:
            # Single response format - look for a single JSON object
            json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)

                # Try to parse the JSON content
                try:
                    content_json = json.loads(json_content)
                    # Extract the content fields
                    # Look for title in different possible formats (title, Title)
                    generated_title = content_json.get('title', '') or content_json.get('Title', '')
                    generated_content = content_json.get('content', '') or content_json.get('Content', '')
                    thumbnail_prompt = content_json.get('thumbnail_prompt', '') or content_json.get('thumbnail', '')

                    # Log the extracted fields
                    logger.info(f"Extracted title from JSON: {generated_title[:50]}...")
                    logger.info(f"Extracted content length: {len(generated_content)} characters")
                    logger.info(f"Extracted thumbnail prompt: {thumbnail_prompt[:50] if thumbnail_prompt else 'None'}")

                    # Truncate title if it's too long
                    if len(generated_title) > 250:
                        logger.warning(f"Title too long ({len(generated_title)} chars), truncating to 250 chars")
                        generated_title = generated_title[:247] + '...'

                    # Return the generated content with title
                    return jsonify({
                        'title': generated_title,
                        'content': generated_content,
                        'thumbnail_prompt': thumbnail_prompt
                    })
                except json.JSONDecodeError:
                    logger.error(f"Error parsing JSON from markdown code block")
                    # Fall through to the next parsing attempt
        else:
            # Multiple responses format - look for a JSON array
            json_match = re.search(r'```(?:json)?\s*(\[.*?\])\s*```', content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)

                # Try to parse the JSON content
                try:
                    content_array = json.loads(json_content)
                    logger.info(f"Successfully parsed JSON array with {len(content_array)} items")

                    # Process each item in the array
                    processed_array = []
                    for item in content_array:
                        title = item.get('title', '') or item.get('Title', '')
                        content_text = item.get('content', '') or item.get('Content', '')
                        thumbnail = item.get('thumbnail_prompt', '') or item.get('thumbnail', '')

                        # Log the extracted fields
                        logger.info(f"Extracted title from array item: {title[:50]}...")
                        logger.info(f"Extracted content length: {len(content_text)} characters")
                        logger.info(f"Extracted thumbnail prompt: {thumbnail[:50] if thumbnail else 'None'}")

                        # Truncate title if it's too long
                        if len(title) > 250:
                            logger.warning(f"Title too long ({len(title)} chars), truncating to 250 chars")
                            title = title[:247] + '...'

                        processed_array.append({
                            'title': title,
                            'content': content_text,
                            'thumbnail_prompt': thumbnail
                        })

                    # Return the array of generated content
                    logger.info(f"Returning array of {len(processed_array)} content items")
                    return jsonify(processed_array)
                except json.JSONDecodeError:
                    logger.error(f"Error parsing JSON array from markdown code block")
                    # Fall through to the next parsing attempt

        # Try to parse the content as JSON directly
        try:
            logger.info("Attempting to parse content as JSON directly")

            if num_responses == 1:
                # Single response format
                content_json = json.loads(content)
                logger.info(f"Successfully parsed JSON: {str(content_json)[:200]}...")

                # Look for title in different possible formats (title, Title)
                generated_title = content_json.get('title', '') or content_json.get('Title', '')
                generated_content = content_json.get('content', '') or content_json.get('Content', '')
                thumbnail_prompt = content_json.get('thumbnail_prompt', '') or content_json.get('thumbnail', '')

                # Truncate title if it's too long
                if len(generated_title) > 250:
                    logger.warning(f"Title too long ({len(generated_title)} chars), truncating to 250 chars")
                    generated_title = generated_title[:247] + '...'

                logger.info(f"Extracted title: {generated_title[:50]}...")
                logger.info(f"Extracted content length: {len(generated_content)} characters")
                logger.info(f"Extracted thumbnail prompt: {thumbnail_prompt[:50] if thumbnail_prompt else 'None'}")

                # Return the generated content with title
                result = {
                    'title': generated_title,
                    'content': generated_content,
                    'thumbnail_prompt': thumbnail_prompt
                }
                logger.info(f"Returning JSON response with keys: {list(result.keys())}")
                return jsonify(result)
            else:
                # Multiple responses format - expect an array
                content_array = json.loads(content)
                if not isinstance(content_array, list):
                    logger.warning("Expected JSON array but got a single object, converting to array")
                    content_array = [content_array]

                logger.info(f"Successfully parsed JSON array with {len(content_array)} items")

                # Process each item in the array
                processed_array = []
                for item in content_array:
                    title = item.get('title', '') or item.get('Title', '')
                    content_text = item.get('content', '') or item.get('Content', '')
                    thumbnail = item.get('thumbnail_prompt', '') or item.get('thumbnail', '')

                    # Log the extracted fields
                    logger.info(f"Extracted title from array item: {title[:50]}...")
                    logger.info(f"Extracted content length: {len(content_text)} characters")
                    logger.info(f"Extracted thumbnail prompt: {thumbnail[:50] if thumbnail else 'None'}")

                    # Truncate title if it's too long
                    if len(title) > 250:
                        logger.warning(f"Title too long ({len(title)} chars), truncating to 250 chars")
                        title = title[:247] + '...'

                    processed_array.append({
                        'title': title,
                        'content': content_text,
                        'thumbnail_prompt': thumbnail
                    })

                # Return the array of generated content
                logger.info(f"Returning array of {len(processed_array)} content items")
                return jsonify(processed_array)
        except json.JSONDecodeError as e:
            # If all parsing attempts fail, extract content directly
            logger.error(f"Error parsing JSON from response: {str(e)}")

            if num_responses == 1:
                # Try to extract a title from the content for single response
                title_match = re.search(r'# (.+)', content) or re.search(r'\*\*(.+?)\*\*', content)
                title = title_match.group(1) if title_match else 'Generated Content'

                # Truncate title if it's too long
                if len(title) > 250:
                    logger.warning(f"Title too long ({len(title)} chars), truncating to 250 chars")
                    title = title[:247] + '...'

                logger.info(f"Extracted title from text: {title}")
                logger.info(f"Returning raw content of length: {len(content)} characters")

                # Return the content as is
                result = {
                    'title': title,
                    'content': content
                }
                logger.info(f"Returning raw content response with keys: {list(result.keys())}")
                return jsonify(result)
            else:
                # For multiple responses, try to extract multiple titles and contents
                logger.warning("Attempting to extract multiple responses from raw text")

                # Try to find sections that look like separate responses
                sections = re.split(r'\n\s*\d+\.\s*|\n\s*Response\s*\d+\s*:|\n\s*Item\s*\d+\s*:', content)
                if len(sections) <= 1:
                    # Try other patterns
                    sections = re.split(r'\n\n\s*[\*\-]\s*|\n\n\s*[A-Z][^\n]+:\s*\n', content)

                # Remove empty sections and clean up
                sections = [s.strip() for s in sections if s.strip()]

                if len(sections) <= 1 or len(sections) > 10:
                    # If we couldn't find reasonable sections or too many, just return the whole content as one item
                    logger.warning(f"Could not extract multiple responses, found {len(sections)} sections")
                    return jsonify([{
                        'title': 'Generated Content',
                        'content': content
                    }])

                # Process each section to extract title and content
                results = []
                for i, section in enumerate(sections[:num_responses]):
                    # Try to extract a title from the section
                    title_match = re.search(r'# (.+?)\n|\*\*(.+?)\*\*|Title:\s*(.+?)\n|^(.+?)\n', section, re.DOTALL)
                    title = None
                    if title_match:
                        # Get the first non-None group
                        for group in title_match.groups():
                            if group:
                                title = group.strip()
                                break

                    if not title:
                        title = f"Generated Content {i+1}"

                    # Truncate title if it's too long
                    if len(title) > 250:
                        title = title[:247] + '...'

                    # The content is the rest of the section or the whole section if no title was found
                    content_text = section
                    if title_match and title in section:
                        # Remove the title from the content
                        content_text = section.replace(title, '', 1).strip()

                    results.append({
                        'title': title,
                        'content': content_text
                    })

                logger.info(f"Extracted {len(results)} responses from raw text")
                return jsonify(results)

    except Exception as e:
        logger.error(f"Error generating content: {str(e)}")
        logger.error(traceback.format_exc())

        # Always return a valid response structure even in case of error
        error_message = f'An error occurred while generating content: {str(e)}\n\nPlease try again or contact support if the issue persists.'

        if num_responses == 1:
            # Single response error format
            return jsonify({
                'error': str(e),
                'title': 'Error Generating Content',
                'content': error_message
            }), 500
        else:
            # Multiple response error format - return an array with one error item
            return jsonify([{
                'error': str(e),
                'title': 'Error Generating Content',
                'content': error_message
            }]), 500

# Content Chunk Blueprint
chunk_bp = Blueprint('chunk', __name__, url_prefix='/api')

# Queue Blueprint - Now imported from queue_api.py
from queue_api import queue_bp

# The following code is now in queue_api.py
'''
# Submit chunks to processing queue
@queue_bp.route('/submit', methods=['POST'])
def submit_to_queue():
    conn = None
    cursor = None
    try:
        data = request.json
        chunk_ids = data.get('chunk_ids', [])
        process_steps = data.get('process_steps', [])

        # Validate chunk_ids
        if not chunk_ids:
            return jsonify({'error': 'No chunk IDs provided'}), 400

        # Ensure chunk_ids is a list
        if not isinstance(chunk_ids, list):
            chunk_ids = [chunk_ids]

        # Log the received data
        logger.info(f"Received chunk_ids: {chunk_ids}")
        logger.info(f"Submitting {len(chunk_ids)} chunks to processing queue")
        logger.info(f"Process steps: {process_steps}")

        # Connect to the database
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor(pymysql.cursors.DictCursor)  # Use dictionary cursor for easier column access

        # First, check if process_queue table exists
        cursor.execute("SHOW TABLES LIKE 'process_queue'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Table exists, check its structure
            logger.info("process_queue table exists, checking structure")
            cursor.execute("DESCRIBE process_queue")
            columns = {row['Field']: row for row in cursor.fetchall()}

            # Check if we need to alter the table
            if 'process_step' not in columns or 'start_time' not in columns or 'end_time' not in columns:
                logger.info("Altering process_queue table to add new columns")

                # Drop the table and recreate it with the new structure
                # This is a simple approach for development; in production, you'd want to migrate data
                cursor.execute("DROP TABLE process_queue")
                logger.info("Dropped existing process_queue table")

                # Now create the table with the new structure
                cursor.execute("""
                    CREATE TABLE process_queue (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        content_id INT,
                        chunk_id INT NOT NULL,
                        process_step VARCHAR(50) NOT NULL,
                        status ENUM('pending', 'processing', 'completed', 'failed', 'waiting') DEFAULT 'pending',
                        step_order INT NOT NULL,
                        result_data JSON,
                        error_message TEXT,
                        start_time TIMESTAMP NULL,
                        end_time TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (chunk_id) REFERENCES content_chunk(id) ON DELETE CASCADE
                    )
                """)
                logger.info("Created process_queue table with new structure")
            else:
                logger.info("process_queue table already has the correct structure")
        else:
            # Table doesn't exist, create it
            logger.info("Creating process_queue table")
            cursor.execute("""
                CREATE TABLE process_queue (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    content_id INT,
                    chunk_id INT NOT NULL,
                    process_step VARCHAR(50) NOT NULL,
                    status ENUM('pending', 'processing', 'completed', 'failed', 'waiting') DEFAULT 'pending',
                    step_order INT NOT NULL,
                    result_data JSON,
                    error_message TEXT,
                    start_time TIMESTAMP NULL,
                    end_time TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (chunk_id) REFERENCES content_chunk(id) ON DELETE CASCADE
                )
            """)
            logger.info("Created process_queue table")

        conn.commit()

        # Track successfully inserted chunks
        inserted_chunks = []
        skipped_chunks = []

        # Insert each chunk into the queue
        for chunk_id in chunk_ids:
            try:
                # Log the current chunk being processed
                logger.info(f"Processing chunk ID: {chunk_id}")

                # Check if chunk exists and get its content_id
                cursor.execute("SELECT id, content_id FROM content_chunk WHERE id = %s", (chunk_id,))
                chunk_result = cursor.fetchone()

                if not chunk_result:
                    logger.warning(f"Chunk ID {chunk_id} not found, skipping")
                    skipped_chunks.append({"id": chunk_id, "reason": "not_found"})
                    continue

                # With DictCursor, we can access columns by name
                content_id = chunk_result.get('content_id')
                if not content_id:
                    logger.warning(f"Chunk ID {chunk_id} has no content_id, using NULL")
                    content_id = None
                logger.info(f"Chunk ID {chunk_id} exists in content_chunk table with Content ID {content_id}")

                # Check if chunk is already in queue for any of the steps
                try:
                    cursor.execute("SELECT id, process_step FROM process_queue WHERE chunk_id = %s AND status != 'completed'", (chunk_id,))
                    queue_results = cursor.fetchall()

                    if queue_results:
                        # With DictCursor, each row is a dictionary
                        existing_steps = [r.get('process_step', 'unknown') for r in queue_results]
                        logger.warning(f"Chunk ID {chunk_id} already has steps {existing_steps} in the queue, skipping")
                        skipped_chunks.append({"id": chunk_id, "reason": "already_in_queue", "existing_steps": existing_steps})
                        continue
                except Exception as e:
                    # This might happen if the table structure is still being updated
                    logger.warning(f"Error checking if chunk is in queue: {str(e)}")
                    # Continue with insertion anyway

                logger.info(f"Chunk ID {chunk_id} is not already in the queue, proceeding with insertion of all steps")

                # Insert each process step into the queue
                chunk_queue_ids = []
                for step_index, step in enumerate(process_steps):
                    # Insert into queue
                    cursor.execute("""
                        INSERT INTO process_queue
                        (content_id, chunk_id, process_step, status, step_order, result_data)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        content_id,
                        chunk_id,
                        step,
                        'pending' if step_index == 0 else 'waiting',  # First step is pending, others are waiting
                        step_index + 1,  # 1-based step order
                        json.dumps({})
                    ))

                    # Get the ID of the inserted queue item
                    inserted_id = cursor.lastrowid
                    chunk_queue_ids.append({"step": step, "queue_id": inserted_id})
                    logger.info(f"Inserted step '{step}' for chunk ID {chunk_id} into queue with queue ID {inserted_id}")

                # Add to the list of successfully inserted chunks
                inserted_chunks.append({
                    "chunk_id": chunk_id,
                    "content_id": content_id,
                    "queue_ids": chunk_queue_ids
                })
                logger.info(f"Successfully inserted all steps for chunk ID {chunk_id} into queue")

            except Exception as e:
                logger.error(f"Error processing chunk ID {chunk_id}: {str(e)}")
                skipped_chunks.append({"id": chunk_id, "reason": f"error: {str(e)}"})

        conn.commit()
        logger.info(f"Chunks successfully added to processing queue: {len(inserted_chunks)} inserted, {len(skipped_chunks)} skipped")

        return jsonify({
            'success': True,
            'message': f"{len(inserted_chunks)} chunks submitted to processing queue, {len(skipped_chunks)} skipped",
            'inserted': inserted_chunks,
            'skipped': skipped_chunks,
            'total_processed': len(chunk_ids)
        })

    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error submitting chunks to queue: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': f"Failed to submit chunks to queue: {str(e)}"}), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
'''

# Check if chunks exist for a content
@chunk_bp.route('/check-chunks/<int:content_id>', methods=['GET'])
def check_chunks(content_id):
    try:
        logger.info(f"Checking if chunks exist for content ID: {content_id}")

        # Run the async function in a synchronous context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        from generate_chunks import check_existing_chunks, create_db_pool
        pool = loop.run_until_complete(create_db_pool())
        chunk_count = loop.run_until_complete(check_existing_chunks(pool, content_id))
        pool.close()
        loop.run_until_complete(pool.wait_closed())
        loop.close()

        return jsonify({
            'exists': chunk_count > 0,
            'count': chunk_count
        })
    except Exception as e:
        error_msg = f"Error checking chunks: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500

# Generate chunks for a content
@chunk_bp.route('/generate-chunks/<int:content_id>', methods=['POST'])
def generate_chunks(content_id):
    try:
        logger.info(f"Generating chunks for content ID: {content_id}")

        # Check if we should overwrite existing chunks
        # Handle different request formats safely
        overwrite = False
        if request.is_json and request.json is not None:
            overwrite = request.json.get('overwrite', False)
        elif request.form:
            overwrite = request.form.get('overwrite', 'false').lower() == 'true'
        elif request.data:
            try:
                data = json.loads(request.data.decode('utf-8'))
                overwrite = data.get('overwrite', False)
            except:
                # If we can't parse the data, default to False
                pass

        logger.info(f"Overwrite parameter: {overwrite}")

        # Run the async function in a synchronous context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(generate_chunks_for_content(content_id, overwrite))
        loop.close()

        if result.get('success'):
            logger.info(f"Successfully generated {len(result.get('chunks', []))} chunks for content ID: {content_id}")
            return jsonify(result)
        elif result.get('chunks_exist'):
            # Special case: chunks exist and overwrite=False
            logger.info(f"Chunks already exist for content ID: {content_id} and overwrite=False")
            return jsonify({
                'success': False,
                'chunks_exist': True,
                'count': result.get('count', 0),
                'message': f"Found {result.get('count', 0)} existing chunks. Set overwrite=true to replace them."
            })
        else:
            error_msg = result.get('error', 'Unknown error')
            logger.error(f"Error generating chunks: {error_msg}")
            return jsonify({'error': error_msg}), 500
    except Exception as e:
        error_msg = f"Error in generate_chunks: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500

# Get all content chunks
@chunk_bp.route('/content-chunks', methods=['GET'])
def get_content_chunks():
    conn = None
    cursor = None
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        query = """
        SELECT id, content_id, chunk_order, text, image_prompt_id, audio_file, created_at
        FROM content_chunk
        ORDER BY content_id, chunk_order
        """

        cursor.execute(query)
        chunks = cursor.fetchall()

        return jsonify(chunks)
    except Exception as e:
        error_msg = f"Error fetching content chunks: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Get a specific content chunk by ID
@chunk_bp.route('/content-chunks/<int:chunk_id>', methods=['GET'])
def get_content_chunk(chunk_id):
    conn = None
    cursor = None
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        query = """
        SELECT id, content_id, chunk_order, text, image_prompt_id, audio_file, created_at
        FROM content_chunk
        WHERE id = %s
        """

        cursor.execute(query, (chunk_id,))
        chunk = cursor.fetchone()

        if not chunk:
            return jsonify({'error': 'Content chunk not found'}), 404

        return jsonify(chunk)
    except Exception as e:
        error_msg = f"Error fetching content chunk: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Create a new content chunk
@chunk_bp.route('/content-chunks', methods=['POST'])
def create_content_chunk():
    conn = None
    cursor = None
    try:
        data = request.json

        # Validate required fields
        if not data.get('content_id') or not isinstance(data.get('chunk_order'), int):
            return jsonify({'error': 'Content ID and chunk order are required'}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        query = """
        INSERT INTO content_chunk (content_id, chunk_order, text, image_prompt_id, audio_file)
        VALUES (%s, %s, %s, %s, %s)
        """

        cursor.execute(
            query,
            (
                data.get('content_id'),
                data.get('chunk_order'),
                data.get('text'),
                data.get('image_prompt_id'),
                data.get('audio_file')
            )
        )

        conn.commit()

        # Get the ID of the newly created chunk
        new_chunk_id = cursor.lastrowid

        return jsonify({
            'id': new_chunk_id,
            'message': 'Content chunk created successfully'
        }), 201
    except Exception as e:
        if conn:
            conn.rollback()
        error_msg = f"Error creating content chunk: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Update an existing content chunk
@chunk_bp.route('/content-chunks/<int:chunk_id>', methods=['PUT'])
def update_content_chunk(chunk_id):
    conn = None
    cursor = None
    try:
        data = request.json

        # Validate required fields
        if not data.get('content_id') or not isinstance(data.get('chunk_order'), int):
            return jsonify({'error': 'Content ID and chunk order are required'}), 400

        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if the chunk exists
        cursor.execute("SELECT id FROM content_chunk WHERE id = %s", (chunk_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'Content chunk not found'}), 404

        query = """
        UPDATE content_chunk
        SET content_id = %s, chunk_order = %s, text = %s, image_prompt_id = %s, audio_file = %s
        WHERE id = %s
        """

        cursor.execute(
            query,
            (
                data.get('content_id'),
                data.get('chunk_order'),
                data.get('text'),
                data.get('image_prompt_id'),
                data.get('audio_file'),
                chunk_id
            )
        )

        conn.commit()

        return jsonify({'message': 'Content chunk updated successfully'})
    except Exception as e:
        if conn:
            conn.rollback()
        error_msg = f"Error updating content chunk: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Delete a content chunk
@chunk_bp.route('/content-chunks/<int:chunk_id>', methods=['DELETE'])
def delete_content_chunk(chunk_id):
    conn = None
    cursor = None
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check if the chunk exists
        cursor.execute("SELECT id FROM content_chunk WHERE id = %s", (chunk_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'Content chunk not found'}), 404

        # Delete the chunk
        cursor.execute("DELETE FROM content_chunk WHERE id = %s", (chunk_id,))
        conn.commit()

        return jsonify({'message': 'Content chunk deleted successfully'})
    except Exception as e:
        if conn:
            conn.rollback()
        error_msg = f"Error deleting content chunk: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Get image prompts for dropdown
@chunk_bp.route('/image-prompts', methods=['GET'])
def get_image_prompts():
    conn = None
    cursor = None
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        query = """
        SELECT id, prompt_text
        FROM image_prompt
        ORDER BY id
        """

        cursor.execute(query)
        prompts = cursor.fetchall()

        return jsonify(prompts)
    except Exception as e:
        error_msg = f"Error fetching image prompts: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Register blueprints
app.register_blueprint(grok_bp)
app.register_blueprint(content_bp)
app.register_blueprint(chunk_bp)
app.register_blueprint(queue_bp)

if __name__ == '__main__':
    logger.info("Starting consolidated API server...")
    logger.info("API endpoints available at http://localhost:5000/")

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)

