import sqlite3

# Connect to the database
conn = sqlite3.connect('vlog_content.db')
cursor = conn.cursor()

# Check if content_prompt_claude table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='content_prompt_claude'")
table_exists = cursor.fetchone() is not None
print('Table exists:', table_exists)

if table_exists:
    # Get schema of content_prompt_claude table
    cursor.execute("PRAGMA table_info(content_prompt_claude)")
    columns = cursor.fetchall()
    print("\nColumns in content_prompt_claude table:")
    for column in columns:
        print(column)
else:
    print("\nTable does not exist. Creating table...")
    
    # Create the content_prompt_claude table with all the parameters
    cursor.execute('''
    CREATE TABLE content_prompt_claude (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        topic TEXT NOT NULL,
        content_type TEXT,
        purpose TEXT,
        audience TEXT,
        tone TEXT,
        style TEXT,
        word_count TEXT,
        persona TEXT,
        content_prompt TEXT,
        call_to_action TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    conn.commit()
    print("Table created successfully!")
    
    # Verify the table was created
    cursor.execute("PRAGMA table_info(content_prompt_claude)")
    columns = cursor.fetchall()
    print("\nColumns in content_prompt_claude table:")
    for column in columns:
        print(column)

# Close the connection
conn.close()
