import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import DashboardNew from './components/DashboardNew.jsx';
import ContentManagement from './components/ContentManagement';
import ContentDetails from './components/ContentDetails';
import ContentChunk from './components/ContentChunk';
import Videos from './components/Videos.jsx';
import Settings from './components/Settings.jsx';
import Cheatsheets from './components/Cheatsheets.jsx';
import Profile from './components/Profile.jsx';
import Logout from './components/Logout.jsx';
import Layout from './components/Layout.jsx';
import LandingPage from './components/LandingPage';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  return (
    <Router>
      <Routes>
        {/* Landing page as default route */}
        <Route path="/" element={<LandingPage />} />

        {/* Dashboard and app routes with Layout */}
        <Route path="/dashboard" element={<Layout />}>
          <Route index element={<DashboardNew />} />
          <Route path="content-management" element={<Navigate to="/dashboard/content-management/claude-prompting" replace />} />
          <Route path="content-management/prompts" element={<Navigate to="/dashboard/content-management/claude-prompting" replace />} />
          <Route path="content-management/details" element={<ContentManagement key="details" />} />
          <Route path="content-management/chunks" element={<ContentManagement key="chunks" />} />
          <Route path="content-management/claude-prompting" element={<ContentManagement key="claude-prompting" />} />
          <Route path="videos" element={<Videos />} />
          <Route path="cheatsheets" element={<Cheatsheets />} />
          <Route path="settings" element={<Settings />} />
          <Route path="settings/category-management" element={<Settings />} />
          <Route path="settings/api-settings" element={<Settings />} />
          <Route path="profile" element={<Profile />} />
          <Route path="logout" element={<Logout />} />
        </Route>

        {/* Fallback route for any unmatched routes */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

export default App;


