import React from 'react';
import { Card, Table, Row, Col, Badge, Alert } from 'react-bootstrap';
import GrokPromptGenerator from './GrokPromptGenerator';
import './Cheatsheets.css';

const ThumbnailPromptCheatsheet = () => {
  return (
    <div className="cheatsheet-container">
      <GrokPromptGenerator promptType="thumbnail" />
      <Card className="mb-4">
        <Card.Header className="bg-dark text-white">
          <h4 className="mb-0">🖼️ Prompt Engineering Cheatsheet for AI Thumbnail Creation (Scene, Mood & Emotion) – 2025 Edition</h4>
        </Card.Header>
        <Card.Body>
          <p className="lead">
            A 10x repetition-tuned guide to help you design ultra-effective image prompts that create emotional, eye-catching thumbnails for YouTube, content channels, courses, and beyond.
          </p>
          <Alert variant="info">
            <strong>New Feature:</strong> The thumbnail generator now includes text overlay suggestions! <PERSON><PERSON> will analyze your topic, emotion, and style to suggest compelling text to overlay on your thumbnail image.
          </Alert>
        </Card.Body>
      </Card>

      {/* 10X Repetition Framework */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🔁 The 10X Repetition Framework</h5>
        </Card.Header>
        <Card.Body>
          <ol className="mb-0">
            <li><strong>Target Audience</strong> – Who will view the thumbnail?</li>
            <li><strong>Content Type</strong> – Vlog, tutorial, reaction, drama, etc.</li>
            <li><strong>Main Subject</strong> – Person, character, object, symbol.</li>
            <li><strong>Scene Description</strong> – Where are they? What's happening?</li>
            <li><strong>Emotion/Mood</strong> – Fear, joy, suspense, urgency, curiosity.</li>
            <li><strong>Facial Expression or Gesture</strong> – Open mouth, shocked, smiling, etc.</li>
            <li><strong>Color Palette</strong> – Warm (energy), Cool (calm), Contrasts (attention).</li>
            <li><strong>Lighting</strong> – Backlight, spotlight, glow, cinematic shadows.</li>
            <li><strong>Composition</strong> – Rule of thirds, close-up, zoomed out, centered text.</li>
            <li><strong>Style</strong> – Realistic, cartoon, cinematic, pop-art, minimalistic.</li>
          </ol>
        </Card.Body>
      </Card>

      {/* Prompt Structure Template */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🧠 Prompt Structure Template</h5>
        </Card.Header>
        <Card.Body>
          <div className="bg-light p-3 rounded">
            <pre className="mb-0">
              "A [main subject] is [doing something] in a [location], showing [emotion/mood]. The thumbnail has [color scheme], [lighting style], and is in [visual style] designed to grab attention."
            </pre>
          </div>
        </Card.Body>
      </Card>

      {/* Common Use-Case Examples */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">💡 Common Use-Case Examples</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6} className="mb-3">
              <h6>🎬 YouTube Vlog</h6>
              <blockquote className="blockquote">
                <p className="mb-0">
                  "A shocked young man covering his mouth, in front of a burning car. The mood is intense and urgent. Red and orange flames contrast with a dark blue night. Cinematic lighting, hyperrealistic style."
                </p>
              </blockquote>
            </Col>
            <Col md={6} className="mb-3">
              <h6>📚 Educational Tutorial</h6>
              <blockquote className="blockquote">
                <p className="mb-0">
                  "A focused woman writing formulas on a transparent screen with neon math symbols around her. The tone is inspiring and smart. Blue glow lighting with a futuristic style."
                </p>
              </blockquote>
            </Col>
          </Row>
          <Row>
            <Col md={6} className="mb-3">
              <h6>👀 Reaction Video</h6>
              <blockquote className="blockquote">
                <p className="mb-0">
                  "Two friends wide-eyed with surprised expressions while watching a giant screen. Vibrant lighting, high contrast. Style: digital cartoon realism."
                </p>
              </blockquote>
            </Col>
            <Col md={6} className="mb-3">
              <h6>🧘 Wellness/Calm</h6>
              <blockquote className="blockquote">
                <p className="mb-0">
                  "A woman meditating on a beach during sunset. Peaceful emotion. Warm pastel palette with soft lighting. Style: watercolor digital art."
                </p>
              </blockquote>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Emotion-Centric Visual Language */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎨 Emotion-Centric Visual Language</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Emotion</th>
                <th>Visual Cues</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Shock</td>
                <td>Wide eyes, open mouth, tilted camera</td>
              </tr>
              <tr>
                <td>Joy</td>
                <td>Smile, bright light, confetti/sparkles</td>
              </tr>
              <tr>
                <td>Mystery</td>
                <td>Shadows, hooded figure, dark blue tones</td>
              </tr>
              <tr>
                <td>Urgency</td>
                <td>Red arrows, timers, explosions, zoom-in</td>
              </tr>
              <tr>
                <td>Inspiration</td>
                <td>Uplight on face, sparkles, sunlight burst</td>
              </tr>
              <tr>
                <td>Suspense</td>
                <td>Cropped faces, close-ups, high contrast shadows</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Thumbnail Prompt Refinement Loop */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎯 Thumbnail Prompt Refinement Loop</h5>
        </Card.Header>
        <Card.Body>
          <p>Use this loop to iterate:</p>
          <ol className="mb-0">
            <li>Write a basic prompt</li>
            <li>Add emotional context</li>
            <li>Include lighting & scene</li>
            <li>Test style variations</li>
            <li>Iterate tone/mood until visual message is 100% aligned with video intent</li>
          </ol>
        </Card.Body>
      </Card>

      {/* Thumbnail-Friendly Styles */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🎥 Thumbnail-Friendly Styles</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Style</th>
                <th>Best For</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Digital Realism</td>
                <td>Reactions, Storytime</td>
              </tr>
              <tr>
                <td>3D Illustration</td>
                <td>Gaming, Explainers</td>
              </tr>
              <tr>
                <td>Pop Art / Cartoon</td>
                <td>Kids content, Humor</td>
              </tr>
              <tr>
                <td>Watercolor</td>
                <td>Meditation, Calm content</td>
              </tr>
              <tr>
                <td>Cinematic</td>
                <td>Documentaries, Dramas</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Tools Optimized For */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">🛠 Tools Optimized For</h5>
        </Card.Header>
        <Card.Body>
          <ul className="mb-0">
            <li><strong>MidJourney</strong></li>
            <li><strong>DALL·E 3</strong></li>
            <li><strong>Leonardo AI</strong></li>
            <li><strong>Stable Diffusion</strong></li>
            <li><strong>Bing Image Creator</strong></li>
          </ul>
        </Card.Body>
      </Card>

      {/* Tips */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">💡 Tips</h5>
        </Card.Header>
        <Card.Body>
          <ul className="mb-0">
            <li>Be specific with <strong>scene composition</strong></li>
            <li>Choose <strong>color palette</strong> to match emotion</li>
            <li>Always test and refine prompt after first output</li>
            <li>You can stack <strong>multiple moods</strong>: "hopeful yet lonely"</li>
            <li>Keep text overlays <strong>short and impactful</strong> (1-4 words)</li>
            <li>Use <strong>contrasting colors</strong> for text to ensure readability</li>
            <li>Position text in the <strong>rule of thirds</strong> areas for maximum impact</li>
          </ul>
        </Card.Body>
      </Card>

      {/* Final Example Prompt */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">✨ Final Example Prompt</h5>
        </Card.Header>
        <Card.Body>
          <blockquote className="blockquote">
            <p className="mb-0">
              "A shocked man in a red hoodie pointing at a laptop screen. Scene: messy room. Mood: urgent, high tension. Color: Red-blue contrast. Light: glowing screen. Style: cinematic hyperrealism. Composition: close-up with blurred background."
            </p>
          </blockquote>
          <div className="mt-3">
            <h6>Example Text Overlay:</h6>
            <div className="text-center p-3" style={{ background: 'linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.4))' }}>
              <span style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#fff',
                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                padding: '10px 20px',
                display: 'inline-block'
              }}>
                HACKED!
              </span>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Credits */}
      <Card className="mb-4">
        <Card.Footer className="text-center">
          <p className="mb-0">
            <small>
              Crafted with precision using the <strong>PromptGPT AI Repetition Protocol™</strong>
            </small>
          </p>
        </Card.Footer>
      </Card>
    </div>
  );
};

export default ThumbnailPromptCheatsheet;
