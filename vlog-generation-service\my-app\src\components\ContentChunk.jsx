import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Table, Button, Form, Modal, Spinner, Alert, Pagination, InputGroup, Badge, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import './Dashboard.css';
import './ContentChunk.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

function ContentChunk({ setError: setParentError }) {
  const [contentChunks, setContentChunks] = useState([]);
  const [contentOptions, setContentOptions] = useState([]);
  const [imagePromptOptions, setImagePromptOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add', 'edit', 'delete'
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [idFilter, setIdFilter] = useState('');
  const [selectedChunks, setSelectedChunks] = useState([]);
  const [showQueueModal, setShowQueueModal] = useState(false);
  const [isSubmittingToQueue, setIsSubmittingToQueue] = useState(false);
  const [formData, setFormData] = useState({
    id: '',
    content_id: '',
    chunk_order: '',
    text: '',
    image_prompt_id: '',
    audio_file: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const navigate = useNavigate();

  // Fetch content chunks on component mount
  useEffect(() => {
    fetchContentChunks();
    fetchContentOptions();
    fetchImagePromptOptions();
  }, []);

  // Fetch content chunks from API
  const fetchContentChunks = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/content-chunks`);

      if (!response.ok) {
        throw new Error(`Failed to fetch content chunks: ${response.status}`);
      }

      const data = await response.json();
      setContentChunks(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching content chunks:', error);
      const errorMessage = `Failed to load content chunks: ${error.message}`;
      setError(errorMessage);
      if (setParentError) setParentError(errorMessage);
      setLoading(false);
    }
  };

  // Fetch content options for dropdown
  const fetchContentOptions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/generated-content`);

      if (!response.ok) {
        throw new Error(`Failed to fetch content options: ${response.status}`);
      }

      const data = await response.json();
      setContentOptions(data);
    } catch (error) {
      console.error('Error fetching content options:', error);
      // We don't set the error state here to avoid blocking the main functionality
    }
  };

  // Fetch image prompt options for dropdown
  const fetchImagePromptOptions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/image-prompts`);

      if (!response.ok) {
        throw new Error(`Failed to fetch image prompt options: ${response.status}`);
      }

      const data = await response.json();
      setImagePromptOptions(data);
    } catch (error) {
      console.error('Error fetching image prompt options:', error);
      // We don't set the error state here to avoid blocking the main functionality
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = {};

    if (!formData.content_id) {
      errors.content_id = 'Content is required';
    }

    if (!formData.chunk_order) {
      errors.chunk_order = 'Chunk order is required';
    } else if (isNaN(formData.chunk_order) || parseInt(formData.chunk_order) < 0) {
      errors.chunk_order = 'Chunk order must be a positive number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle add new content chunk
  const handleAddContentChunk = () => {
    setFormData({
      id: '',
      content_id: '',
      chunk_order: '',
      text: '',
      image_prompt_id: '',
      audio_file: ''
    });
    setModalMode('add');
    setShowModal(true);
  };

  // Handle edit content chunk
  const handleEditContentChunk = (chunk) => {
    setFormData({
      id: chunk.id,
      content_id: chunk.content_id,
      chunk_order: chunk.chunk_order,
      text: chunk.text || '',
      image_prompt_id: chunk.image_prompt_id || '',
      audio_file: chunk.audio_file || ''
    });
    setModalMode('edit');
    setShowModal(true);
  };

  // Handle delete content chunk
  const handleDeleteContentChunk = (chunk) => {
    setFormData({
      id: chunk.id,
      content_id: chunk.content_id,
      chunk_order: chunk.chunk_order,
      text: chunk.text || '',
      image_prompt_id: chunk.image_prompt_id || '',
      audio_file: chunk.audio_file || ''
    });
    setModalMode('delete');
    setShowModal(true);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (modalMode === 'delete') {
      await deleteContentChunk();
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      const url = modalMode === 'add'
        ? `${API_BASE_URL}/api/content-chunks`
        : `${API_BASE_URL}/api/content-chunks/${formData.id}`;

      const method = modalMode === 'add' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content_id: parseInt(formData.content_id),
          chunk_order: parseInt(formData.chunk_order),
          text: formData.text,
          image_prompt_id: formData.image_prompt_id ? parseInt(formData.image_prompt_id) : null,
          audio_file: formData.audio_file
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${modalMode} content chunk`);
      }

      // Refresh the content chunks list
      await fetchContentChunks();

      // Show success message
      setSuccess(`Content chunk ${modalMode === 'add' ? 'added' : 'updated'} successfully`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Close the modal
      setShowModal(false);
    } catch (error) {
      console.error(`Error ${modalMode}ing content chunk:`, error);
      const errorMessage = error.message;
      setError(errorMessage);
      if (setParentError) setParentError(errorMessage);
    }
  };

  // Handle delete content chunk
  const deleteContentChunk = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/content-chunks/${formData.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete content chunk');
      }

      // Refresh the content chunks list
      await fetchContentChunks();

      // Show success message
      setSuccess('Content chunk deleted successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Close the modal
      setShowModal(false);
    } catch (error) {
      console.error('Error deleting content chunk:', error);
      const errorMessage = error.message;
      setError(errorMessage);
      if (setParentError) setParentError(errorMessage);
    }
  };

  // Filter content chunks based on search term and ID filter
  const filteredContentChunks = contentChunks.filter(chunk => {
    // Apply ID filter if present
    if (idFilter && !chunk.id.toString().includes(idFilter)) {
      return false;
    }

    // Apply general search term
    return (
      chunk.id.toString().includes(searchTerm) ||
      (chunk.text && chunk.text.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredContentChunks.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredContentChunks.length / itemsPerPage);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Get content title by ID
  const getContentTitle = (contentId) => {
    const content = contentOptions.find(c => c.id === contentId);
    return content ? content.title : 'Unknown';
  };

  // Get image prompt text by ID
  const getImagePromptText = (promptId) => {
    if (!promptId) return 'None';
    const prompt = imagePromptOptions.find(p => p.id === promptId);
    return prompt ? prompt.prompt_text : 'Unknown';
  };

  // Handle checkbox selection
  const handleChunkSelection = (chunkId) => {
    setSelectedChunks(prev => {
      if (prev.includes(chunkId)) {
        return prev.filter(id => id !== chunkId);
      } else {
        return [...prev, chunkId];
      }
    });
  };

  // Handle select all chunks
  const handleSelectAllChunks = () => {
    if (selectedChunks.length === currentItems.length) {
      // If all are selected, deselect all
      setSelectedChunks([]);
    } else {
      // Otherwise, select all
      setSelectedChunks(currentItems.map(chunk => chunk.id));
    }
  };

  // Open queue modal
  const openQueueModal = () => {
    if (selectedChunks.length === 0) {
      setError('Please select at least one content chunk to submit to the queue');
      return;
    }
    setShowQueueModal(true);
  };

  // Submit selected chunks to queue
  const submitToQueue = async () => {
    try {
      setIsSubmittingToQueue(true);

      // Log the selected chunks before submission
      console.log('Selected chunks for queue submission:', selectedChunks);

      // Ensure selectedChunks is an array of integers
      const chunkIdsToSubmit = selectedChunks.map(id => parseInt(id));
      console.log('Chunk IDs to submit (after conversion):', chunkIdsToSubmit);

      // Define the process steps
      const processSteps = [
        'generate_images',
        'generate_speech',
        'create_slideshow',
        'create_subtitles',
        'mix_components'
      ];

      // Prepare the request payload
      const payload = {
        chunk_ids: chunkIdsToSubmit,
        process_steps: processSteps
      };

      console.log('Submitting payload to API:', payload);

      // Make the API call to submit the selected chunks to the queue
      const response = await fetch(`${API_BASE_URL}/api/queue/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit chunks to queue');
      }

      // Parse the response
      const responseData = await response.json();
      console.log('Queue submission response:', responseData);

      // Show detailed success message
      const insertedCount = responseData.inserted?.length || 0;
      const skippedCount = responseData.skipped?.length || 0;
      const totalSteps = responseData.inserted?.reduce((total, chunk) => total + chunk.queue_ids.length, 0) || 0;

      setSuccess(
        `${insertedCount} content chunk(s) submitted to queue successfully with ${totalSteps} total processing steps. ` +
        (skippedCount > 0 ? `${skippedCount} chunk(s) were skipped.` : '')
      );

      // Clear selected chunks
      setSelectedChunks([]);

      // Close the modal
      setShowQueueModal(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error submitting chunks to queue:', error);
      const errorMessage = error.message;
      setError(errorMessage);
      if (setParentError) setParentError(errorMessage);
    } finally {
      setIsSubmittingToQueue(false);
    }
  };

  return (
    <div>
      <div className="mb-2">
        <h5>Content Chunks</h5>
        <p className="text-muted mb-0">Manage content chunks for your videos</p>
      </div>

      {/* Success and Error Messages */}
      {success && (
        <Alert variant="success" onClose={() => setSuccess(null)} dismissible>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          {error}
        </Alert>
      )}

      {/* Search Bar, ID Filter, and Buttons */}
      <div className="d-flex mb-3 align-items-center flex-wrap">
        <div className="search-container position-relative me-3" style={{ maxWidth: '300px' }}>
          <Form.Control
            type="text"
            placeholder="Search by text..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <div className="position-absolute" style={{ top: '50%', left: '10px', transform: 'translateY(-50%)' }}>
            <i className="fas fa-search text-muted"></i>
          </div>
        </div>

        <div className="me-3" style={{ width: '150px' }}>
          <Form.Control
            type="text"
            placeholder="Filter by ID"
            value={idFilter}
            onChange={(e) => setIdFilter(e.target.value)}
            className="id-filter-input"
          />
        </div>

        <div className="ms-auto d-flex">
          {selectedChunks.length > 0 && (
            <Button
              variant="success"
              onClick={openQueueModal}
              className="me-2"
            >
              <i className="fas fa-tasks me-2"></i>
              Submit to Queue ({selectedChunks.length})
            </Button>
          )}

          <Button
            variant="primary"
            onClick={handleAddContentChunk}
            className="add-content-btn"
          >
            <i className="fas fa-plus me-2"></i>
            Add Content Chunk
          </Button>
        </div>
      </div>

      {/* Content Chunks Table */}
      {loading ? (
        <div className="text-center my-3">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
        </div>
      ) : (
        <div className="table-responsive">
          <Table bordered hover className="content-table">
            <thead>
              <tr>
                <th style={{ width: '40px' }}>
                  <Form.Check
                    type="checkbox"
                    onChange={handleSelectAllChunks}
                    checked={currentItems.length > 0 && selectedChunks.length === currentItems.length}
                    title="Select/Deselect All"
                  />
                </th>
                <th>
                  ID
                  <OverlayTrigger
                    placement="top"
                    overlay={<Tooltip>Filter is applied to this column</Tooltip>}
                  >
                    <Badge bg="info" className="ms-1" style={{ cursor: 'help' }}>
                      <i className="fas fa-filter"></i>
                    </Badge>
                  </OverlayTrigger>
                </th>
                <th>Content</th>
                <th>Order</th>
                <th>Text</th>
                <th>Image Prompt</th>
                <th>Audio File</th>
                <th>Created At</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentItems.length > 0 ? (
                currentItems.map((chunk) => (
                  <tr key={chunk.id} className={selectedChunks.includes(chunk.id) ? 'table-primary' : ''}>
                    <td>
                      <Form.Check
                        type="checkbox"
                        onChange={() => handleChunkSelection(chunk.id)}
                        checked={selectedChunks.includes(chunk.id)}
                      />
                    </td>
                    <td>{chunk.id}</td>
                    <td>{getContentTitle(chunk.content_id)}</td>
                    <td>{chunk.chunk_order}</td>
                    <td>
                      {chunk.text ? (
                        chunk.text.length > 50 ? `${chunk.text.substring(0, 50)}...` : chunk.text
                      ) : 'N/A'}
                    </td>
                    <td>{getImagePromptText(chunk.image_prompt_id)}</td>
                    <td>{chunk.audio_file || 'N/A'}</td>
                    <td>{new Date(chunk.created_at).toLocaleString()}</td>
                    <td>
                      <div className="d-flex gap-2 justify-content-center">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          className="action-btn"
                          onClick={() => handleEditContentChunk(chunk)}
                        >
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          className="action-btn"
                          onClick={() => handleDeleteContentChunk(chunk)}
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="text-center">No content chunks found</td>
                </tr>
              )}
            </tbody>
          </Table>
        </div>
      )}

      {/* Add/Edit/Delete Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {modalMode === 'add' ? 'Add New Content Chunk' :
             modalMode === 'edit' ? 'Edit Content Chunk' :
             'Delete Content Chunk'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {modalMode === 'delete' ? (
            <p>Are you sure you want to delete this content chunk? This action cannot be undone.</p>
          ) : (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Content</Form.Label>
                <Form.Select
                  name="content_id"
                  value={formData.content_id}
                  onChange={handleInputChange}
                  isInvalid={!!formErrors.content_id}
                >
                  <option value="">Select Content</option>
                  {contentOptions.map((content) => (
                    <option key={content.id} value={content.id}>
                      {content.title}
                    </option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {formErrors.content_id}
                </Form.Control.Feedback>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Chunk Order</Form.Label>
                <Form.Control
                  type="number"
                  name="chunk_order"
                  value={formData.chunk_order}
                  onChange={handleInputChange}
                  isInvalid={!!formErrors.chunk_order}
                />
                <Form.Control.Feedback type="invalid">
                  {formErrors.chunk_order}
                </Form.Control.Feedback>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Text</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={5}
                  name="text"
                  value={formData.text}
                  onChange={handleInputChange}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Image Prompt</Form.Label>
                <Form.Select
                  name="image_prompt_id"
                  value={formData.image_prompt_id}
                  onChange={handleInputChange}
                >
                  <option value="">None</option>
                  {imagePromptOptions.map((prompt) => (
                    <option key={prompt.id} value={prompt.id}>
                      {prompt.prompt_text}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Audio File</Form.Label>
                <Form.Control
                  type="text"
                  name="audio_file"
                  value={formData.audio_file}
                  onChange={handleInputChange}
                  placeholder="Path to audio file"
                />
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button
            variant={modalMode === 'delete' ? 'danger' : 'primary'}
            onClick={handleSubmit}
          >
            {modalMode === 'add' ? 'Add' : modalMode === 'edit' ? 'Save Changes' : 'Delete'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Queue Process Modal */}
      <Modal show={showQueueModal} onHide={() => setShowQueueModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Submit to Processing Queue</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>You are about to submit <strong>{selectedChunks.length}</strong> content chunk(s) to the processing queue.</p>

          <div className="mb-3">
            <h5>Selected Chunks</h5>
            <Table bordered size="sm" className="mb-4">
              <thead>
                <tr>
                  <th>Chunk ID</th>
                  <th>Content</th>
                  <th>Order</th>
                  <th>Text</th>
                </tr>
              </thead>
              <tbody>
                {currentItems
                  .filter(chunk => selectedChunks.includes(chunk.id))
                  .map(chunk => (
                    <tr key={chunk.id}>
                      <td>{chunk.id}</td>
                      <td>{getContentTitle(chunk.content_id)}</td>
                      <td>{chunk.chunk_order}</td>
                      <td>
                        {chunk.text ? (
                          chunk.text.length > 30 ? `${chunk.text.substring(0, 30)}...` : chunk.text
                        ) : 'N/A'}
                      </td>
                    </tr>
                  ))
                }
              </tbody>
            </Table>
          </div>

          <h5>Processing Steps</h5>
          <p>The following steps will be performed in order for each chunk:</p>

          <ol className="list-group list-group-numbered mb-3">
            <li className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Generate Images</div>
                <small>Create images for each content chunk based on the text</small>
              </div>
              <Badge bg="primary" pill>Step 1</Badge>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Generate Speech</div>
                <small>Convert text to speech for each chunk using AI voice synthesis</small>
              </div>
              <Badge bg="primary" pill>Step 2</Badge>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Create Slideshow</div>
                <small>Create slideshow with transitions (panning, zoom in/out) using the generated images</small>
              </div>
              <Badge bg="primary" pill>Step 3</Badge>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Create Subtitles</div>
                <small>Generate subtitles from the audio for accessibility and engagement</small>
              </div>
              <Badge bg="primary" pill>Step 4</Badge>
            </li>
            <li className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Mix Components</div>
                <small>Combine all elements (images, audio, subtitles) to create the final video</small>
              </div>
              <Badge bg="primary" pill>Step 5</Badge>
            </li>
          </ol>

          <Alert variant="info">
            <i className="fas fa-info-circle me-2"></i>
            <strong>Processing Information:</strong>
            <ul className="mb-0 mt-2">
              <li>Each chunk will go through all 5 processing steps in sequence</li>
              <li>Steps for a chunk will only start after the previous step is completed</li>
              <li>Multiple chunks can be processed in parallel</li>
              <li>The entire process may take some time depending on the number of chunks and their content</li>
              <li>Start and end times will be recorded for each process step for tracking</li>
            </ul>
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowQueueModal(false)}>
            Cancel
          </Button>
          <Button
            variant="success"
            onClick={submitToQueue}
            disabled={isSubmittingToQueue}
          >
            {isSubmittingToQueue ? (
              <>
                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                Submitting to Queue...
              </>
            ) : (
              <>Submit {selectedChunks.length} Chunk(s) to Queue</>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default ContentChunk;
