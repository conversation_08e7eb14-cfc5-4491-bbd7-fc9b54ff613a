"""
Subtitle creation tasks for the VisionFrame AI queue system.
"""

import os
import json
import logging
import subprocess
from celery import shared_task
from .db_utils import get_chunk_data, update_task_status, get_task_by_id
from .task_utils import task_completed, task_failed
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@shared_task(
    name='tasks.subtitle_tasks.create_subtitles',
    bind=True,
    max_retries=3,
    default_retry_delay=60
)
def create_subtitles(self, queue_id):
    """Create subtitles for a content chunk."""
    logger.info(f"Creating subtitles for task {queue_id}")

    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the chunk data
        chunk_id = task['chunk_id']
        chunk_data = get_chunk_data(chunk_id)

        if not chunk_data:
            error_msg = f"Chunk data not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the previous task result (speech generation)
        speech_task = get_task_by_id(queue_id - 2)  # Assuming sequential IDs

        if not speech_task or speech_task['process_step'] != 'generate_speech':
            error_msg = f"Speech generation task not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the audio path from the previous task
        speech_result_data = json.loads(speech_task['result_data'])

        if not speech_result_data or 'audio_path' not in speech_result_data:
            error_msg = f"No audio found in previous task result for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Extract the text from the chunk
        chunk_text = chunk_data['chunk']['text']

        if not chunk_text:
            error_msg = f"No text found in chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Create the subtitles
        subtitle_path = create_subtitles_from_text(
            chunk_text,
            speech_result_data['audio_path'],
            f"chunk_{chunk_id}_subtitles"
        )

        if not subtitle_path:
            error_msg = f"Failed to create subtitles for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Update the result data
        result_data = {
            'subtitle_path': subtitle_path,
            'text': chunk_text
        }

        # Mark the task as completed
        task_completed.delay(queue_id, result_data)

        return f"Created subtitles for chunk {chunk_id}"

    except Exception as e:
        logger.error(f"Error creating subtitles for task {queue_id}: {str(e)}")

        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            # If max retries exceeded, mark the task as failed
            task_failed.delay(queue_id, str(e))

        return f"Error creating subtitles for task {queue_id}: {str(e)}"

def create_subtitles_from_text(text, audio_path, subtitle_name):
    """Create subtitles from text and audio."""
    try:
        # Create the subtitles directory if it doesn't exist
        os.makedirs('subtitles', exist_ok=True)

        # Split the text into sentences
        sentences = split_into_sentences(text)

        if not sentences:
            logger.error("No sentences found in text")
            return None

        # Get the audio duration using FFmpeg
        audio_duration = get_audio_duration(audio_path)

        if not audio_duration:
            logger.error(f"Could not determine duration of audio file {audio_path}")
            return None

        # Create a simple SRT file with evenly distributed timestamps
        subtitle_path = f"subtitles/{subtitle_name}.srt"

        with open(subtitle_path, 'w') as f:
            for i, sentence in enumerate(sentences):
                start_time = i * (audio_duration / len(sentences))
                end_time = (i + 1) * (audio_duration / len(sentences))

                # Format timestamps as HH:MM:SS,mmm
                start_formatted = format_timestamp(start_time)
                end_formatted = format_timestamp(end_time)

                # Write the subtitle entry
                f.write(f"{i+1}\n")
                f.write(f"{start_formatted} --> {end_formatted}\n")
                f.write(f"{sentence}\n\n")

        logger.info(f"Subtitles created at {subtitle_path}")

        return subtitle_path

    except Exception as e:
        logger.error(f"Error creating subtitles from text: {str(e)}")
        return None

def split_into_sentences(text):
    """Split text into sentences."""
    # Simple sentence splitting by punctuation
    import re
    sentences = re.split(r'(?<=[.!?])\s+', text)
    return [s.strip() for s in sentences if s.strip()]

def get_audio_duration(audio_path):
    """Get the duration of an audio file in seconds."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1',
            audio_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        duration = float(result.stdout.strip())

        return duration

    except Exception as e:
        logger.error(f"Error getting audio duration: {str(e)}")
        return None

def format_timestamp(seconds):
    """Format seconds as HH:MM:SS,mmm for SRT files."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    milliseconds = int((seconds - int(seconds)) * 1000)

    return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"
