/**
 * Grok API Service
 * This service handles communication with the Grok API for content generation
 */

const API_BASE_URL = process.env.REACT_APP_GROK_API_URL || 'http://localhost:5001';

/**
 * Generate content prompt using Grok AI
 * @param {Object} params - Parameters for content generation
 * @param {string} params.title - Title of the content
 * @param {string} params.category - Category of the content
 * @param {string} params.description - Brief description or context
 * @returns {Promise<Object>} - Generated content prompt
 */
export const generateContentPrompt = async (params) => {
  try {
    console.log(`Calling Grok API at ${API_BASE_URL}/api/grok/generate-prompt with params:`, params);

    const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    console.log('Received response with status:', response.status);

    let responseData;
    try {
      responseData = await response.json();
      console.log('Response data:', responseData);
    } catch (jsonError) {
      console.error('Error parsing JSON response:', jsonError);
      throw new Error('Invalid response from server');
    }

    if (!response.ok) {
      throw new Error(responseData.error || 'Failed to generate content with Grok');
    }

    return responseData;
  } catch (error) {
    console.error('Error generating content with Grok:', error);
    throw error;
  }
};

/**
 * Check if Grok API is configured and available
 * @returns {Promise<boolean>} - True if Grok API is available
 */
export const checkGrokAvailability = async () => {
  try {
    console.log(`Checking Grok API availability at ${API_BASE_URL}/api/grok/status`);

    const response = await fetch(`${API_BASE_URL}/api/grok/status`);
    console.log('Received status response with status:', response.status);

    if (!response.ok) {
      console.warn('Grok API status check failed with status:', response.status);
      return false;
    }

    const data = await response.json();
    console.log('Grok availability data:', data);

    return data.available === true;
  } catch (error) {
    console.error('Error checking Grok availability:', error);
    return false;
  }
};
