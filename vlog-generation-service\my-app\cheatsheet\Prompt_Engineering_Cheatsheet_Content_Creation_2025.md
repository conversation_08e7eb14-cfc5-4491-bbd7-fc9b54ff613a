# 📚 Prompt Engineering Cheatsheet for Content Writing, Blogging & Vlogging (2025 Pro Edition)

Maximize your content creation workflow using this AI prompt engineering cheatsheet—custom-crafted for writers, vloggers, bloggers, and content strategists. From short-form video scripts to long-form blog planning, this guide helps you communicate effectively with AI tools like ChatGPT to get consistent, accurate, and engaging outputs.

---

## 🚀 Table of Contents

- [🔍 Purpose](#-purpose)
- [🎯 Content Goal Framework (C.A.T.S.)](#-content-goal-framework-cats)
- [🧩 Universal Prompt Template](#-universal-prompt-template)
- [📜 Scriptwriting Prompt Structures](#-scriptwriting-prompt-structures)
- [🗓 Content Planning Prompts](#-content-planning-prompts)
- [🎥 Vlog-Specific Prompt Examples](#-vlog-specific-prompt-examples)
- [✍️ Blogging Prompt Enhancers](#️-blogging-prompt-enhancers)
- [📣 Hooks & CTAs](#-hooks--ctas)
- [🧠 Refinement Loops](#-refinement-loops)
- [🛠 Bonus Prompt Starters](#-bonus-prompt-starters)

---

## 🔍 Purpose

This cheatsheet is designed to help content creators harness the full potential of AI tools for content planning, scripting, storytelling, and optimization. Whether you’re creating TikTok reels, YouTube vlogs, or SEO-friendly blog posts, this guide empowers you to prompt smarter and produce faster.

---

## 🎯 Content Goal Framework (C.A.T.S.)

> **C.A.T.S. = Content Type + Audience + Tone + Style**

### Example:
- **Content**: Short-form vlog script  
- **Audience**: Millennials struggling with burnout  
- **Tone**: Empathetic and motivational  
- **Style**: Conversational, with storytelling  

---

## 🧩 Universal Prompt Template

```
Act as a professional [role].  
Create a [content type] about [topic] for [audience].  
Keep the tone [tone], and structure it with [format/rules].
```

### Example:
> Act as a mental health coach.  
> Create a 1-minute vlog script about daily habits to reduce stress for college students.  
> Keep it calming and warm, with a 3-point structure.

---

## 📜 Scriptwriting Prompt Structures

### 🎬 Short-Form Script
```
Write a [duration] script with:
- Hook (0–3s)
- 2–3 Value Points
- Clear CTA
```

### 🎥 Long-Form Script
```
Structure:
- Hook
- Introduction
- 3–5 Key Points
- Summary/Takeaway
- CTA
```

---

## 🗓 Content Planning Prompts

### Monthly/Weekly Calendar:
```
Create a [monthly/weekly] content calendar on [topic].
Include titles, formats (reel, blog, script), and goals.
```

### Persona-Specific Ideas:
```
Generate content ideas for [persona] interested in [topic].
Include pain points, desires, and keywords.
```

---

## 🎥 Vlog-Specific Prompt Examples

### Storytelling Prompt
```
Write a heartfelt 90-second vlog script about [experience/topic].
Make the audience feel [emotion].
Include personal reflection and a lesson.
```

### Explainer Prompt (F.I.R.E. Method)
```
F = Fast hook  
I = Insight  
R = Real example  
E = End with CTA
```

---

## ✍️ Blogging Prompt Enhancers

| Goal            | Prompt Example |
|------------------|----------------|
| SEO Optimized     | “Write a blog about [topic] targeting '[keyword]'.” |
| Personal Voice    | “Rewrite this blog in a casual, friend-like tone.” |
| Data-Based        | “Summarize latest studies about [topic].” |
| Step-by-Step Guide| “Create a how-to blog with clear steps for [task].” |
| Listicle Format   | “Write a Top 7 listicle about [niche topic].” |

---

## 📣 Hooks & CTAs

### Hook Templates:
- “What no one tells you about [topic]...”
- “If you’re [pain point], this is for you…”
- “Here’s how I [achieved X] without [obstacle]”

### CTA Templates:
- “Try this today and comment how it works.”
- “Follow for more tips on [goal].”
- “Tag someone who needs this.”

---

## 🧠 Refinement Loops

Improve your AI output in steps:
1. **First Draft** – General script or outline  
2. **Improve Hook** – Make it punchy and emotional  
3. **Enhance Structure** – Add sections or transitions  
4. **Tone Shift** – Adjust for humor, formality, etc.  
5. **Final CTA** – Add a compelling close or action step  

---

## 🛠 Bonus Prompt Starters

- **Idea Generator**  
  > “Give me 10 vlog ideas on [topic] with hooks and outlines.”

- **Viral Script Format**  
  > “Create a TikTok script in the style of viral storytelling.”

- **Visual Script Prompt**  
  > “Pair this script with footage of [location/action]. Add narrator lines.”

- **Before & After Story**  
  > “Write a transformation script showing results of [habit/lesson].”

---

## 📦 License

This project is licensed under the MIT License — use freely, adapt widely.

---

## 🙌 Credits

Created by **PromptGPT with 10x iteration logic**  
Optimized for creators, storytellers, marketers, and growth hackers in the age of AI.
