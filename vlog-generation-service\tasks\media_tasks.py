"""
Media generation tasks for the VisionFrame AI queue system.
This module integrates the media generation pipeline with the task queue system.
"""

import os
import sys
import gc
import logging
import importlib
import traceback
import asyncio
from celery import shared_task
from .db_utils import get_task_by_id, update_task_status
from .task_utils import task_completed, task_failed

# Add the parent directory to sys.path to import media_generation_service
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import functions from media_generation_service
from media_generation_service import (
    create_db_pool,
    log_event,
    process_content_chunk,
    update_chunk_status,
    PIPELINE_STEPS
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@shared_task(
    name='tasks.media_tasks.generate_media',
    bind=True,
    max_retries=2,
    default_retry_delay=60
)
def generate_media(self, queue_id):
    """
    Generate media for a content chunk using the media generation pipeline.
    
    Args:
        queue_id (int): The ID of the queue task
    """
    logger.info(f"Starting media generation for task {queue_id}")
    
    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            return
        
        # Get the chunk ID
        chunk_id = task['chunk_id']
        if not chunk_id:
            error_msg = f"No chunk ID found for task {queue_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return
        
        # Run the media generation pipeline for this chunk
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create a database pool
            pool = loop.run_until_complete(create_db_pool())
            
            try:
                # Log the start of processing
                loop.run_until_complete(
                    log_event(
                        pool,
                        "MEDIA_GENERATION_START",
                        f"Starting media generation for chunk ID {chunk_id}"
                    )
                )
                
                # Update chunk status to processing
                loop.run_until_complete(
                    update_chunk_status(pool, chunk_id, "processing")
                )
                
                # Process the chunk
                success = loop.run_until_complete(
                    process_content_chunk(pool, chunk_id)
                )
                
                # Update chunk status based on result
                if success:
                    loop.run_until_complete(
                        update_chunk_status(pool, chunk_id, "completed")
                    )
                    
                    # Mark the task as completed
                    result_data = {
                        "chunk_id": chunk_id,
                        "status": "completed",
                        "message": "Media generation completed successfully"
                    }
                    task_completed.delay(queue_id, result_data)
                    
                    logger.info(f"Media generation completed for chunk ID {chunk_id}")
                else:
                    error_msg = f"Media generation failed for chunk ID {chunk_id}"
                    loop.run_until_complete(
                        update_chunk_status(pool, chunk_id, "failed", error_msg)
                    )
                    
                    # Mark the task as failed
                    task_failed.delay(queue_id, error_msg)
                    
                    logger.error(error_msg)
            
            except Exception as e:
                error_msg = f"Error processing chunk ID {chunk_id}: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                
                # Update chunk status to failed
                try:
                    loop.run_until_complete(
                        update_chunk_status(pool, chunk_id, "failed", str(e))
                    )
                except Exception:
                    pass
                
                # Mark the task as failed
                task_failed.delay(queue_id, error_msg)
                
                # Retry the task if it's not the last retry
                try:
                    self.retry(exc=e)
                except self.MaxRetriesExceededError:
                    pass
            
            finally:
                # Close the database pool
                pool.close()
                loop.run_until_complete(pool.wait_closed())
        
        finally:
            # Clean up the event loop
            loop.close()
            
            # Force garbage collection
            gc.collect()
    
    except Exception as e:
        error_msg = f"Critical error in media generation task {queue_id}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # Mark the task as failed
        task_failed.delay(queue_id, error_msg)
        
        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            pass

@shared_task(
    name='tasks.media_tasks.run_pipeline_step',
    bind=True,
    max_retries=2,
    default_retry_delay=60
)
def run_pipeline_step(self, queue_id, step_name):
    """
    Run a specific step of the media generation pipeline.
    
    Args:
        queue_id (int): The ID of the queue task
        step_name (str): The name of the pipeline step to run
    """
    logger.info(f"Running pipeline step '{step_name}' for task {queue_id}")
    
    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            return
        
        # Get the chunk ID
        chunk_id = task['chunk_id']
        if not chunk_id:
            error_msg = f"No chunk ID found for task {queue_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return
        
        # Find the requested step
        step = next((s for s in PIPELINE_STEPS if s['name'].lower() == step_name.lower()), None)
        if not step:
            error_msg = f"Step '{step_name}' not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return
        
        # Run the pipeline step
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create a database pool
            pool = loop.run_until_complete(create_db_pool())
            
            try:
                # Log the start of processing
                loop.run_until_complete(
                    log_event(
                        pool,
                        "PIPELINE_STEP_START",
                        f"Starting step '{step_name}' for chunk ID {chunk_id}"
                    )
                )
                
                # Import the module
                module = importlib.import_module(step['module'])
                
                # Get the main function
                if 'main_function' in step and step['async']:
                    main_func = getattr(module, step['main_function'])
                    
                    # Run the main function
                    if asyncio.iscoroutinefunction(main_func):
                        loop.run_until_complete(main_func())
                    else:
                        # If it's not a coroutine function but returns a coroutine
                        result = main_func()
                        if asyncio.iscoroutine(result):
                            loop.run_until_complete(result)
                else:
                    # For synchronous modules, just import them
                    pass
                
                # Log the completion
                loop.run_until_complete(
                    log_event(
                        pool,
                        "PIPELINE_STEP_COMPLETE",
                        f"Completed step '{step_name}' for chunk ID {chunk_id}"
                    )
                )
                
                # Mark the task as completed
                result_data = {
                    "chunk_id": chunk_id,
                    "step": step_name,
                    "status": "completed",
                    "message": f"Pipeline step '{step_name}' completed successfully"
                }
                task_completed.delay(queue_id, result_data)
                
                logger.info(f"Pipeline step '{step_name}' completed for chunk ID {chunk_id}")
                
                # Clean up to release memory
                del module
                gc.collect()
                
            except Exception as e:
                error_msg = f"Error running step '{step_name}' for chunk ID {chunk_id}: {str(e)}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                
                # Log the error
                loop.run_until_complete(
                    log_event(
                        pool,
                        "PIPELINE_STEP_ERROR",
                        error_msg,
                        "ERROR",
                        e
                    )
                )
                
                # Mark the task as failed
                task_failed.delay(queue_id, error_msg)
                
                # Retry the task if it's not the last retry
                try:
                    self.retry(exc=e)
                except self.MaxRetriesExceededError:
                    pass
            
            finally:
                # Close the database pool
                pool.close()
                loop.run_until_complete(pool.wait_closed())
        
        finally:
            # Clean up the event loop
            loop.close()
            
            # Force garbage collection
            gc.collect()
    
    except Exception as e:
        error_msg = f"Critical error in pipeline step task {queue_id}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        # Mark the task as failed
        task_failed.delay(queue_id, error_msg)
        
        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            pass
