import subprocess
import sys
import os

# Get the Python executable from the current virtual environment
python_executable = sys.executable

# List of Python scripts to execute
scripts = ['_5content_generate_videoimage_speech_moviepy.py','_6content_generate_video_subtitle.py']

for script in scripts:
    try:
        # Execute the script using the virtual environment's Python
        result = subprocess.run([python_executable, script], check=True)
        print(f"{script} executed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"An error occurred while executing {script}: {e}")
        break
    except Exception as e:
        print(f"Unexpected error while executing {script}: {e}")
        break
