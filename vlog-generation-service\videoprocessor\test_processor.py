#!/usr/bin/env python3
"""
Test script for VisionFrame AI Video Processor
Verifies installation and basic functionality
"""

import sys
import os
import json
import tempfile
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import moviepy
        print("✅ MoviePy imported successfully")
    except ImportError as e:
        print(f"❌ MoviePy import failed: {e}")
        return False
    
    try:
        import PIL
        print("✅ Pillow imported successfully")
    except ImportError as e:
        print(f"❌ Pillow import failed: {e}")
        return False
    
    try:
        import numpy
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests imported successfully")
    except ImportError as e:
        print(f"❌ Requests import failed: {e}")
        return False
    
    return True

def test_local_imports():
    """Test if local modules can be imported"""
    print("\n🔍 Testing local imports...")
    
    try:
        from video_processor import VideoProcessor
        print("✅ VideoProcessor imported successfully")
    except ImportError as e:
        print(f"❌ VideoProcessor import failed: {e}")
        return False
    
    try:
        from text_processor import TextProcessor
        print("✅ TextProcessor imported successfully")
    except ImportError as e:
        print(f"❌ TextProcessor import failed: {e}")
        return False
    
    try:
        from audio_processor import AudioProcessor
        print("✅ AudioProcessor imported successfully")
    except ImportError as e:
        print(f"❌ AudioProcessor import failed: {e}")
        return False
    
    return True

def test_ffmpeg():
    """Test if FFmpeg is available"""
    print("\n🔍 Testing FFmpeg availability...")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg is available")
            return True
        else:
            print("❌ FFmpeg command failed")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in PATH")
        print("   Please install FFmpeg:")
        print("   - Windows: Download from https://ffmpeg.org/")
        print("   - macOS: brew install ffmpeg")
        print("   - Linux: sudo apt install ffmpeg")
        return False
    except Exception as e:
        print(f"❌ FFmpeg test failed: {e}")
        return False

def create_test_template():
    """Create a minimal test template"""
    template = {
        "output_format": "mp4",
        "width": 720,
        "height": 1280,
        "elements": [
            {
                "id": "test-scene-1",
                "name": "Test-Scene",
                "type": "composition",
                "track": 1,
                "time": 0,
                "elements": [
                    {
                        "id": "test-image-1",
                        "name": "Test-Image",
                        "type": "image",
                        "track": 1,
                        "time": 0,
                        "source": "placeholder",
                        "color_overlay": "rgba(0,0,0,0.15)",
                        "animations": [
                            {
                                "type": "pan",
                                "start_scale": "120%",
                                "end_scale": "100%"
                            }
                        ]
                    },
                    {
                        "id": "test-text-1",
                        "name": "Test-Text",
                        "type": "text",
                        "track": 2,
                        "time": 0,
                        "font_family": "Arial",
                        "font_size": "8 vmin",
                        "fill_color": "#ffffff",
                        "stroke_color": "#333333",
                        "x_alignment": "50%",
                        "y_alignment": "50%",
                        "transcript_effect": "highlight"
                    }
                ]
            }
        ]
    }
    
    return template

def test_basic_processing():
    """Test basic video processing functionality"""
    print("\n🔍 Testing basic video processing...")
    
    try:
        from video_processor import VideoProcessor
        
        # Create test template
        template = create_test_template()
        
        # Create temporary output directory
        with tempfile.TemporaryDirectory() as temp_dir:
            processor = VideoProcessor(temp_dir)
            
            # Test template processing (should create a short video)
            output_path = processor.process_template(template, "test_output.mp4")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"✅ Basic processing successful")
                print(f"   Output: {output_path}")
                print(f"   Size: {file_size} bytes")
                return True
            else:
                print("❌ Output file was not created")
                return False
                
    except Exception as e:
        print(f"❌ Basic processing failed: {e}")
        return False

def test_text_processing():
    """Test text processing functionality"""
    print("\n🔍 Testing text processing...")
    
    try:
        from text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # Test text image creation
        text_element = {
            "font_family": "Arial",
            "font_size": "24px",
            "fill_color": "#ffffff",
            "stroke_color": "#000000",
            "x_alignment": "50%",
            "y_alignment": "50%"
        }
        
        # This should not raise an exception
        text_image = processor.create_word_image(
            ["Hello", "World"], 0, 24, "#ffffff", "#000000", "#ff0000",
            720, 1280, text_element
        )
        
        if text_image is not None and text_image.shape[0] > 0:
            print("✅ Text processing successful")
            return True
        else:
            print("❌ Text processing returned invalid result")
            return False
            
    except Exception as e:
        print(f"❌ Text processing failed: {e}")
        return False

def test_audio_processing():
    """Test audio processing functionality"""
    print("\n🔍 Testing audio processing...")
    
    try:
        from audio_processor import AudioProcessor
        
        processor = AudioProcessor()
        
        # Test audio element processing (without actual audio file)
        audio_element = {
            "name": "Test Audio",
            "source": "",
            "provider": "test"
        }
        
        # This should return None for audio clip but valid duration
        audio_clip, duration = processor.process_audio_element(audio_element, "Test transcript")
        
        if duration > 0:
            print("✅ Audio processing successful")
            print(f"   Default duration: {duration} seconds")
            return True
        else:
            print("❌ Audio processing returned invalid duration")
            return False
            
    except Exception as e:
        print(f"❌ Audio processing failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🚀 VisionFrame AI Video Processor - Installation Test\n")
    
    tests = [
        ("Import Dependencies", test_imports),
        ("Import Local Modules", test_local_imports),
        ("FFmpeg Availability", test_ffmpeg),
        ("Basic Video Processing", test_basic_processing),
        ("Text Processing", test_text_processing),
        ("Audio Processing", test_audio_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The video processor is ready to use.")
        print("\nNext steps:")
        print("1. Run: python main.py sample_template.json")
        print("2. Or: python main.py --sample")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Install FFmpeg for your system")
        print("3. Check Python version (3.8+ required)")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
