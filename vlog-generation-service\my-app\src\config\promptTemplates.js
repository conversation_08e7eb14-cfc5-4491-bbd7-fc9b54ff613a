/**
 * Configuration file for content prompt templates and output formats
 */

// Standard output format to be used for all content prompts
export const STANDARD_OUTPUT_FORMAT = "standard";

// Output format templates
export const OUTPUT_FORMAT_TEMPLATES = {
  standard: {
    name: "Standard Format",
    description: "The standard output format for all content prompts",
    template: `Your content should follow this structure:

1. Introduction (1-2 sentences)
2. Main points (3-5 bullet points)
3. Conclusion (1-2 sentences)
4. Call to action

Total length should be between 300-500 words.`
  },

  // Additional formats can be added here if needed in the future
  short: {
    name: "Short Format",
    description: "A shorter format for quick content",
    template: `Brief content with:
- Introduction (1 sentence)
- 2-3 key points
- Brief conclusion
- Simple call to action

Total length: 150-250 words.`
  },

  medium: {
    name: "Medium Format",
    description: "A medium-length format for standard content",
    template: `Medium-length content with:
- Introduction (2-3 sentences)
- 4-6 key points with brief explanations
- Conclusion with summary
- Compelling call to action

Total length: 400-600 words.`
  },

  long: {
    name: "Long Format",
    description: "A comprehensive format for detailed content",
    template: `Comprehensive content with:
- Detailed introduction (3-4 sentences)
- 6-8 key points with thorough explanations
- Examples or case studies
- In-depth conclusion
- Multiple call to action options

Total length: 800-1200 words.`
  }
};

// Video format options
export const VIDEO_FORMAT_OPTIONS = [
  { value: "1min", label: "1 Minute" },
  { value: "3min", label: "3 Minutes" },
  { value: "5min", label: "5 Minutes" },
  { value: "custom", label: "Custom" }
];

// Tone options
export const TONE_OPTIONS = [
  { value: "friendly", label: "Friendly" },
  { value: "empathetic", label: "Empathetic" },
  { value: "funny", label: "Funny" },
  { value: "sarcastic", label: "Sarcastic" },
  { value: "motivational", label: "Motivational" },
  { value: "professional", label: "Professional" }
];

// Goal options
export const GOAL_OPTIONS = [
  { value: "educate", label: "Educate" },
  { value: "entertain", label: "Entertain" },
  { value: "inspire", label: "Inspire" },
  { value: "solve_problem", label: "Solve a Problem" },
  { value: "promote", label: "Promote" }
];

// Target audience options
export const TARGET_AUDIENCE_OPTIONS = [
  { value: "general", label: "General Audience" },
  { value: "beginners", label: "Beginners" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
  { value: "professionals", label: "Professionals" },
  { value: "students", label: "Students" },
  { value: "parents", label: "Parents" },
  { value: "young_adults", label: "Young Adults" },
  { value: "seniors", label: "Seniors" }
];

// Format options
export const FORMAT_OPTIONS = [
  { value: "hook_points_cta", label: "Hook + Key Points + CTA" },
  { value: "question_answer", label: "Question & Answer" },
  { value: "story_lesson", label: "Story with Lesson" },
  { value: "problem_solution", label: "Problem & Solution" },
  { value: "tips_tricks", label: "Tips & Tricks" },
  { value: "tutorial", label: "Step-by-Step Tutorial" }
];

// Default values for new content prompts
export const DEFAULT_CONTENT_PROMPT = {
  // Claude format fields only
  topic: "",
  title: "", // Keep for backward compatibility
  category_id: "",
  content_type: "blog_post",
  purpose: "inform",
  audience: "general",
  tone: "conversational",
  style: "descriptive",
  word_count: "500",
  persona: "expert",
  content_prompt: "", // Added content_prompt field
  call_to_action: "",
  output_format: STANDARD_OUTPUT_FORMAT
};
