/* Custom styles for the video editor */

/* Preview container styling */
#previewContainer {
    background: linear-gradient(45deg, #1a1a1a 25%, transparent 25%),
                linear-gradient(-45deg, #1a1a1a 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #1a1a1a 75%),
                linear-gradient(-45deg, transparent 75%, #1a1a1a 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Element styling in preview */
.preview-element {
    position: absolute;
    cursor: move;
    user-select: none;
    border: 2px solid transparent;
    transition: border-color 0.2s;
}

.preview-element:hover {
    border-color: #3b82f6;
}

.preview-element.selected {
    border-color: #ef4444;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.preview-element.dragging {
    z-index: 1000;
    opacity: 0.8;
    transform: scale(1.05);
    border-color: #10b981 !important;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.preview-element.text-element {
    font-size: 24px;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    min-width: 100px;
    text-align: center;
}

.preview-element.image-element {
    max-width: 300px;
    max-height: 200px;
    object-fit: contain;
}

.preview-element.video-element {
    max-width: 400px;
    max-height: 300px;
}

.preview-element.audio-element {
    width: 200px;
    height: 60px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

/* Timeline styling */
#timeline {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    background: #374151;
    border-radius: 4px;
    outline: none;
}

#timeline::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
}

#timeline::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Element list styling */
.element-item {
    background: #374151;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid transparent;
}

.element-item:hover {
    background: #4b5563;
    border-color: #3b82f6;
}

.element-item.selected {
    border-color: #ef4444;
    background: #4b5563;
}

.element-item .element-type {
    font-size: 12px;
    color: #9ca3af;
    text-transform: uppercase;
    font-weight: 600;
}

.element-item .element-content {
    font-size: 14px;
    margin-top: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Properties panel styling */
.property-group {
    background: #374151;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
}

.property-group h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #f3f4f6;
}

.property-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.property-row label {
    flex: 1;
    font-size: 12px;
    color: #d1d5db;
}

.property-row input {
    flex: 2;
    padding: 6px 8px;
    background: #1f2937;
    border: 1px solid #4b5563;
    border-radius: 4px;
    color: white;
    font-size: 12px;
}

.property-row input:focus {
    outline: none;
    border-color: #3b82f6;
}

/* Animation keyframes styling */
.keyframe-item {
    background: #1f2937;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #4b5563;
}

.keyframe-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.keyframe-controls input {
    flex: 1;
    padding: 4px 6px;
    background: #374151;
    border: 1px solid #6b7280;
    border-radius: 3px;
    color: white;
    font-size: 11px;
}

.delete-keyframe {
    background: #dc2626;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
}

.delete-keyframe:hover {
    background: #b91c1c;
}

.add-keyframe {
    background: #059669;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    margin-top: 8px;
}

.add-keyframe:hover {
    background: #047857;
}

/* Modal styling improvements */
.modal-overlay {
    backdrop-filter: blur(4px);
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .w-80 {
        width: 250px;
    }

    .preview-element.text-element {
        font-size: 18px;
    }

    .preview-element.image-element {
        max-width: 200px;
        max-height: 150px;
    }

    .preview-element.video-element {
        max-width: 250px;
        max-height: 200px;
    }
}

/* Animation for smooth transitions */
.preview-element {
    transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

/* Loading state */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

/* Success/Error states */
.success {
    border-color: #10b981 !important;
}

.error {
    border-color: #ef4444 !important;
}

/* Timeline View Styles */
.timeline-track {
    height: 40px;
    border-bottom: 1px solid #4b5563;
    position: relative;
    background: #1f2937;
}

.timeline-track:nth-child(even) {
    background: #111827;
}

.timeline-element {
    position: absolute;
    height: 30px;
    top: 5px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-size: 12px;
    color: white;
    border: 2px solid transparent;
    transition: all 0.2s;
    min-width: 20px;
}

.timeline-element:hover {
    border-color: #3b82f6;
    z-index: 10;
}

.timeline-element.selected {
    border-color: #ef4444;
    z-index: 20;
}

.timeline-element.text-type {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.timeline-element.image-type {
    background: linear-gradient(135deg, #10b981, #047857);
}

.timeline-element.video-type {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.timeline-element.audio-type {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.timeline-element .resize-handle {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 8px;
    cursor: ew-resize;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: opacity 0.2s;
}

.timeline-element:hover .resize-handle {
    opacity: 1;
}

.timeline-element .resize-handle.left {
    left: 0;
    border-radius: 4px 0 0 4px;
}

.timeline-element .resize-handle.right {
    right: 0;
    border-radius: 0 4px 4px 0;
}

.timeline-ruler-mark {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #6b7280;
}

.timeline-ruler-mark.major {
    background: #9ca3af;
}

.timeline-ruler-label {
    position: absolute;
    top: 2px;
    font-size: 10px;
    color: #d1d5db;
    transform: translateX(-50%);
}

.track-label {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    border-bottom: 1px solid #4b5563;
    font-size: 12px;
    background: #374151;
}

.track-label:nth-child(even) {
    background: #4b5563;
}

/* File upload styling */
.file-upload-area {
    border: 2px dashed #6b7280;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.file-upload-area.dragover {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

/* Drag and drop preview */
.drag-preview {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    opacity: 0.8;
    transform: scale(0.8);
}
