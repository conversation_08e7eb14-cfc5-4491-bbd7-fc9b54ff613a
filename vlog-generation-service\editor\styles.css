/* VisionFrame AI Professional Video Editor - Creatomate Style */

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

/* Top Toolbar Styles */
.dot {
    transition: transform 0.3s ease;
}

#animateToggle:checked + div + .dot {
    transform: translateX(100%);
}

/* Tab Styles */
[data-tab] {
    transition: all 0.2s ease;
}

[data-tab].active {
    color: #3b82f6 !important;
    border-bottom-color: #3b82f6 !important;
}

/* Canvas Styles */
#canvas {
    transition: all 0.3s ease;
    border-radius: 4px;
    overflow: hidden;
}

#canvasGrid {
    background-image:
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Element Styles */
.canvas-element {
    position: absolute;
    cursor: move;
    user-select: none;
    transition: all 0.1s ease;
}

.canvas-element:hover {
    z-index: 10;
}

.canvas-element.selected {
    z-index: 20;
}

.canvas-element.text-element {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.canvas-element.shape-element {
    display: flex;
    align-items: center;
    justify-content: center;
}

.canvas-element.image-element img,
.canvas-element.video-element video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: inherit;
}

.canvas-element.audio-element {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

/* Selection Box */
#selectionBox {
    pointer-events: none;
    border: 2px solid #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

#selectionBox > div {
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border: 1px solid white;
    border-radius: 2px;
    cursor: nw-resize;
}

#selectionBox > div:nth-child(2) {
    cursor: ne-resize;
}

#selectionBox > div:nth-child(3) {
    cursor: sw-resize;
}

#selectionBox > div:nth-child(4) {
    cursor: se-resize;
}

/* Layer Navigator */
.layer-item {
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
}

.layer-item:hover {
    background: rgba(75, 85, 99, 0.5);
}

.layer-item.selected {
    background: rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.layer-item .layer-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.layer-item .layer-name {
    flex: 1;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.layer-item .layer-visibility {
    width: 16px;
    height: 16px;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s;
}

.layer-item .layer-visibility:hover {
    opacity: 1;
}

/* Properties Panel */
.property-section {
    border: 1px solid #374151;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 12px;
}

.property-section-title {
    background: #374151;
    padding: 8px 12px;
    font-size: 13px;
    font-weight: 600;
    color: #f3f4f6;
    border-bottom: 1px solid #4b5563;
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.property-section-title:hover {
    background: #4b5563;
}

.property-section-content {
    padding: 12px;
    background: #1f2937;
}

.property-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.property-row:last-child {
    margin-bottom: 0;
}

.property-row label {
    flex: 1;
    font-size: 12px;
    color: #d1d5db;
    font-weight: 500;
}

.property-input {
    flex: 1.5;
    padding: 6px 8px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    transition: border-color 0.2s;
}

.property-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.property-slider {
    flex: 1.2;
    margin-right: 8px;
}

.property-value {
    flex: 0.3;
    font-size: 11px;
    color: #9ca3af;
    text-align: right;
}

.property-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
}

.property-row-group {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.property-row-group .property-row {
    margin-bottom: 0;
    flex: 1;
}

/* Timeline Styles */
.timeline-track {
    height: 40px;
    border-bottom: 1px solid #4b5563;
    position: relative;
    background: #1f2937;
}

.timeline-track:nth-child(even) {
    background: #111827;
}

.timeline-element {
    position: absolute;
    height: 30px;
    top: 5px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-size: 11px;
    color: white;
    border: 2px solid transparent;
    transition: all 0.2s;
    min-width: 20px;
    overflow: hidden;
    white-space: nowrap;
}

.timeline-element:hover {
    border-color: rgba(255, 255, 255, 0.3);
    z-index: 10;
}

.timeline-element.selected {
    border-color: #3b82f6;
    z-index: 20;
    box-shadow: 0 0 0 1px #3b82f6;
}

.timeline-element.text-type {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.timeline-element.image-type {
    background: linear-gradient(135deg, #10b981, #047857);
}

.timeline-element.video-type {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.timeline-element.audio-type {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.timeline-element.shape-type {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.timeline-element .resize-handle {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 8px;
    cursor: ew-resize;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: opacity 0.2s;
}

.timeline-element:hover .resize-handle {
    opacity: 1;
}

.timeline-element .resize-handle.left {
    left: 0;
    border-radius: 4px 0 0 4px;
}

.timeline-element .resize-handle.right {
    right: 0;
    border-radius: 0 4px 4px 0;
}

.timeline-ruler-mark {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #6b7280;
}

.timeline-ruler-mark.major {
    background: #9ca3af;
    height: 100%;
}

.timeline-ruler-mark.minor {
    height: 50%;
    top: 50%;
}

.timeline-ruler-label {
    position: absolute;
    top: 2px;
    font-size: 10px;
    color: #d1d5db;
    transform: translateX(-50%);
    pointer-events: none;
}

.timeline-track-label {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid #4b5563;
    font-size: 12px;
    background: #374151;
    color: #d1d5db;
}

.timeline-track-label:nth-child(even) {
    background: #4b5563;
}

/* Media Library */
.media-item {
    aspect-ratio: 1;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    position: relative;
}

.media-item:hover {
    border-color: #3b82f6;
    transform: scale(1.02);
}

.media-item img,
.media-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-item .media-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.media-item:hover .media-overlay {
    opacity: 1;
}

/* Drop Zone */
#dropZone.dragover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* Animation Presets */
.animation-preset {
    transition: all 0.2s ease;
}

.animation-preset:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .w-80 {
        width: 250px;
    }

    .property-input {
        font-size: 11px;
        padding: 4px 6px;
    }

    .timeline-element {
        font-size: 10px;
        padding: 0 6px;
    }
}
