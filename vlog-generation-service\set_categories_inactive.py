import os
import sys
import logging
import requests
import json
import pymysql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

def set_categories_inactive(category_ids):
    """Set specified categories as inactive"""
    conn = None
    cursor = None
    try:
        logger.info(f"Setting categories {category_ids} as inactive")
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Update categories to inactive
        for category_id in category_ids:
            cursor.execute(
                "UPDATE category SET status = 'inactive' WHERE id = %s",
                (category_id,)
            )
            logger.info(f"Set category ID {category_id} to inactive")
        
        conn.commit()
        logger.info(f"Successfully set {len(category_ids)} categories to inactive")
        
    except Exception as e:
        logger.error(f"Error setting categories inactive: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def main():
    """Set some categories as inactive for testing"""
    # Set these category IDs as inactive
    # You can modify this list to include the IDs you want to set as inactive
    category_ids_to_deactivate = [2, 4, 6, 8, 10]
    
    set_categories_inactive(category_ids_to_deactivate)
    
    logger.info("Done setting categories inactive")

if __name__ == "__main__":
    main()
