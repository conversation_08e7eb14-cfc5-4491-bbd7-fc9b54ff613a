import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Nav, Tab, Alert } from 'react-bootstrap';
import './ContentManagement.css';
import ContentDetails from './ContentDetails';
import ContentChunk from './ContentChunk';
import ClaudePromptingContent from './ClaudePromptingContent';
import {
  ContentPaste as ContentIcon,
  VideoLibrary as VideoLibraryIcon,
  Storage as StorageIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';

const ContentManagement = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  // Determine active tab based on URL
  const getActiveTab = () => {
    console.log('Current pathname:', location.pathname);
    if (location.pathname.includes('/dashboard/content-management/details') ||
        location.pathname.includes('/content-management/details')) {
      console.log('Setting active tab to content-details');
      return 'content-details';
    } else if (location.pathname.includes('/dashboard/content-management/chunks') ||
               location.pathname.includes('/content-management/chunks')) {
      console.log('Setting active tab to content-chunks');
      return 'content-chunks';
    } else if (location.pathname.includes('/dashboard/content-management/claude-prompting') ||
               location.pathname.includes('/content-management/claude-prompting')) {
      console.log('Setting active tab to claude-prompting');
      return 'claude-prompting';
    }
    // Default to claude-prompting instead of content-prompts
    console.log('Setting active tab to claude-prompting (default)');
    return 'claude-prompting';
  };

  const [activeKey, setActiveKey] = useState(getActiveTab());

  // Update active key when location changes
  useEffect(() => {
    setActiveKey(getActiveTab());
  }, [location.pathname]);

  // Handle tab change
  const handleTabChange = (key) => {
    console.log('Tab change to:', key);
    setActiveKey(key);
    if (key === 'content-details') {
      navigate('/dashboard/content-management/details');
    } else if (key === 'content-chunks') {
      navigate('/dashboard/content-management/chunks');
    } else if (key === 'claude-prompting') {
      navigate('/dashboard/content-management/claude-prompting');
    }
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="d-flex align-items-center">
            <ContentIcon className="me-2" fontSize="large" />
            Content Management
          </h1>
        </Col>
      </Row>

      {error && (
        <Row className="mb-4">
          <Col>
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row className="mb-4">
        <Col>
          <div className="submenu-container">
            <Nav variant="pills" className="submenu-tabs" activeKey={activeKey} onSelect={handleTabChange}>
              <Nav.Item>
                <Nav.Link eventKey="claude-prompting" className="d-flex align-items-center">
                  <PsychologyIcon className="me-2" />
                  Create Content Prompt
                </Nav.Link>
              </Nav.Item>

              <Nav.Item>
                <Nav.Link eventKey="content-details" className="d-flex align-items-center">
                  <VideoLibraryIcon className="me-2" />
                  Generate Content
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="content-chunks" className="d-flex align-items-center">
                  <StorageIcon className="me-2" />
                  Content Chunks
                </Nav.Link>
              </Nav.Item>
            </Nav>
          </div>
        </Col>
      </Row>
      <Row>
        <Col>
          <Tab.Content>
            {activeKey === 'content-details' ? (
              <Tab.Pane eventKey="content-details" active={true}>
                <ContentDetails setError={setError} />
              </Tab.Pane>
            ) : activeKey === 'content-chunks' ? (
              <Tab.Pane eventKey="content-chunks" active={true}>
                <ContentChunk setError={setError} />
              </Tab.Pane>
            ) : (
              <Tab.Pane eventKey="claude-prompting" active={true}>
                <ClaudePromptingContent setError={setError} />
              </Tab.Pane>
            )}
          </Tab.Content>
        </Col>
      </Row>
    </Container>
  );
};

export default ContentManagement;

