"""
Utility functions for task management in the VisionFrame AI queue system.
This module contains shared functions to avoid circular imports.
"""

import logging
from celery import shared_task
from .db_utils import (
    get_task_by_id,
    update_task_status,
    activate_next_task
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def log_task_execution_time(task):
    """Log the execution time of a task."""
    if not task or not task.get('start_time') or not task.get('end_time'):
        return

    start_time = task['start_time']
    end_time = task['end_time']

    # Calculate execution time
    execution_time = end_time - start_time
    execution_seconds = execution_time.total_seconds()

    # Log execution time
    logger.info(f"Task {task['id']} execution time: {execution_seconds:.2f} seconds")

    # Log additional details for long-running tasks
    if execution_seconds > 60:
        minutes = execution_seconds / 60
        logger.info(f"Task {task['id']} ({task['process_step']}) took {minutes:.2f} minutes to complete")

@shared_task(name='tasks.task_utils.task_completed')
def task_completed(queue_id, result_data=None):
    """Handle task completion and activate the next task in the sequence."""
    logger.info(f"Task {queue_id} completed")

    # Get the task
    task = get_task_by_id(queue_id)

    if not task:
        logger.error(f"Task {queue_id} not found")
        return

    # Update task status to completed
    update_task_status(queue_id, 'completed', result_data=result_data)

    # Get the updated task with end_time
    updated_task = get_task_by_id(queue_id)

    # Log execution time
    log_task_execution_time(updated_task)

    # Activate the next task in the sequence
    chunk_id = task['chunk_id']
    step_order = task['step_order']

    next_task = activate_next_task(chunk_id, step_order)

    if next_task:
        logger.info(f"Activated next task {next_task['id']} for chunk {chunk_id}")
        # The next task will be picked up by the check_pending_tasks function
        return f"Task {queue_id} completed, next task {next_task['id']} activated"
    else:
        logger.info(f"No more tasks for chunk {chunk_id}")
        return f"Task {queue_id} completed, no more tasks for chunk {chunk_id}"

@shared_task(name='tasks.task_utils.task_failed')
def task_failed(queue_id, error_message):
    """Handle task failure."""
    logger.error(f"Task {queue_id} failed: {error_message}")

    # Update task status to failed
    update_task_status(queue_id, 'failed', error_message=error_message)

    # Get the updated task with end_time
    updated_task = get_task_by_id(queue_id)

    # Log execution time
    log_task_execution_time(updated_task)

    return f"Task {queue_id} failed: {error_message}"
