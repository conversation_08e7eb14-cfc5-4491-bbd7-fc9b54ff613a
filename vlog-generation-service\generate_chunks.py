import os
import sys
import logging
import asyncio
import aiomysql
import re
import requests
import json
import shutil
from dotenv import load_dotenv
from runware import Runware, IImageInference

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'db': os.getenv('DB_DATABASE'),
}

# API Keys
XAI_API_KEY = os.getenv('XAI_API_KEY')
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

# Image generation parameters
IMAGE_MODEL = os.getenv('IMAGE_MODEL', 'runware:101@1')  # Default model if not specified in .env
IMAGE_HEIGHT = int(os.getenv('IMAGE_HEIGHT', '2048'))
IMAGE_WIDTH = int(os.getenv('IMAGE_WIDTH', '1152'))
IMAGE_CFG_SCALE = float(os.getenv('IMAGE_CFG_SCALE', '7.5'))
IMAGE_NEGATIVE_PROMPT = os.getenv('IMAGE_NEGATIVE_PROMPT', 'blurry, low quality, deformed, text, watermark')

# API URLs
GROK_API_URL = "https://api.x.ai/v1/chat/completions"

# Constants
WORDS_PER_MINUTE = 146

async def create_db_pool():
    """Create and return a database connection pool"""
    return await aiomysql.create_pool(**DB_CONFIG)

async def log_event(pool, event_type, message, status="INFO", error=None, content_id=None):
    """Log events to both file and database"""

    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")

    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # Convert status to log_level format expected by the database
                log_level = "ERROR" if status == "ERROR" else "INFO"

                # Format the message to include the event type
                formatted_message = f"{event_type}: {message}"
                if error:
                    formatted_message += f" - Error: {str(error)}"

                await cursor.execute(
                    "INSERT INTO process_log (content_id, log_level, message) "
                    "VALUES (%s, %s, %s)",
                    (content_id, log_level, formatted_message)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def get_generated_content(pool, content_id):
    """Get the generated content by ID"""
    async with pool.acquire() as conn:
        async with conn.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(
                """
                SELECT id, title, content_text
                FROM generated_content
                WHERE id = %s
                """,
                (content_id,)
            )
            return await cursor.fetchone()

async def check_existing_chunks(pool, content_id):
    """Check if chunks already exist for a content ID"""
    async with pool.acquire() as conn:
        async with conn.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(
                """
                SELECT COUNT(*) as chunk_count
                FROM content_chunk
                WHERE content_id = %s
                """,
                (content_id,)
            )
            result = await cursor.fetchone()
            return result['chunk_count'] if result else 0

def count_words(text):
    """Count the number of words in a text"""
    if not text:
        return 0
    # Remove HTML tags if present
    text = re.sub(r'<[^>]+>', '', text)
    # Split by whitespace and count
    return len(text.split())

def determine_image_count(chunk_text):
    """Determine the optimal number of images for a chunk based on word count

    The AI decides the number of images per chunk, with a maximum of 5 images per 146 words.
    The algorithm uses a progressive scale to determine the number of images:
    - For very short chunks (< 50 words): 1 image
    - For short chunks (50-100 words): 2 images
    - For medium chunks (100-146 words): 3 images
    - For longer chunks (146-200 words): 4 images
    - For very long chunks (> 200 words): 5 images (maximum)

    Returns:
        int: The number of images to generate (1-5)
    """
    word_count = count_words(chunk_text)

    # Progressive scale for determining image count
    if word_count < 50:
        return 1  # Very short chunks get 1 image
    elif word_count < 100:
        return 2  # Short chunks get 2 images
    elif word_count < 146:
        return 3  # Medium chunks get 3 images
    elif word_count < 200:
        return 4  # Longer chunks get 4 images
    else:
        return 5  # Very long chunks get the maximum of 5 images

def chunk_content(content_text, words_per_chunk=WORDS_PER_MINUTE):
    """Split content into chunks based on word count"""
    if not content_text:
        return []

    # Remove HTML tags if present
    clean_text = re.sub(r'<[^>]+>', '', content_text)

    # Split text into paragraphs
    paragraphs = clean_text.split('\n\n')

    chunks = []
    current_chunk = []
    current_word_count = 0

    for paragraph in paragraphs:
        paragraph = paragraph.strip()
        if not paragraph:
            continue

        paragraph_words = paragraph.split()
        paragraph_word_count = len(paragraph_words)

        # If adding this paragraph exceeds the limit, start a new chunk
        if current_word_count + paragraph_word_count > words_per_chunk and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = []
            current_word_count = 0

        # If a single paragraph is longer than the limit, split it
        if paragraph_word_count > words_per_chunk:
            words = paragraph_words
            while words:
                chunk_words = words[:words_per_chunk - current_word_count]
                current_chunk.extend(chunk_words)
                chunks.append(' '.join(current_chunk))
                words = words[words_per_chunk - current_word_count:]
                current_chunk = []
                current_word_count = 0
        else:
            current_chunk.append(paragraph)
            current_word_count += paragraph_word_count

    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks

async def insert_content_chunk(pool, content_id, chunk_order, text):
    """Insert a content chunk into the database"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                """
                INSERT INTO content_chunk (content_id, chunk_order, text)
                VALUES (%s, %s, %s)
                """,
                (content_id, chunk_order, text)
            )
            await conn.commit()
            return cursor.lastrowid

async def generate_image_prompt(chunk_text, content_title, image_index=0, total_images=1):
    """Generate an image prompt using Grok API

    Args:
        chunk_text (str): The text content of the chunk
        content_title (str): The title of the content
        image_index (int): The index of the current image (0-based)
        total_images (int): The total number of images to generate for this chunk

    Returns:
        str: The generated image prompt
    """
    # Create a more specific prompt based on the image index and total images
    if total_images > 1:
        # For multiple images, we want to focus on different aspects of the chunk
        focus_instructions = [
            "Focus on the main subject or character mentioned in the content.",
            "Focus on the setting or environment described in the content.",
            "Focus on an action or event happening in the content.",
            "Focus on an emotional moment or reaction described in the content.",
            "Focus on a symbolic or metaphorical representation of the content's theme."
        ]

        # Use the appropriate focus instruction based on the image index
        focus = focus_instructions[min(image_index, len(focus_instructions)-1)]

        # Add information about this being one of multiple images
        multi_image_context = f"This is image {image_index+1} of {total_images} for this content segment."
    else:
        # For a single image, create a comprehensive representation
        focus = "Create a comprehensive scene that captures the essence of the content."
        multi_image_context = ""

    prompt = f"""
    Create a detailed image prompt for a video frame that represents the following content:

    Title: {content_title}

    Content: {chunk_text}

    {multi_image_context}
    {focus}

    The image prompt should be descriptive, visual, and suitable for an AI image generator.
    Do not include any text or watermarks in the image description.
    Make the image visually distinct from other images in the same sequence.

    Return ONLY the image prompt text, nothing else.
    """

    headers = {
        "Authorization": f"Bearer {XAI_API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "grok-2-vision-latest",
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 500
    }

    try:
        response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=30)
        response.raise_for_status()

        data = response.json()
        image_prompt = data["choices"][0]["message"]["content"].strip()

        return image_prompt
    except Exception as e:
        logger.error(f"Error generating image prompt: {str(e)}")
        return f"Image {image_index+1} of {total_images} representing {content_title}"

async def get_content_id_for_chunk(pool, chunk_id):
    """Get the content_id for a chunk"""
    async with pool.acquire() as conn:
        async with conn.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(
                """
                SELECT content_id FROM content_chunk WHERE id = %s
                """,
                (chunk_id,)
            )
            result = await cursor.fetchone()
            return result['content_id'] if result else None

async def insert_image_prompt(pool, chunk_id, prompt_text):
    """Insert an image prompt into the database

    This function gets the original content_id from the chunk and uses that
    for the foreign key constraint in the image_prompt table.

    Args:
        pool: Database connection pool
        chunk_id: ID of the chunk
        prompt_text: The prompt text for the image

    Returns:
        int: ID of the inserted image prompt
    """
    try:
        # First, get the content_id for this chunk
        content_id = await get_content_id_for_chunk(pool, chunk_id)
        if not content_id:
            raise ValueError(f"Could not find content_id for chunk {chunk_id}")

        # Now insert the image prompt with the original content_id
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    INSERT INTO image_prompt (content_id, prompt_text, chunk_id)
                    VALUES (%s, %s, %s)
                    """,
                    (content_id, prompt_text, chunk_id)
                )
                await conn.commit()
                return cursor.lastrowid
    except Exception as e:
        logger.error(f"Error inserting image prompt: {str(e)}")
        raise

async def update_content_chunk_image(pool, chunk_id, image_prompt_id):
    """Update the content chunk with the image prompt ID"""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                """
                UPDATE content_chunk
                SET image_prompt_id = %s
                WHERE id = %s
                """,
                (image_prompt_id, chunk_id)
            )
            await conn.commit()

def delete_image_files(content_id, chunk_ids=None):
    """Delete image files from the filesystem"""
    try:
        # Delete content directory if no specific chunk IDs are provided
        content_dir = os.path.join("content-images", f"{content_id}")
        if os.path.exists(content_dir):
            if chunk_ids is None:
                # Delete the entire content directory
                shutil.rmtree(content_dir)
                logging.info(f"Deleted image directory for content ID: {content_id}")
            else:
                # Delete only specific chunk directories
                for chunk_id in chunk_ids:
                    chunk_dir = os.path.join(content_dir, f"chunk_{chunk_id}")
                    if os.path.exists(chunk_dir):
                        shutil.rmtree(chunk_dir)
                        logging.info(f"Deleted image directory for chunk ID: {chunk_id}")
    except Exception as e:
        logging.error(f"Error deleting image files: {str(e)}")

async def delete_existing_chunks(pool, content_id):
    """Delete existing chunks and their associated image prompts for a content ID"""
    async with pool.acquire() as conn:
        async with conn.cursor(aiomysql.DictCursor) as cursor:
            # First get all the chunks to find their image prompt IDs and chunk IDs
            await cursor.execute(
                """
                SELECT id, image_prompt_id
                FROM content_chunk
                WHERE content_id = %s
                """,
                (content_id,)
            )
            chunks = await cursor.fetchall()
            chunk_ids = [chunk['id'] for chunk in chunks]
            image_prompt_ids = [chunk['image_prompt_id'] for chunk in chunks if chunk['image_prompt_id']]

            # Get all image prompts that reference these chunks as content_id
            if chunk_ids:
                chunk_ids_str = ", ".join([str(id) for id in chunk_ids])
                await cursor.execute(
                    f"SELECT id FROM image_prompt WHERE content_id IN ({chunk_ids_str})"
                )
                additional_prompts = await cursor.fetchall()
                for prompt in additional_prompts:
                    if prompt['id'] not in image_prompt_ids:
                        image_prompt_ids.append(prompt['id'])

            # Delete image prompts associated with these chunks
            if image_prompt_ids:
                image_prompt_ids_str = ", ".join([str(id) for id in image_prompt_ids])
                await cursor.execute(
                    f"DELETE FROM image_prompt WHERE id IN ({image_prompt_ids_str})"
                )
                await log_event(
                    pool,
                    "IMAGE_PROMPT_DELETION",
                    f"Deleted {len(image_prompt_ids)} image prompts for content ID: {content_id}",
                    content_id=content_id
                )

            # Delete any image prompts that reference these chunks as chunk_id
            if chunk_ids:
                await cursor.execute(
                    f"DELETE FROM image_prompt WHERE chunk_id IN ({chunk_ids_str})"
                )

            # Now delete the chunks
            await cursor.execute(
                "DELETE FROM content_chunk WHERE content_id = %s",
                (content_id,)
            )

            # Delete image files from the filesystem
            delete_image_files(content_id)

            # Log the deletion
            await log_event(
                pool,
                "CHUNK_DELETION",
                f"Deleted {len(chunks)} chunks and their associated image prompts for content ID: {content_id}",
                content_id=content_id
            )

            await conn.commit()

            return len(chunks)

async def generate_image(pool, image_prompt_id, prompt_text, content_id, chunk_id, image_index=0, total_images=1, model=None):
    """Generate an image using Runware API

    Args:
        pool: Database connection pool
        image_prompt_id: ID of the image prompt in the database
        prompt_text: The prompt text to generate the image
        content_id: ID of the content
        chunk_id: ID of the chunk
        image_index: Index of the image within the chunk (0-based)
        total_images: Total number of images for this chunk
        model: Optional model override (defaults to IMAGE_MODEL from env)

    Returns:
        str: URL of the generated image, or None if generation failed
    """
    try:
        runware = Runware(RUNWARE_API_KEY)

        # Use the model specified in the function call, or fall back to the environment variable
        image_model = model if model else IMAGE_MODEL

        # Log the model being used
        logger.info(f"Generating image using model: {image_model}")

        # Use the parameters from the environment variables
        # For multiple images, we can still add some variety if needed
        if total_images > 1:
            # Keep the same aspect ratio but add slight variations to make images distinct
            # We'll vary the CFG scale slightly for different stylistic results
            cfg_scale_variation = (image_index % 5 - 2) * 0.2  # Values: -0.4, -0.2, 0, 0.2, 0.4
            cfg_scale = IMAGE_CFG_SCALE + cfg_scale_variation

            # Use the standard dimensions from environment variables
            height = IMAGE_HEIGHT
            width = IMAGE_WIDTH
        else:
            # Use the exact parameters from environment variables for single images
            height = IMAGE_HEIGHT
            width = IMAGE_WIDTH
            cfg_scale = IMAGE_CFG_SCALE

        # Log the parameters being used
        logger.info(f"Image parameters - Height: {height}, Width: {width}, CFG Scale: {cfg_scale}")

        request_image = IImageInference(
            positivePrompt=prompt_text,
            negativePrompt=IMAGE_NEGATIVE_PROMPT,
            model=image_model,
            numberResults=1,
            height=height,
            width=width,
            CFGScale=cfg_scale,
        )

        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL

        # Update the image prompt with the URL
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    UPDATE image_prompt
                    SET image_url = %s
                    WHERE id = %s
                    """,
                    (image_url, image_prompt_id)
                )
                await conn.commit()

        # Create a more organized directory structure
        content_dir = os.path.join("content-images", f"{content_id}")
        chunk_dir = os.path.join(content_dir, f"chunk_{chunk_id}")
        os.makedirs(chunk_dir, exist_ok=True)

        # Use a more descriptive filename that includes the image index
        if total_images > 1:
            filename = f"{image_prompt_id}_image_{image_index+1}_of_{total_images}.jpg"
        else:
            filename = f"{image_prompt_id}.jpg"

        # Download the image
        await download_image(pool, image_url, filename, chunk_dir)

        return image_url
    except Exception as e:
        logger.error(f"Error generating image: {str(e)}")
        return None

async def download_image(pool, image_url, filename, output_path):
    """Download image from URL and save to local storage"""
    import aiohttp
    import aiofiles

    # Extract content_id from the output_path (assuming format 'content-images/{content_id}')
    try:
        content_id = int(output_path.split('/')[-1])
    except (ValueError, IndexError):
        content_id = None

    try:
        # Create directory if it doesn't exist
        os.makedirs(output_path, exist_ok=True)

        # Full path for the image
        full_path = os.path.join(output_path, filename)

        # Download the image
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    # Save the image to disk
                    async with aiofiles.open(full_path, 'wb') as f:
                        await f.write(await response.read())

                    await log_event(
                        pool,
                        "IMAGE_DOWNLOAD_SUCCESS",
                        f"Successfully downloaded image to {full_path}",
                        content_id=content_id
                    )
                    return True
                else:
                    await log_event(
                        pool,
                        "IMAGE_DOWNLOAD_FAILED",
                        f"Failed to download image: HTTP {response.status}",
                        content_id=content_id
                    )
                    return False
    except Exception as e:
        await log_event(
            pool,
            "IMAGE_DOWNLOAD_ERROR",
            f"Error downloading image: {str(e)}",
            "ERROR",
            error=e,
            content_id=content_id
        )
        return False

async def generate_chunks_for_content(content_id, overwrite=False):
    """Main function to generate chunks for a content"""
    pool = await create_db_pool()

    try:
        await log_event(pool, "CHUNK_GENERATION_START", f"Starting chunk generation for content ID: {content_id}", content_id=content_id)

        # Get the content
        content = await get_generated_content(pool, content_id)
        if not content:
            await log_event(pool, "CHUNK_GENERATION_ERROR", f"Content with ID {content_id} not found", "ERROR", content_id=content_id)
            return {"success": False, "error": f"Content with ID {content_id} not found"}

        # Check if chunks already exist
        chunk_count = await check_existing_chunks(pool, content_id)
        if chunk_count > 0:
            await log_event(pool, "CHUNK_GENERATION_INFO", f"Found {chunk_count} existing chunks for content ID: {content_id}", content_id=content_id)

            # If overwrite is False, return without generating new chunks
            if not overwrite:
                await log_event(pool, "CHUNK_GENERATION_SKIPPED", f"Skipping chunk generation because chunks already exist and overwrite=False", content_id=content_id)
                return {
                    "success": False,
                    "chunks_exist": True,
                    "count": chunk_count,
                    "content_id": content_id,
                    "title": content['title']
                }

            # Delete existing chunks if overwrite is True
            await log_event(pool, "CHUNK_DELETION_START", f"Deleting {chunk_count} existing chunks for content ID: {content_id}", content_id=content_id)
            deleted_count = await delete_existing_chunks(pool, content_id)
            await log_event(pool, "CHUNK_DELETION_COMPLETE", f"Deleted {deleted_count} chunks for content ID: {content_id}", content_id=content_id)

        # Split content into chunks
        chunks = chunk_content(content['content_text'])
        if not chunks:
            await log_event(pool, "CHUNK_GENERATION_ERROR", "No chunks generated from content", "ERROR", content_id=content_id)
            return {"success": False, "error": "No chunks could be generated from the content"}

        await log_event(pool, "CHUNK_GENERATION_INFO", f"Generated {len(chunks)} chunks from content", content_id=content_id)

        # Process each chunk
        chunk_results = []
        for i, chunk_text in enumerate(chunks):
            chunk_order = i + 1

            # Insert chunk
            chunk_id = await insert_content_chunk(pool, content_id, chunk_order, chunk_text)
            await log_event(pool, "CHUNK_INSERTED", f"Inserted chunk {chunk_order} with ID {chunk_id}", content_id=content_id)

            # Determine how many images to generate for this chunk
            num_images = determine_image_count(chunk_text)
            await log_event(pool, "IMAGE_COUNT_DETERMINED", f"Determined to generate {num_images} images for chunk {chunk_id}", content_id=content_id)

            # Generate multiple image prompts and images for this chunk
            chunk_image_results = []

            for img_idx in range(num_images):
                # Generate image prompt with specific focus based on the image index
                image_prompt = await generate_image_prompt(chunk_text, content['title'], img_idx, num_images)
                await log_event(pool, "IMAGE_PROMPT_GENERATED",
                               f"Generated image prompt {img_idx+1}/{num_images} for chunk {chunk_id}",
                               content_id=content_id)

                # Insert image prompt
                image_prompt_id = await insert_image_prompt(pool, chunk_id, image_prompt)
                await log_event(pool, "IMAGE_PROMPT_INSERTED",
                               f"Inserted image prompt {img_idx+1}/{num_images} with ID {image_prompt_id} for chunk {chunk_id}",
                               content_id=content_id)

                # For the first image, update the chunk with the image prompt ID
                if img_idx == 0:
                    await update_content_chunk_image(pool, chunk_id, image_prompt_id)
                    await log_event(pool, "CHUNK_UPDATED",
                                   f"Updated chunk {chunk_id} with primary image prompt ID {image_prompt_id}",
                                   content_id=content_id)

                # Generate image with image index and total images information
                # We're not passing a specific model here, so it will use the one from environment variables
                image_url = await generate_image(pool, image_prompt_id, image_prompt, content_id, chunk_id, img_idx, num_images)
                if image_url:
                    await log_event(pool, "IMAGE_GENERATED",
                                   f"Generated image {img_idx+1}/{num_images} for chunk {chunk_id} with prompt {image_prompt_id}: {image_url}",
                                   content_id=content_id)
                else:
                    await log_event(pool, "IMAGE_GENERATION_FAILED",
                                   f"Failed to generate image {img_idx+1}/{num_images} for chunk {chunk_id} with prompt {image_prompt_id}",
                                   "ERROR", content_id=content_id)

                # Add this image result to the chunk's image results
                chunk_image_results.append({
                    "image_prompt_id": image_prompt_id,
                    "image_url": image_url,
                    "image_index": img_idx + 1,
                    "total_images": num_images
                })

            # Add the chunk result with all its images
            chunk_results.append({
                "chunk_id": chunk_id,
                "chunk_order": chunk_order,
                "word_count": count_words(chunk_text),
                "image_count": num_images,
                "images": chunk_image_results,
                "primary_image_prompt_id": chunk_image_results[0]["image_prompt_id"] if chunk_image_results else None,
                "primary_image_url": chunk_image_results[0]["image_url"] if chunk_image_results else None
            })

        await log_event(pool, "CHUNK_GENERATION_COMPLETE", f"Completed chunk generation for content ID: {content_id}", content_id=content_id)

        return {
            "success": True,
            "content_id": content_id,
            "title": content['title'],
            "chunks": chunk_results,
            "overwritten": chunk_count > 0
        }
    except Exception as e:
        await log_event(pool, "CHUNK_GENERATION_ERROR", f"Error generating chunks: {str(e)}", "ERROR", error=e, content_id=content_id)
        return {"success": False, "error": str(e)}
    finally:
        pool.close()
        await pool.wait_closed()

if __name__ == "__main__":
    # For testing
    if len(sys.argv) > 1:
        content_id = int(sys.argv[1])
        result = asyncio.run(generate_chunks_for_content(content_id))
        print(json.dumps(result, indent=2))
    else:
        print("Please provide a content ID as an argument")
