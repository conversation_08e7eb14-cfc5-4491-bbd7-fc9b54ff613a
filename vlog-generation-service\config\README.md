# API Configuration Documentation

## Overview
This document outlines the API configurations and settings used in the vlog generation service. The configuration is split into two parts:
1. Static configurations (stored in api_config.py)
2. Dynamic configurations (stored in database)

## API Services

### 1. Anthropic (<PERSON>)
- **Purpose**: Text generation and content creation
- **Key Features**:
  - Model: claude-2
  - Max tokens: 100,000
  - Temperature: 0.7
- **Environment Variables**:
  - ANTHROPIC_API_KEY
- **Documentation**: [Anthropic API Docs](https://docs.anthropic.com/claude/reference/getting-started-with-the-api)

### 2. XAI
- **Purpose**: AI processing
- **Environment Variables**:
  - XAI_API_KEY
- **Documentation**: [XAI API Docs](https://docs.xai.com)

### 3. Runware
- **Purpose**: Image generation
- **Key Features**:
  - Max Resolution: 1024x1024
  - Supported Formats: PNG, JPG
  - Max Batch Size: 10
- **Environment Variables**:
  - RUNWARE_API_KEY
- **Documentation**: [Runware API Docs](https://docs.runware.com)

### 4. ElevenLabs
- **Purpose**: Text-to-speech generation
- **Key Features**:
  - Default Model: eleven_monolingual_v1
  - Supported Formats: MP3, WAV
  - Max Text Length: 5,000 characters
- **Environment Variables**:
  - ELEVENLABS_API_KEY
- **Documentation**: [ElevenLabs API Docs](https://docs.elevenlabs.io/api-reference)

### 5. FishAudio
- **Purpose**: Audio processing
- **Key Features**:
  - Supported Formats: MP3, WAV
  - Max Text Length: 5,000 characters
- **Environment Variables**:
  - FISHAUDIO_API_KEY
  - FISHAUDIO_OUTPUT
- **Documentation**: [FishAudio API Docs](https://docs.fishaudio.com)

## Dynamic Configurations

### Database Tables

1. **api_voice_configs**
   - Stores voice configurations for text-to-speech services
   - Fields: voice_id, voice_name, voice_settings, is_active

2. **api_model_configs**
   - Stores AI model configurations
   - Fields: model_id, model_name, model_settings, is_active

3. **api_generation_settings**
   - Stores general generation settings
   - Fields: setting_type, settings, is_active

### Maintenance

To maintain dynamic configurations:
1. Access the admin panel
2. Navigate to API Settings
3. Update relevant configurations
4. Test changes in staging environment before applying to production

## Environment Setup

1. Create a `.env` file in the root directory
2. Add all required API keys:
```env
ANTHROPIC_API_KEY=your_key_here
XAI_API_KEY=your_key_here
RUNWARE_API_KEY=your_key_here
ELEVENLABS_API_KEY=your_key_here
FISHAUDIO_API_KEY=your_key_here
FISHAUDIO_OUTPUT=path/to/output
```

## Security Notes

1. Never commit API keys to version control
2. Rotate API keys periodically
3. Use environment variables for sensitive data
4. Implement rate limiting and monitoring
5. Regular security audits of API usage

## Troubleshooting

Common issues and solutions:
1. API Rate Limits: Implement exponential backoff
2. Token Expiration: Refresh tokens before expiry
3. Connection Issues: Check network and API status
4. Invalid Responses: Validate API versions and parameters