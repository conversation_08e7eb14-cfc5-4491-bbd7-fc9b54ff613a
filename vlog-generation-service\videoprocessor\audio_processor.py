#!/usr/bin/env python3
"""
Audio Processing for Video Generation
Handles text-to-speech, audio loading, and synchronization
"""

import os
import requests
import json
import logging
from typing import Dict, Optional, Tuple, List
from pathlib import Path
import tempfile
import re

try:
    from moviepy.editor import AudioFileClip
except ImportError:
    print("MoviePy not available. Audio processing will be limited.")
    AudioFileClip = None

logger = logging.getLogger(__name__)

class AudioProcessor:
    """Handle audio processing and text-to-speech generation"""

    def __init__(self, elevenlabs_api_key: str = None):
        self.elevenlabs_api_key = elevenlabs_api_key or os.getenv('ELEVENLABS_API_KEY')
        self.temp_dir = Path(tempfile.gettempdir()) / "visionframe_audio"
        self.temp_dir.mkdir(exist_ok=True)

    def process_audio_element(self, audio_element: Dict, transcript_text: str = None) -> Tuple[AudioFileClip, float]:
        """Process audio element and return audio clip with duration"""

        source = audio_element.get('source', '')
        provider = audio_element.get('provider', '')

        # If source is provided and exists, load it
        if source and os.path.exists(source):
            audio_clip = AudioFileClip(source)
            return audio_clip, audio_clip.duration

        # If source is a URL, download it
        if source and source.startswith('http'):
            try:
                audio_file = self.download_audio(source)
                audio_clip = AudioFileClip(audio_file)
                return audio_clip, audio_clip.duration
            except Exception as e:
                logger.warning(f"Failed to download audio from {source}: {e}")

        # If provider is ElevenLabs, generate speech
        if provider and 'elevenlabs' in provider.lower():
            if transcript_text:
                audio_file = self.generate_elevenlabs_speech(transcript_text, provider)
                if audio_file:
                    audio_clip = AudioFileClip(audio_file)
                    return audio_clip, audio_clip.duration

        # Return None if no audio could be processed
        return None, 5.0  # Default 5 second duration

    def generate_elevenlabs_speech(self, text: str, provider_config: str) -> Optional[str]:
        """Generate speech using ElevenLabs API"""

        if not self.elevenlabs_api_key:
            logger.warning("ElevenLabs API key not provided. Cannot generate speech.")
            return None

        # Parse provider configuration
        config = self.parse_elevenlabs_config(provider_config)

        url = f"https://api.elevenlabs.io/v1/text-to-speech/{config['voice_id']}"

        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.elevenlabs_api_key
        }

        data = {
            "text": text,
            "model_id": config.get('model_id', 'eleven_monolingual_v1'),
            "voice_settings": {
                "stability": config.get('stability', 0.75),
                "similarity_boost": config.get('similarity_boost', 0.75),
                "style": config.get('style', 0.0),
                "use_speaker_boost": config.get('use_speaker_boost', True)
            }
        }

        try:
            response = requests.post(url, json=data, headers=headers)
            response.raise_for_status()

            # Save audio file
            audio_filename = f"elevenlabs_{hash(text)}.mp3"
            audio_path = self.temp_dir / audio_filename

            with open(audio_path, 'wb') as f:
                f.write(response.content)

            logger.info(f"Generated speech audio: {audio_path}")
            return str(audio_path)

        except requests.exceptions.RequestException as e:
            logger.error(f"ElevenLabs API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to generate speech: {e}")
            return None

    def parse_elevenlabs_config(self, provider_config: str) -> Dict:
        """Parse ElevenLabs provider configuration string"""
        config = {}

        # Example: "elevenlabs model_id=eleven_multilingual_v2 voice_id=XrExE9yKIg1WjnnlVkGX stability=0.75"
        parts = provider_config.split()

        for part in parts[1:]:  # Skip 'elevenlabs'
            if '=' in part:
                key, value = part.split('=', 1)

                # Convert numeric values
                if key in ['stability', 'similarity_boost', 'style']:
                    config[key] = float(value)
                elif key == 'use_speaker_boost':
                    config[key] = value.lower() == 'true'
                else:
                    config[key] = value

        return config

    def download_audio(self, url: str) -> str:
        """Download audio file from URL"""
        try:
            response = requests.get(url)
            response.raise_for_status()

            # Determine file extension
            content_type = response.headers.get('content-type', '')
            if 'mp3' in content_type:
                ext = '.mp3'
            elif 'wav' in content_type:
                ext = '.wav'
            elif 'mp4' in content_type or 'm4a' in content_type:
                ext = '.m4a'
            else:
                ext = '.mp3'  # Default

            # Save file
            audio_filename = f"downloaded_{hash(url)}{ext}"
            audio_path = self.temp_dir / audio_filename

            with open(audio_path, 'wb') as f:
                f.write(response.content)

            return str(audio_path)

        except Exception as e:
            logger.error(f"Failed to download audio from {url}: {e}")
            raise

    def get_audio_duration(self, audio_path: str) -> float:
        """Get duration of audio file"""
        try:
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration
            audio_clip.close()
            return duration
        except Exception as e:
            logger.error(f"Failed to get audio duration for {audio_path}: {e}")
            return 5.0  # Default duration

    def create_silence(self, duration: float) -> AudioFileClip:
        """Create silent audio clip of specified duration"""
        # Create a very quiet tone instead of pure silence
        # This helps with video encoding compatibility
        import numpy as np
        from moviepy.audio.AudioClip import AudioArrayClip

        sample_rate = 44100
        samples = int(duration * sample_rate)

        # Create very quiet white noise
        audio_array = np.random.normal(0, 0.001, (samples, 2))  # Stereo

        return AudioArrayClip(audio_array, fps=sample_rate)

    def cleanup_temp_files(self):
        """Clean up temporary audio files"""
        try:
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
            logger.info("Cleaned up temporary audio files")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp files: {e}")

class TranscriptExtractor:
    """Extract and process transcript data from various sources"""

    def __init__(self):
        pass

    def extract_from_audio_element(self, audio_element: Dict, default_text: str = None) -> str:
        """Extract transcript text from audio element"""

        # Check if transcript is embedded in the element
        transcript = audio_element.get('transcript', '')
        if transcript:
            return transcript

        # Check for transcript file reference
        transcript_file = audio_element.get('transcript_file', '')
        if transcript_file and os.path.exists(transcript_file):
            with open(transcript_file, 'r', encoding='utf-8') as f:
                return f.read().strip()

        # Use default text or generate sample
        if default_text:
            return default_text

        # Generate sample transcript based on element name
        element_name = audio_element.get('name', 'Voiceover')
        scene_number = self.extract_scene_number(element_name)

        sample_transcripts = [
            "Welcome to our amazing journey through the world of technology and innovation.",
            "In this segment, we explore the fascinating developments that are shaping our future.",
            "Discover the incredible possibilities that await us in the digital transformation era.",
            "Join us as we delve deeper into the revolutionary changes happening around us."
        ]

        # Return appropriate sample based on scene number
        if scene_number and scene_number <= len(sample_transcripts):
            return sample_transcripts[scene_number - 1]

        return sample_transcripts[0]

    def extract_scene_number(self, element_name: str) -> Optional[int]:
        """Extract scene number from element name"""
        match = re.search(r'(\d+)', element_name)
        if match:
            return int(match.group(1))
        return None

    def clean_transcript_text(self, text: str) -> str:
        """Clean and normalize transcript text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove special characters that might cause issues
        text = re.sub(r'[^\w\s\.,!?;:\-\'"]', '', text)

        # Ensure proper sentence endings
        text = re.sub(r'([.!?])\s*$', r'\1', text)
        if not text.endswith(('.', '!', '?')):
            text += '.'

        return text.strip()

    def split_into_segments(self, text: str, max_length: int = 35) -> List[str]:
        """Split transcript into segments of maximum length"""
        words = text.split()
        segments = []
        current_segment = []
        current_length = 0

        for word in words:
            if current_length + len(word) + 1 <= max_length:
                current_segment.append(word)
                current_length += len(word) + 1
            else:
                if current_segment:
                    segments.append(' '.join(current_segment))
                current_segment = [word]
                current_length = len(word)

        if current_segment:
            segments.append(' '.join(current_segment))

        return segments
