import os
import requests
from urllib.parse import urlparse
import logging

# Get the directory where the script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(SCRIPT_DIR, 'download_image.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def download_image(image_url, save_directory, filename):
    """
    Downloads an image from the provided URL and saves it to the specified directory with the given filename.
    
    Args:
        image_url (str): The URL of the image to download
        save_directory (str): The directory path where the image will be saved
        filename (str): The filename to save the image as (including extension)
    
    Returns:
        bool: True if download and save were successful, False otherwise
    """
    try:
        logger.info(f"Starting image download from URL: {image_url}")
        
        # Remove any leading slashes and make path relative to script directory
        save_directory = save_directory.lstrip('/')
        full_directory_path = os.path.join(SCRIPT_DIR, save_directory)
        
        # Create the directory if it doesn't exist
        os.makedirs(full_directory_path, exist_ok=True)
        logger.info(f"Directory verified/created: {full_directory_path}")
        
        # Get the file extension from the URL if not in the filename
        if '.' not in filename:
            parsed_url = urlparse(image_url)
            original_filename = os.path.basename(parsed_url.path)
            if '.' in original_filename:
                extension = original_filename.split('.')[-1]
                filename = f"{filename}.{extension}"
                logger.info(f"Added extension to filename: {filename}")
        
        # Full path where the image will be saved
        save_path = os.path.join(full_directory_path, filename)
        logger.info(f"Full save path: {save_path}")
        
        # Download the image
        logger.info("Initiating download request")
        response = requests.get(image_url, stream=True)
        response.raise_for_status()  # Raise an exception for HTTP errors
        
        # Save the image
        logger.info("Starting to write image to file")
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        
        logger.info(f"Image successfully downloaded and saved to {save_path}")
        return True
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading image: {e}")
        return False
    except IOError as e:
        logger.error(f"Error saving image: {e}")
        return False

if __name__ == "__main__":
    # Example usage
    image_url = "https://im.runware.ai/image/ws/2/ii/ee60e93c-c07e-4964-8077-727cb4c84afa.jpg"
    save_directory = "content-images"  # Remove the leading slash
    filename = "test.jpg"
    
    logger.info("Starting image download process")
    result = download_image(image_url, save_directory, filename)
    logger.info(f"Download process completed with result: {result}")
