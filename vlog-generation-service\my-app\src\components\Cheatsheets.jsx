import React, { useState } from 'react';
import { Container, Row, Col, Nav, Tab, Al<PERSON>, Card } from 'react-bootstrap';
import PromptEngineeringCheatsheet from './cheatsheets/PromptEngineeringCheatsheet';
import ImagePromptCheatsheet from './cheatsheets/ImagePromptCheatsheet';
import ThumbnailPromptCheatsheet from './cheatsheets/ThumbnailPromptCheatsheet';
import ClaudePromptCheatsheet from './cheatsheets/ClaudePromptCheatsheet';
import AdvancedThumbnailGenerator from './cheatsheets/AdvancedThumbnailGenerator';
import {
  MenuBook as MenuBookIcon,
  Description as DescriptionIcon,
  Image as ImageIcon,
  Wallpaper as WallpaperIcon,
  SmartToy as SmartToyIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';

const Cheatsheets = () => {
  const [activeKey, setActiveKey] = useState('prompt-engineering');
  const [error, setError] = useState(null);

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="d-flex align-items-center">
            <MenuBookIcon className="me-2" fontSize="large" />
            Prompt Engineering Cheatsheets
          </h1>
          <p className="text-muted">
            Reference guides for creating effective prompts for content and image generation
          </p>
        </Col>
      </Row>

      {error && (
        <Row className="mb-4">
          <Col>
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      <Row>
        <Col md={3} lg={2} className="mb-4">
          <Nav variant="pills" className="flex-column" activeKey={activeKey} onSelect={(k) => setActiveKey(k)}>
            <Nav.Item>
              <Nav.Link eventKey="prompt-engineering" className="d-flex align-items-center">
                <DescriptionIcon className="me-2" />
                Content Creation
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="image-prompt" className="d-flex align-items-center">
                <ImageIcon className="me-2" />
                Image Generation
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="thumbnail-prompt" className="d-flex align-items-center">
                <WallpaperIcon className="me-2" />
                Thumbnail Creation
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="advanced-thumbnail" className="d-flex align-items-center">
                <AutoAwesomeIcon className="me-2" />
                Advanced Thumbnail
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="claude-prompt" className="d-flex align-items-center">
                <SmartToyIcon className="me-2" />
                Claude Prompting
              </Nav.Link>
            </Nav.Item>
          </Nav>
        </Col>
        <Col md={9} lg={10}>
          <Tab.Content>
            <Tab.Pane eventKey="prompt-engineering" active={activeKey === 'prompt-engineering'}>
              <PromptEngineeringCheatsheet />
            </Tab.Pane>
            <Tab.Pane eventKey="image-prompt" active={activeKey === 'image-prompt'}>
              <ImagePromptCheatsheet />
            </Tab.Pane>
            <Tab.Pane eventKey="thumbnail-prompt" active={activeKey === 'thumbnail-prompt'}>
              <ThumbnailPromptCheatsheet />
            </Tab.Pane>
            <Tab.Pane eventKey="advanced-thumbnail" active={activeKey === 'advanced-thumbnail'}>
              <AdvancedThumbnailGenerator />
            </Tab.Pane>
            <Tab.Pane eventKey="claude-prompt" active={activeKey === 'claude-prompt'}>
              <ClaudePromptCheatsheet />
            </Tab.Pane>
          </Tab.Content>
        </Col>
      </Row>
    </Container>
  );
};

export default Cheatsheets;
