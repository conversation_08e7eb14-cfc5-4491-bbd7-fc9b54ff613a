.claude-prompt-container {
  max-width: 100%;
  margin-bottom: 2rem;
}

.claude-prompt-container .card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.claude-prompt-container .card-header {
  padding: 1rem 1.25rem;
  font-weight: 600;
}

.claude-prompt-container .card-header.bg-primary {
  background-color: #2541b8 !important;
}

.claude-prompt-container .card-header.bg-success {
  background-color: #28a745 !important;
}

.claude-prompt-container .card-header.bg-info {
  background-color: #17a2b8 !important;
}

.claude-prompt-container .card-header.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.claude-prompt-container .btn-warning {
  background-color: #ffc107 !important;
  border-color: #ffc107 !important;
  color: #212529 !important;
  font-weight: 600;
}

.claude-prompt-container .badge.bg-danger {
  font-size: 0.85rem;
  padding: 0.4rem 0.6rem;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.claude-prompt-container .card-header.bg-dark {
  background-color: #000 !important;
}

.claude-prompt-container pre.generated-prompt {
  white-space: pre-wrap;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
  color: #333;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.claude-prompt-container .table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

@media (max-width: 768px) {
  .claude-prompt-container .card-body {
    padding: 1rem;
  }
}

/* Content Prompts Modal Styles */
.modal-xl .modal-content {
  border-radius: 8px;
}

.modal-xl .table-responsive {
  max-height: 60vh;
  overflow-y: auto;
}

.modal-xl .table {
  margin-bottom: 0;
}

.modal-xl .table th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 1;
}

.modal-xl .table td {
  vertical-align: middle;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modal-xl .table td:nth-child(2) {
  min-width: 150px;
}

.modal-xl .table .d-flex.gap-2 {
  justify-content: center;
}
