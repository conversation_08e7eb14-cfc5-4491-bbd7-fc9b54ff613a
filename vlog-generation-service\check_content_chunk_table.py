import os
import pymysql
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

def check_content_chunk_table():
    """Check if content_chunk table exists and its structure"""
    try:
        logger.info("Connecting to database...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check if content_chunk table exists
        logger.info("Checking if content_chunk table exists...")
        cursor.execute("SHOW TABLES LIKE 'content_chunk'")
        content_chunk_exists = cursor.fetchone() is not None
        
        if content_chunk_exists:
            logger.info("content_chunk table exists")
            
            # Get table structure
            cursor.execute("DESCRIBE content_chunk")
            columns = cursor.fetchall()
            logger.info("content_chunk table structure:")
            for column in columns:
                logger.info(f"{column['Field']} - {column['Type']} - {column['Null']} - {column['Key']} - {column['Default']}")
            
            # Get sample data
            cursor.execute("SELECT * FROM content_chunk LIMIT 5")
            rows = cursor.fetchall()
            logger.info(f"Found {len(rows)} rows in content_chunk table")
            for row in rows:
                logger.info(f"Row: {row}")
        else:
            logger.info("content_chunk table does not exist")
        
        # Check if image_prompt table exists
        logger.info("Checking if image_prompt table exists...")
        cursor.execute("SHOW TABLES LIKE 'image_prompt'")
        image_prompt_exists = cursor.fetchone() is not None
        
        if image_prompt_exists:
            logger.info("image_prompt table exists")
            
            # Get table structure
            cursor.execute("DESCRIBE image_prompt")
            columns = cursor.fetchall()
            logger.info("image_prompt table structure:")
            for column in columns:
                logger.info(f"{column['Field']} - {column['Type']} - {column['Null']} - {column['Key']} - {column['Default']}")
            
            # Get sample data
            cursor.execute("SELECT * FROM image_prompt LIMIT 5")
            rows = cursor.fetchall()
            logger.info(f"Found {len(rows)} rows in image_prompt table")
            for row in rows:
                logger.info(f"Row: {row}")
        else:
            logger.info("image_prompt table does not exist")
        
        # Close the connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking tables: {str(e)}")

if __name__ == "__main__":
    check_content_chunk_table()
