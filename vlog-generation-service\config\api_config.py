"""
API Configuration Settings
This file contains all static API configurations and documentation for the vlog generation service.
"""

from typing import Dict, Any

# Anthropic (Claude) API Configuration
ANTHROPIC_CONFIG: Dict[str, Any] = {
    "api_key_env": "ANTHROPIC_API_KEY",
    "base_url": "https://api.anthropic.com/v1",
    "model": "claude-2",
    "max_tokens": 100000,
    "temperature": 0.7,
    "documentation_url": "https://docs.anthropic.com/claude/reference/getting-started-with-the-api"
}

# XAI API Configuration
XAI_CONFIG: Dict[str, Any] = {
    "api_key_env": "XAI_API_KEY",
    "base_url": "https://api.xai.com/v1",  # Replace with actual base URL
    "documentation_url": "https://docs.xai.com"  # Replace with actual documentation URL
}

# Runware API Configuration
RUNWARE_CONFIG: Dict[str, Any] = {
    "api_key_env": "RUNWARE_API_KEY",
    "base_url": "https://api.runware.com",  # Replace with actual base URL
    "image_generation": {
        "max_resolution": "1024x1024",
        "supported_formats": ["png", "jpg"],
        "max_batch_size": 10
    },
    "documentation_url": "https://docs.runware.com"  # Replace with actual documentation URL
}

# ElevenLabs API Configuration
ELEVENLABS_CONFIG: Dict[str, Any] = {
    "api_key_env": "ELEVENLABS_API_KEY",
    "base_url": "https://api.elevenlabs.io/v1",
    "default_model": "eleven_monolingual_v1",
    "supported_formats": ["mp3", "wav"],
    "max_text_length": 5000,
    "documentation_url": "https://docs.elevenlabs.io/api-reference"
}

# FishAudio API Configuration
FISHAUDIO_CONFIG: Dict[str, Any] = {
    "api_key_env": "FISHAUDIO_API_KEY",
    "output_dir_env": "FISHAUDIO_OUTPUT",
    "supported_formats": ["mp3", "wav"],
    "max_text_length": 5000,
    "documentation_url": "https://docs.fishaudio.com"  # Replace with actual documentation URL
}

# Database Schema for Dynamic Configurations
"""
CREATE TABLE api_voice_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    api_name VARCHAR(50) NOT NULL,
    voice_id VARCHAR(100) NOT NULL,
    voice_name VARCHAR(100) NOT NULL,
    voice_settings JSON,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE api_model_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    api_name VARCHAR(50) NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    model_settings JSON,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE api_generation_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_type VARCHAR(50) NOT NULL,
    settings JSON NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
"""

# Example of dynamic settings to be stored in database
DYNAMIC_SETTINGS_EXAMPLES = {
    "elevenlabs_voices": [
        {
            "voice_id": "george",
            "voice_name": "George",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.75
            }
        }
    ],
    "anthropic_prompts": [
        {
            "prompt_type": "content_generation",
            "template": "Your prompt template here",
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }
    ],
    "image_generation_settings": [
        {
            "setting_type": "thumbnail",
            "settings": {
                "width": 1280,
                "height": 720,
                "style": "modern",
                "format": "png"
            }
        }
    ]
}