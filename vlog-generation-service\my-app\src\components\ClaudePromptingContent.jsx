import React, { useState, useEffect } from 'react';
import { Card, Table, Row, Col, Badge, Al<PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, Mo<PERSON>, Container } from 'react-bootstrap';
import { API_BASE_URL } from '../constants';
import { Psychology as PsychologyIcon, Save as SaveIcon, Edit as EditIcon, Visibility as VisibilityIcon, Delete as DeleteIcon } from '@mui/icons-material';
import './ClaudePromptingContent.css';

const ClaudePromptingContent = ({ setError: setParentError }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [topic, setTopic] = useState('');
  const [contentType, setContentType] = useState('blog_post');
  const [purpose, setPurpose] = useState('inform');
  const [audience, setAudience] = useState('general');
  const [tone, setTone] = useState('conversational');
  const [style, setStyle] = useState('descriptive');
  const [wordCount, setWordCount] = useState('500');
  const [customWordCount, setCustomWordCount] = useState('');
  const [showCustomWordCount, setShowCustomWordCount] = useState(false);
  const [persona, setPersona] = useState('expert');

  // State for saving content
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [autoGeneratedPrompt, setAutoGeneratedPrompt] = useState(false);
  const [parametersChanged, setParametersChanged] = useState(false);
  const [promptGenerated, setPromptGenerated] = useState(false);
  const [categories, setCategories] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [contentPromptText, setContentPromptText] = useState('');
  const [callToActionText, setCallToActionText] = useState('');

  // State for the content prompts modal
  const [showPromptsModal, setShowPromptsModal] = useState(false);
  const [contentPrompts, setContentPrompts] = useState([]);
  const [loadingPrompts, setLoadingPrompts] = useState(false);
  const [promptsError, setPromptsError] = useState(null);
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [promptToDelete, setPromptToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Options for dropdowns
  const contentTypes = [
    { value: 'blog_post', label: 'Blog Post/Article' },
    { value: 'video_script', label: 'Video Script' },
    { value: 'story', label: 'Story/Narrative' },
    { value: 'essay', label: 'Essay/Academic Writing' },
    { value: 'social_media', label: 'Social Media Content' },
    { value: 'marketing_copy', label: 'Marketing Copy' },
    { value: 'educational_content', label: 'Educational Content' },
    { value: 'vlog_script', label: 'Vlog Script' },
    { value: 'analysis', label: 'Analysis/Commentary' }
  ];

  const purposes = [
    { value: 'inform', label: 'Inform/Educate' },
    { value: 'persuade', label: 'Persuade/Convince' },
    { value: 'entertain', label: 'Entertain' },
    { value: 'inspire', label: 'Inspire/Motivate' },
    { value: 'analyze', label: 'Analyze/Examine' },
    { value: 'instruct', label: 'Instruct/Guide' },
    { value: 'storytell', label: 'Tell a Story' }
  ];

  const audiences = [
    { value: 'general', label: 'General Audience' },
    { value: 'beginners', label: 'Beginners' },
    { value: 'intermediate', label: 'Intermediate Level' },
    { value: 'advanced', label: 'Advanced/Experts' },
    { value: 'professionals', label: 'Professionals' },
    { value: 'students', label: 'Students' },
    { value: 'educators', label: 'Educators' },
    { value: 'researchers', label: 'Researchers' },
    { value: 'business', label: 'Business Professionals' },
    { value: 'technical', label: 'Technical Audience' }
  ];

  const tones = [
    { value: 'conversational', label: 'Conversational' },
    { value: 'formal', label: 'Formal' },
    { value: 'informal', label: 'Informal' },
    { value: 'humorous', label: 'Humorous' },
    { value: 'serious', label: 'Serious' },
    { value: 'optimistic', label: 'Optimistic' },
    { value: 'cautionary', label: 'Cautionary' },
    { value: 'empathetic', label: 'Empathetic' },
    { value: 'neutral', label: 'Neutral/Objective' },
    { value: 'authoritative', label: 'Authoritative' }
  ];

  const styles = [
    { value: 'descriptive', label: 'Descriptive' },
    { value: 'narrative', label: 'Narrative' },
    { value: 'analytical', label: 'Analytical' },
    { value: 'persuasive', label: 'Persuasive' },
    { value: 'conversational', label: 'Conversational' },
    { value: 'technical', label: 'Technical' },
    { value: 'creative', label: 'Creative' },
    { value: 'instructional', label: 'Instructional' },
    { value: 'journalistic', label: 'Journalistic' },
    { value: 'academic', label: 'Academic' }
  ];

  const wordCounts = [
    { value: '100', label: '100 words (very short)' },
    { value: '200', label: '200 words (short)' },
    { value: '300', label: '300 words (brief)' },
    { value: '500', label: '500 words (standard)' },
    { value: '750', label: '750 words (medium)' },
    { value: '1000', label: '1000 words (detailed)' },
    { value: '1500', label: '1500 words (comprehensive)' },
    { value: '2000', label: '2000 words (in-depth)' },
    { value: 'custom', label: 'Custom word count' }
  ];

  const personas = [
    { value: 'expert', label: 'Subject Matter Expert' },
    { value: 'teacher', label: 'Teacher/Educator' },
    { value: 'journalist', label: 'Journalist' },
    { value: 'storyteller', label: 'Storyteller' },
    { value: 'coach', label: 'Coach/Mentor' },
    { value: 'researcher', label: 'Researcher' },
    { value: 'consultant', label: 'Consultant' },
    { value: 'friend', label: 'Friendly Advisor' },
    { value: 'analyst', label: 'Analyst' },
    { value: 'enthusiast', label: 'Enthusiast' }
  ];

  // Extract content_prompt from generatedPrompt when it changes
  useEffect(() => {
    if (generatedPrompt) {
      console.log('Full generated prompt:', generatedPrompt);

      // Try different regex patterns to extract the content_prompt
      let extractedPrompt = '';

      // Pattern 1: Look for content between "## Generated Prompt" and "## Call to Action"
      const pattern1 = /## Generated Prompt\s*\n([\s\S]*?)\n\s*## Call to Action/m;
      const match1 = generatedPrompt.match(pattern1);

      // Pattern 2: Look for content after "## Generated Prompt" until the end or next section
      const pattern2 = /## Generated Prompt\s*\n([\s\S]*?)(?:\n\s*##|$)/m;
      const match2 = generatedPrompt.match(pattern2);

      // Pattern 3: Just look for any content after "Generated Prompt"
      const pattern3 = /Generated Prompt[:\s]*\n([\s\S]*?)(?:\n\s*(?:Call to Action|#)|$)/mi;
      const match3 = generatedPrompt.match(pattern3);

      if (match1 && match1[1]) {
        extractedPrompt = match1[1].trim();
        console.log('Extracted content_prompt using pattern 1:', extractedPrompt);
      } else if (match2 && match2[1]) {
        extractedPrompt = match2[1].trim();
        console.log('Extracted content_prompt using pattern 2:', extractedPrompt);
      } else if (match3 && match3[1]) {
        extractedPrompt = match3[1].trim();
        console.log('Extracted content_prompt using pattern 3:', extractedPrompt);
      } else {
        // If no pattern matches, use a fallback approach
        const lines = generatedPrompt.split('\n');
        const startIndex = lines.findIndex(line =>
          line.includes('Generated Prompt') || line.includes('GENERATED PROMPT'));

        if (startIndex !== -1) {
          const endIndex = lines.findIndex((line, idx) =>
            idx > startIndex && (line.includes('Call to Action') || line.includes('CALL TO ACTION')));

          if (endIndex !== -1) {
            extractedPrompt = lines.slice(startIndex + 1, endIndex).join('\n').trim();
          } else {
            extractedPrompt = lines.slice(startIndex + 1).join('\n').trim();
          }
          console.log('Extracted content_prompt using fallback approach:', extractedPrompt);
        }
      }

      if (extractedPrompt) {
        // Store the extracted content in the window object
        window.lastContentPrompt = extractedPrompt;
        console.log('Stored in window.lastContentPrompt:', window.lastContentPrompt);

        setContentPromptText(extractedPrompt);
      }

      // Try different regex patterns to extract the call_to_action
      let extractedCTA = '';

      // Pattern 1: Look for content after "## Call to Action" until the end
      const ctaPattern1 = /## Call to Action\s*\n([\s\S]*?)$/m;
      const ctaMatch1 = generatedPrompt.match(ctaPattern1);

      // Pattern 2: Just look for any content after "Call to Action"
      const ctaPattern2 = /Call to Action[:\s]*\n([\s\S]*?)$/mi;
      const ctaMatch2 = generatedPrompt.match(ctaPattern2);

      if (ctaMatch1 && ctaMatch1[1]) {
        extractedCTA = ctaMatch1[1].trim();
        console.log('Extracted call_to_action using pattern 1:', extractedCTA);
      } else if (ctaMatch2 && ctaMatch2[1]) {
        extractedCTA = ctaMatch2[1].trim();
        console.log('Extracted call_to_action using pattern 2:', extractedCTA);
      } else {
        // If no pattern matches, use a fallback approach
        const lines = generatedPrompt.split('\n');
        const startIndex = lines.findIndex(line =>
          line.includes('Call to Action') || line.includes('CALL TO ACTION'));

        if (startIndex !== -1) {
          extractedCTA = lines.slice(startIndex + 1).join('\n').trim();
          console.log('Extracted call_to_action using fallback approach:', extractedCTA);
        }
      }

      if (extractedCTA) {
        // Store the extracted content in the window object
        window.lastCallToAction = extractedCTA;
        console.log('Stored in window.lastCallToAction:', window.lastCallToAction);

        setCallToActionText(extractedCTA);
      }
    }
  }, [generatedPrompt]);

  // Function to fetch content prompts
  const fetchContentPrompts = async () => {
    setLoadingPrompts(true);
    setPromptsError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/api/content-prompts`);

      if (!response.ok) {
        throw new Error(`Failed to fetch content prompts: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched content prompts:', data);
      setContentPrompts(data);
    } catch (error) {
      console.error('Error fetching content prompts:', error);
      setPromptsError(error.message || 'Failed to fetch content prompts');
    } finally {
      setLoadingPrompts(false);
    }
  };

  // Function to handle prompt deletion
  const handleDeletePrompt = async (id) => {
    if (!id) return;

    setDeleteLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/api/content-prompts/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete prompt: ${response.statusText}`);
      }

      // Remove the deleted prompt from the list
      setContentPrompts(contentPrompts.filter(prompt => prompt.id !== id));
      setShowDeleteModal(false);
      setPromptToDelete(null);
    } catch (error) {
      console.error('Error deleting prompt:', error);
      setPromptsError(error.message || 'Failed to delete prompt');
    } finally {
      setDeleteLoading(false);
    }
  };

  // Function to load a prompt for editing
  const handleEditPrompt = (prompt) => {
    setSelectedPrompt(prompt);
    setTopic(prompt.title || '');
    setContentType(prompt.content_type || 'blog_post');
    setPurpose(prompt.purpose || 'inform');
    setAudience(prompt.audience || 'general');
    setTone(prompt.tone || 'conversational');
    setStyle(prompt.style || 'descriptive');
    setWordCount(prompt.word_count || '500');
    setPersona(prompt.persona || 'expert');
    setContentPromptText(prompt.content_prompt || '');
    setCallToActionText(prompt.call_to_action || '');

    // Format the prompt for display
    const formattedPrompt = `
# Claude Optimized Prompt

## Topic
${prompt.title || ''}

## Parameters
- Content Type: ${contentTypes.find(ct => ct.value === (prompt.content_type || 'blog_post'))?.label || prompt.content_type || 'Blog Post/Article'}
- Purpose: ${purposes.find(p => p.value === (prompt.purpose || 'inform'))?.label || prompt.purpose || 'Inform'}
- Target Audience: ${audiences.find(a => a.value === (prompt.audience || 'general'))?.label || prompt.audience || 'General'}
- Tone: ${tones.find(t => t.value === (prompt.tone || 'conversational'))?.label || prompt.tone || 'Conversational'}
- Style: ${styles.find(s => s.value === (prompt.style || 'descriptive'))?.label || prompt.style || 'Descriptive'}
- Word Count: ${prompt.word_count || '500'} words
- Writing Persona: ${personas.find(p => p.value === (prompt.persona || 'expert'))?.label || prompt.persona || 'Expert'}

## Generated Prompt
${prompt.content_prompt || ''}

## Call to Action
${prompt.call_to_action || ''}
`;

    setGeneratedPrompt(formattedPrompt);
    setShowPromptsModal(false);

    // Reset the parameters changed and prompt generated flags
    setParametersChanged(false);
    setPromptGenerated(true);
  };

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/categories`);
        if (!response.ok) {
          throw new Error(`Failed to fetch categories: ${response.statusText}`);
        }
        const data = await response.json();
        setCategories(data);
        if (data.length > 0) {
          setSelectedCategoryId(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setError('Failed to fetch categories. Please try again later.');
      }
    };

    fetchCategories();
  }, []);

  // Function to mark parameters as changed
  const markParametersChanged = () => {
    setParametersChanged(true);
    setPromptGenerated(false);
  };

  // Handle word count change
  const handleWordCountChange = (e) => {
    const value = e.target.value;
    setWordCount(value);
    setShowCustomWordCount(value === 'custom');
    markParametersChanged();
  };

  // Handle save content
  const handleSaveContent = async () => {
    // Check if parameters have changed and prompt hasn't been regenerated
    if (parametersChanged && !promptGenerated) {
      setError('You have changed parameters. Please click "Generate Claude-Optimized Prompt" before saving.');
      return;
    }

    // Validate all required fields
    const validationErrors = [];

    if (!topic) {
      validationErrors.push('Please enter a topic');
    }

    if (!contentType) {
      validationErrors.push('Please select a content type');
    }

    if (!purpose) {
      validationErrors.push('Please select a purpose');
    }

    if (!audience) {
      validationErrors.push('Please select an audience');
    }

    if (!tone) {
      validationErrors.push('Please select a tone');
    }

    if (!style) {
      validationErrors.push('Please select a style');
    }

    if (!wordCount) {
      validationErrors.push('Please select a word count');
    } else if (wordCount === 'custom' && (!customWordCount || isNaN(customWordCount) || parseInt(customWordCount) <= 0)) {
      validationErrors.push('Please enter a valid custom word count');
    }

    if (!persona) {
      validationErrors.push('Please select a persona');
    }

    // Check if we have content to save
    let promptToUse = window.lastContentPrompt || contentPromptText;
    let ctaToUse = window.lastCallToAction || callToActionText;

    // If no prompt exists, generate a default one based on the selected parameters
    if (!promptToUse) {
      // Create a default prompt based on the selected parameters
      promptToUse = `Act as an expert in ${contentType.replace('_', ' ')} creation with a specialization in ${topic} content.

Your task is to create a ${wordCount === 'custom' ? customWordCount : wordCount}-word ${contentType.replace('_', ' ')} about ${topic} for a ${audience} audience. Use a ${tone} tone and a ${style} style to engage the readers.

**Specific Purpose:** To ${purpose} the audience about ${topic}.

**Target Audience:** ${audiences.find(a => a.value === audience)?.label || audience}.

**Tone:** ${tones.find(t => t.value === tone)?.label || tone}.

**Writing Style:** ${styles.find(s => s.value === style)?.label || style}.

**Word Count:** Approximately ${wordCount === 'custom' ? customWordCount : wordCount} words.

**Structure Requirements:**
1. **Introduction** - Briefly introduce the topic and why it matters.
2. **Main Content** - Provide detailed information, examples, or steps related to the topic.
3. **Conclusion** - Summarize key points and provide a call to action.

Please generate the content following these detailed instructions.`;

      // Set the generated prompt
      window.lastContentPrompt = promptToUse;
      setContentPromptText(promptToUse);
      console.log('Generated default content prompt:', promptToUse);

      // Set the auto-generated prompt flag
      setAutoGeneratedPrompt(true);

      // We'll continue with saving since we've generated a valid prompt
    }

    // If no call to action exists, generate a default one
    if (!ctaToUse) {
      ctaToUse = `If you found this ${contentType.replace('_', ' ')} helpful, please like, share, and subscribe for more content about ${topic}!`;
      window.lastCallToAction = ctaToUse;
      setCallToActionText(ctaToUse);
      console.log('Generated default call to action:', ctaToUse);

      // Set the auto-generated prompt flag if we haven't already set it
      if (!autoGeneratedPrompt) {
        setAutoGeneratedPrompt(true);
      }
    }

    // Display all validation errors if any
    if (validationErrors.length > 0) {
      setError(validationErrors.join('\n'));
      return;
    }

    setIsSaving(true);
    setSaveSuccess(false);
    setError(null);

    try {
      // Use the formDataToSave object if available, otherwise build the request data from state
      let requestData;

      // Build the request data using the validated and potentially generated values
      requestData = {
        topic: topic,
        content_type: contentType,
        purpose: purpose,
        audience: audience,
        tone: tone,
        style: style,
        word_count: wordCount === 'custom' ? customWordCount : wordCount,
        persona: persona,
        content_prompt: promptToUse,  // This will be either the existing prompt or our generated one
        call_to_action: ctaToUse,     // This will be either the existing CTA or our generated one
        output_format: 'standard'
      };

      console.log('Built requestData for saving:', requestData);
      console.log('contentPromptText before saving:', contentPromptText);
      console.log('callToActionText before saving:', callToActionText);
      console.log('window.lastContentPrompt:', window.lastContentPrompt);
      console.log('window.lastCallToAction:', window.lastCallToAction);

      const response = await fetch(`${API_BASE_URL}/api/content-prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save content: ${errorText}`);
      }

      const data = await response.json();
      console.log('Save response:', data);

      setSaveSuccess(true);

      // Show success notification
      if (autoGeneratedPrompt) {
        alert('Content saved successfully! A default prompt was automatically generated based on your parameters.');
      } else {
        alert('Content saved successfully!');
      }

      // Reset success state after 5 seconds
      setTimeout(() => {
        setSaveSuccess(false);
        setAutoGeneratedPrompt(false);
      }, 5000);
    } catch (error) {
      console.error('Error saving content:', error);
      setError(error.message || 'Failed to save content');
    } finally {
      setIsSaving(false);
    }
  };

  const handleGeneratePrompt = async () => {
    if (!topic) {
      setError('Please enter a topic');
      if (setParentError) setParentError('Please enter a topic');
      return;
    }

    // Validate custom word count if selected
    if (wordCount === 'custom') {
      if (!customWordCount || isNaN(customWordCount) || parseInt(customWordCount) <= 0) {
        setError('Please enter a valid custom word count');
        if (setParentError) setParentError('Please enter a valid custom word count');
        return;
      }
    }

    setLoading(true);
    setError(null);
    if (setParentError) setParentError(null);

    try {
      const requestData = {
        prompt_type: 'claude_prompt',
        topic: topic,
        content_type: contentType,
        purpose: purpose,
        audience: audience,
        tone: tone,
        style: style,
        word_count: wordCount === 'custom' ? customWordCount : wordCount,
        persona: persona
      };

      console.log('Sending request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate prompt: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      // Reset the parameters changed flag and set prompt generated flag
      setParametersChanged(false);
      setPromptGenerated(true);

      // Extract content from the response
      let promptText = '';
      let ctaText = '';
      let formattedPrompt = '';

      if (data.success && data.generated_prompt) {
        // Set the generated prompt
        const generatedPrompt = data.generated_prompt;
        formattedPrompt = generatedPrompt;

        // Extract content_prompt
        const promptMatch = generatedPrompt.match(/## Generated Prompt\s*\n([\s\S]*?)(?:\n\s*## Call to Action|$)/m);
        if (promptMatch && promptMatch[1]) {
          promptText = promptMatch[1].trim();
          console.log('Extracted content_prompt from generated_prompt:', promptText);
        } else {
          // Fallback: extract everything after "Generated Prompt" until the end or next section
          const lines = generatedPrompt.split('\n');
          const startIndex = lines.findIndex(line => line.includes('Generated Prompt'));
          if (startIndex !== -1) {
            const endIndex = lines.findIndex((line, idx) => idx > startIndex && line.includes('Call to Action'));
            if (endIndex !== -1) {
              promptText = lines.slice(startIndex + 1, endIndex).join('\n').trim();
            } else {
              promptText = lines.slice(startIndex + 1).join('\n').trim();
            }
            console.log('Extracted content_prompt using fallback approach:', promptText);
          }
        }

        // Extract call_to_action
        const ctaMatch = generatedPrompt.match(/## Call to Action\s*\n([\s\S]*?)$/m);
        if (ctaMatch && ctaMatch[1]) {
          ctaText = ctaMatch[1].trim();
          console.log('Extracted call_to_action from generated_prompt:', ctaText);
        } else {
          // Fallback: extract everything after "Call to Action" until the end
          const lines = generatedPrompt.split('\n');
          const startIndex = lines.findIndex(line => line.includes('Call to Action'));
          if (startIndex !== -1) {
            ctaText = lines.slice(startIndex + 1).join('\n').trim();
            console.log('Extracted call_to_action using fallback approach:', ctaText);
          }
        }
      } else if (data.content_prompt) {
        // Use the content_prompt and call_to_action from the API response
        promptText = data.content_prompt || '';
        ctaText = data.call_to_action || '';
        console.log('API returned content_prompt:', promptText);
        console.log('API returned call_to_action:', ctaText);

        // Format the prompt
        formattedPrompt = `
# Claude Optimized Prompt

## Topic
${topic}

## Parameters
- Content Type: ${contentTypes.find(ct => ct.value === contentType)?.label || contentType}
- Purpose: ${purposes.find(p => p.value === purpose)?.label || purpose}
- Target Audience: ${audiences.find(a => a.value === audience)?.label || audience}
- Tone: ${tones.find(t => t.value === tone)?.label || tone}
- Style: ${styles.find(s => s.value === style)?.label || style}
- Word Count: ${wordCount === 'custom' ? customWordCount : wordCount} words
- Writing Persona: ${personas.find(p => p.value === persona)?.label || persona}

## Generated Prompt
${promptText}

## Call to Action
${ctaText}
`;
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }

      // Set the generated prompt
      setGeneratedPrompt(formattedPrompt);

      // Use the full generated prompt as the content_prompt
      if (data.success && data.generated_prompt) {
        // Use the full generated prompt as the content_prompt
        promptText = data.generated_prompt;
        console.log('Using full generated prompt as content_prompt:', promptText);
      } else if (!promptText) {
        // Fallback to a generic template if no prompt was generated
        promptText = `Write a ${contentType} about ${topic} for ${audience} with a ${tone} tone and ${style} style. The content should be around ${wordCount} words and provide valuable insights as a ${persona}.`;
        console.log('Using default content_prompt:', promptText);
      }

      if (!ctaText) {
        ctaText = "If you found this helpful, please like, share, and subscribe for more content!";
        console.log('Using default call_to_action:', ctaText);
      }

      // Store the content_prompt and call_to_action
      console.log('Setting contentPromptText to:', promptText);
      console.log('Setting callToActionText to:', ctaText);

      // Store in window object for debugging and direct access
      window.lastContentPrompt = promptText;
      window.lastCallToAction = ctaText;

      // Set state values
      setContentPromptText(promptText);
      setCallToActionText(ctaText);

      // Log the state after setting
      setTimeout(() => {
        console.log('contentPromptText after setting:', contentPromptText);
        console.log('callToActionText after setting:', callToActionText);
        console.log('window.lastContentPrompt:', window.lastContentPrompt);
        console.log('window.lastCallToAction:', window.lastCallToAction);
      }, 100);

      // Directly update the form data for saving
      window.formDataToSave = {
        topic,
        content_type: contentType,
        purpose,
        audience,
        tone,
        style,
        word_count: wordCount === 'custom' ? customWordCount : wordCount,
        persona,
        content_prompt: promptText,
        call_to_action: ctaText,
        output_format: 'standard'
      };

      console.log('Prepared form data for saving:', window.formDataToSave);
    } catch (error) {
      console.error('Error generating prompt:', error);
      setError(error.message || 'Failed to generate prompt');
      if (setParentError) setParentError(error.message || 'Failed to generate prompt');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="claude-prompt-container">
      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          {error.includes('\n') ? (
            <>
              <strong>Please fix the following errors:</strong>
              <ul className="mb-0 mt-2">
                {error.split('\n').map((err, index) => (
                  <li key={index}>{err}</li>
                ))}
              </ul>
            </>
          ) : (
            error
          )}
        </Alert>
      )}

      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-end mb-3">
            <Button
              variant="primary"
              onClick={() => {
                fetchContentPrompts();
                setShowPromptsModal(true);
              }}
              className="d-flex align-items-center"
            >
              <EditIcon className="me-2" fontSize="small" />
              Edit/View Content Prompts
            </Button>
          </div>
          <Card>
            <Card.Header className="d-flex align-items-center">
              <PsychologyIcon className="me-2" />
              <h5 className="mb-0">Generate Claude-Optimized Prompt</h5>
            </Card.Header>
            <Card.Body>

              <Form>
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Topic <Badge bg="primary">Required</Badge></Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter your topic or question"
                        value={topic}
                        onChange={(e) => {
                          setTopic(e.target.value);
                          markParametersChanged();
                        }}
                      />
                      <Form.Text className="text-muted">
                        Be specific about what you want Claude to help you with
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Content Type</Form.Label>
                      <Form.Select
                        value={contentType}
                        onChange={(e) => {
                          setContentType(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {contentTypes.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Purpose</Form.Label>
                      <Form.Select
                        value={purpose}
                        onChange={(e) => {
                          setPurpose(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {purposes.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Target Audience</Form.Label>
                      <Form.Select
                        value={audience}
                        onChange={(e) => {
                          setAudience(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {audiences.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Tone</Form.Label>
                      <Form.Select
                        value={tone}
                        onChange={(e) => {
                          setTone(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {tones.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Writing Style</Form.Label>
                      <Form.Select
                        value={style}
                        onChange={(e) => {
                          setStyle(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {styles.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Word Count/Length</Form.Label>
                      <Form.Select
                        value={wordCount}
                        onChange={handleWordCountChange}
                      >
                        {wordCounts.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                      {showCustomWordCount && (
                        <Form.Control
                          type="number"
                          placeholder="Enter custom word count"
                          className="mt-2"
                          value={customWordCount}
                          onChange={(e) => {
                            setCustomWordCount(e.target.value);
                            markParametersChanged();
                          }}
                        />
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Claude Persona</Form.Label>
                      <Form.Select
                        value={persona}
                        onChange={(e) => {
                          setPersona(e.target.value);
                          markParametersChanged();
                        }}
                      >
                        {personas.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6} className="d-flex align-items-end">
                    <Button
                      variant={parametersChanged ? "warning" : "primary"}
                      onClick={handleGeneratePrompt}
                      disabled={loading}
                      className="w-100 mb-3"
                    >
                      {parametersChanged && <span className="me-2">⚠️</span>}
                      {loading ? (
                        <>
                          <Spinner
                            as="span"
                            animation="border"
                            size="sm"
                            role="status"
                            aria-hidden="true"
                            className="me-2"
                          />
                          Generating...
                        </>
                      ) : (
                        parametersChanged ? 'Regenerate Prompt (Required)' : 'Generate Claude-Optimized Prompt'
                      )}
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {generatedPrompt && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header className={parametersChanged && !promptGenerated ? "bg-warning text-dark" : "bg-success text-white"}>
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Generated Claude-Optimized Prompt</h5>
                  {parametersChanged && !promptGenerated && (
                    <Badge bg="danger" className="ms-2">
                      Parameters changed - Regenerate prompt required
                    </Badge>
                  )}
                </div>
              </Card.Header>
              <Card.Body>
                <pre className="generated-prompt">
                  {generatedPrompt}
                </pre>
                <div className="d-flex mt-2">
                  <Button
                    variant="outline-primary"
                    onClick={() => {
                      navigator.clipboard.writeText(generatedPrompt);
                      alert('Prompt copied to clipboard!');
                    }}
                    className="me-2"
                  >
                    Copy to Clipboard
                  </Button>
                  <Button
                    variant="success"
                    onClick={handleSaveContent}
                    disabled={isSaving || (parametersChanged && !promptGenerated)}
                    title={parametersChanged && !promptGenerated ? "You must regenerate the prompt after changing parameters" : "Save this content prompt"}
                  >
                    {parametersChanged && !promptGenerated && <span className="me-1">⚠️</span>}
                    {isSaving ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Saving...
                      </>
                    ) : (
                      <>
                        <SaveIcon className="me-1" fontSize="small" />
                        Save Content
                      </>
                    )}
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header className="bg-info text-white">
              <h5 className="mb-0">Claude Prompt Engineering Guide</h5>
            </Card.Header>
            <Card.Body>
              <h5>Key Principles for Effective Claude Prompts</h5>
              <Table striped bordered hover responsive>
                <thead>
                  <tr>
                    <th>Principle</th>
                    <th>Description</th>
                    <th>Example</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Be Specific</strong></td>
                    <td>Clearly state what you want Claude to do</td>
                    <td>"Write a 500-word blog post about renewable energy" instead of "Tell me about energy"</td>
                  </tr>
                  <tr>
                    <td><strong>Provide Context</strong></td>
                    <td>Give relevant background information</td>
                    <td>"I'm writing for an audience of high school students who have basic knowledge of physics"</td>
                  </tr>
                  <tr>
                    <td><strong>Define Format</strong></td>
                    <td>Specify the structure you want</td>
                    <td>"Format this as a bullet-point list with 5 main points and 2-3 sub-points under each"</td>
                  </tr>
                  <tr>
                    <td><strong>Set Tone</strong></td>
                    <td>Indicate the desired writing style</td>
                    <td>"Use a conversational, friendly tone that would appeal to young professionals"</td>
                  </tr>
                  <tr>
                    <td><strong>Use Examples</strong></td>
                    <td>Provide examples of what you want</td>
                    <td>"Here's an example of the kind of response I'm looking for: [example]"</td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Prompt Structure Best Practices</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h6>1. Be Specific</h6>
                  <ul>
                    <li><strong>Bad</strong>: "Write about climate change."</li>
                    <li><strong>Good</strong>: "Write a 500-word blog post about three innovative technologies addressing climate change, with statistics from the last two years."</li>
                  </ul>
                </Col>
                <Col md={6}>
                  <h6>2. Structure Your Prompts</h6>
                  <ul>
                    <li>Use clear sections with headers</li>
                    <li>Number your requirements</li>
                    <li>Use bullet points for clarity</li>
                    <li>Provide context before requests</li>
                  </ul>
                </Col>
              </Row>
              <Row className="mt-3">
                <Col md={6}>
                  <h6>3. Define Output Parameters</h6>
                  <ul>
                    <li>Specify word count/length</li>
                    <li>Mention style, tone, and format</li>
                    <li>State target audience</li>
                    <li>Include content structure preferences</li>
                  </ul>
                </Col>
                <Col md={6}>
                  <h6>4. Use Persona Direction</h6>
                  <ul>
                    <li>"Act as an expert in [field]"</li>
                    <li>"Write as if you are a [profession/expert]"</li>
                    <li>"Adopt the writing style of [author/publication]"</li>
                  </ul>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Content-Specific Frameworks</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-4">
                <h6>Blog Posts/Articles</h6>
                <div className="bg-light p-3 rounded">
                  <pre className="mb-0">
{`Write a [word count] [content type] about [specific topic] for [target audience].
Include:
1. An engaging headline that [objective]
2. [Number] key points focused on [specific aspects]
3. Practical examples or case studies for each point
4. A conclusion with actionable takeaways
Use a [tone] tone and incorporate relevant statistics or research where appropriate.`}
                  </pre>
                </div>
              </div>

              <div className="mb-4">
                <h6>Video Scripts</h6>
                <div className="bg-light p-3 rounded">
                  <pre className="mb-0">
{`Create a script for a [duration] video about [topic] targeting [audience].
Structure it with:
1. A hook that [specific attention-grabbing approach]
2. Brief introduction explaining [key context]
3. [Number] main sections covering: [list key points]
4. For each section, include: explanation, example, and visual description
5. Call-to-action asking viewers to [desired action]
Use [tone] language and keep sentences short and conversational.`}
                  </pre>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Advanced Prompting Techniques</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h6>Chain-of-Thought</h6>
                  <div className="bg-light p-3 rounded mb-3">
                    <pre className="mb-0">
{`To solve [complex problem], please:
1. Break down the key elements of [problem]
2. Consider different approaches to addressing [aspect]
3. Analyze the pros and cons of each approach
4. Recommend the best solution based on [specific criteria]
5. Explain your reasoning for this recommendation`}
                    </pre>
                  </div>
                </Col>
                <Col md={6}>
                  <h6>Multi-Step Generation</h6>
                  <div className="bg-light p-3 rounded mb-3">
                    <pre className="mb-0">
{`First, generate 5 potential [headlines/topics/angles] for [content type] about [subject].
Then, I'll select one, and you'll create an outline.
Finally, expand that outline into a complete [content type].`}
                    </pre>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Troubleshooting AI Responses</h5>
            </Card.Header>
            <Card.Body>
              <Table striped bordered hover responsive>
                <thead className="bg-light">
                  <tr>
                    <th>Problem</th>
                    <th>Solution</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Too Generic</td>
                    <td>"Avoid clichés and generic advice. Include specific, actionable insights that aren't commonly found in basic articles."</td>
                  </tr>
                  <tr>
                    <td>Too Short</td>
                    <td>"Elaborate on each point with at least [X] sentences of explanation and one specific example."</td>
                  </tr>
                  <tr>
                    <td>Lack Creativity</td>
                    <td>"Approach this from an unexpected angle that most people overlook."</td>
                  </tr>
                  <tr>
                    <td>Too Technical</td>
                    <td>"Explain concepts as if speaking to someone with no background in [field]. Use analogies to everyday experiences."</td>
                  </tr>
                  <tr>
                    <td>Too Simplistic</td>
                    <td>"Dive deeper into the nuances and complexities of [topic]. Include advanced concepts and address potential counterarguments."</td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Success Alert */}
      {saveSuccess && (
        <Row className="mb-4">
          <Col>
            <Alert variant="success" onClose={() => setSaveSuccess(false)} dismissible>
              Content saved successfully!
              {autoGeneratedPrompt && (
                <div className="mt-2">
                  <strong>Note:</strong> A default prompt was automatically generated based on your parameters.
                  You can view and edit it in the "Generated Claude-Optimized Prompt" section below.
                </div>
              )}
            </Alert>
          </Col>
        </Row>
      )}

      {/* Content Prompts Modal */}
      <Modal
        show={showPromptsModal}
        onHide={() => setShowPromptsModal(false)}
        size="xl"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Content Prompts</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {promptsError && (
            <Alert variant="danger" onClose={() => setPromptsError(null)} dismissible>
              {promptsError}
            </Alert>
          )}

          {loadingPrompts ? (
            <div className="text-center p-4">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
              <p className="mt-2">Loading content prompts...</p>
            </div>
          ) : contentPrompts.length === 0 ? (
            <Alert variant="info">
              No content prompts found. Create a new prompt to get started.
            </Alert>
          ) : (
            <div className="table-responsive">
              <Table striped bordered hover>
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Topic</th>
                    <th>Content Type</th>
                    <th>Purpose</th>
                    <th>Audience</th>
                    <th>Tone</th>
                    <th>Style</th>
                    <th>Word Count</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {contentPrompts.map((prompt) => (
                    <tr key={prompt.id}>
                      <td>{prompt.id}</td>
                      <td>{prompt.title}</td>
                      <td>{prompt.content_type}</td>
                      <td>{prompt.purpose}</td>
                      <td>{prompt.audience}</td>
                      <td>{prompt.tone}</td>
                      <td>{prompt.style}</td>
                      <td>{prompt.word_count}</td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEditPrompt(prompt)}
                            title="Edit/View"
                          >
                            <VisibilityIcon fontSize="small" />
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => {
                              setPromptToDelete(prompt);
                              setShowDeleteModal(true);
                            }}
                            title="Delete"
                          >
                            <DeleteIcon fontSize="small" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPromptsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        show={showDeleteModal}
        onHide={() => setShowDeleteModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Confirm Deletion</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the prompt "{promptToDelete?.title}"? This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={() => handleDeletePrompt(promptToDelete?.id)}
            disabled={deleteLoading}
          >
            {deleteLoading ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ClaudePromptingContent;
