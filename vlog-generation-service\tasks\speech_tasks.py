"""
Speech generation tasks for the VisionFrame AI queue system.
"""

import os
import json
import logging
import requests
from celery import shared_task
from .db_utils import get_chunk_data, update_task_status, get_task_by_id
from .task_utils import task_completed, task_failed
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Speech generation parameters
ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY')
FISHAUDIO_API_KEY = os.getenv('FISHAUDIO_API_KEY')
FISHAUDIO_OUTPUT = os.getenv('FISHAUDIO_OUTPUT', 'content-speech/')

@shared_task(
    name='tasks.speech_tasks.generate_speech',
    bind=True,
    max_retries=3,
    default_retry_delay=60,
    rate_limit='5/m'
)
def generate_speech(self, queue_id):
    """Generate speech for a content chunk."""
    logger.info(f"Generating speech for task {queue_id}")

    try:
        # Get the task
        task = get_task_by_id(queue_id)
        if not task:
            error_msg = f"Task {queue_id} not found"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Get the chunk data
        chunk_id = task['chunk_id']
        chunk_data = get_chunk_data(chunk_id)

        if not chunk_data:
            error_msg = f"Chunk data not found for chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Extract the text from the chunk
        chunk_text = chunk_data['chunk']['text']

        if not chunk_text:
            error_msg = f"No text found in chunk {chunk_id}"
            logger.error(error_msg)
            task_failed.delay(queue_id, error_msg)
            return

        # Generate speech using FishAudio API
        audio_path = generate_speech_with_fishaudio(chunk_text, f"chunk_{chunk_id}_speech")

        if not audio_path:
            # Try fallback to ElevenLabs
            audio_path = generate_speech_with_elevenlabs(chunk_text, f"chunk_{chunk_id}_speech")

            if not audio_path:
                error_msg = f"Failed to generate speech for chunk {chunk_id}"
                logger.error(error_msg)
                task_failed.delay(queue_id, error_msg)
                return

        # Update the result data
        result_data = {
            'audio_path': audio_path,
            'text': chunk_text
        }

        # Mark the task as completed
        task_completed.delay(queue_id, result_data)

        return f"Generated speech for chunk {chunk_id}"

    except Exception as e:
        logger.error(f"Error generating speech for task {queue_id}: {str(e)}")

        # Retry the task if it's not the last retry
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            # If max retries exceeded, mark the task as failed
            task_failed.delay(queue_id, str(e))

        return f"Error generating speech for task {queue_id}: {str(e)}"

def generate_speech_with_fishaudio(text, audio_name):
    """Generate speech using the FishAudio API."""
    if not FISHAUDIO_API_KEY:
        logger.error("FISHAUDIO_API_KEY not configured")
        return None

    try:
        # Create the speech directory if it doesn't exist
        os.makedirs(FISHAUDIO_OUTPUT, exist_ok=True)

        # Prepare the API request
        url = "https://api.fishaudio.ai/v1/tts"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {FISHAUDIO_API_KEY}"
        }
        payload = {
            "text": text,
            "voice_id": "en-US-GuyNeural",  # Default voice
            "speed": 1.0,
            "format": "mp3"
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            logger.error(f"FishAudio API error: {response.status_code} - {response.text}")
            return None

        # Save the audio
        audio_path = f"{FISHAUDIO_OUTPUT}{audio_name}.mp3"

        with open(audio_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"Audio saved to {audio_path}")

        return audio_path

    except Exception as e:
        logger.error(f"Error generating speech with FishAudio: {str(e)}")
        return None

def generate_speech_with_elevenlabs(text, audio_name):
    """Generate speech using the ElevenLabs API."""
    if not ELEVENLABS_API_KEY:
        logger.error("ELEVENLABS_API_KEY not configured")
        return None

    try:
        # Create the speech directory if it doesn't exist
        os.makedirs(FISHAUDIO_OUTPUT, exist_ok=True)

        # Prepare the API request
        url = "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM"  # Default voice ID
        headers = {
            "Content-Type": "application/json",
            "xi-api-key": ELEVENLABS_API_KEY
        }
        payload = {
            "text": text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            logger.error(f"ElevenLabs API error: {response.status_code} - {response.text}")
            return None

        # Save the audio
        audio_path = f"{FISHAUDIO_OUTPUT}{audio_name}.mp3"

        with open(audio_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"Audio saved to {audio_path}")

        return audio_path

    except Exception as e:
        logger.error(f"Error generating speech with ElevenLabs: {str(e)}")
        return None
