import asyncio
import os
from dotenv import load_dotenv
import logging
from runware import Runware, IImageInference

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('landing_images.log')
    ]
)
logger = logging.getLogger(__name__)

# Runware configuration
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

# Define image prompts for landing page features
LANDING_PAGE_PROMPTS = [
    {
        "name": "shorts_creation",
        "prompt": "A professional content creator editing short-form video content on a modern computer setup, with vibrant colors and dynamic visual elements, high quality, detailed, 4k resolution"
    },
    {
        "name": "ai_image_generation",
        "prompt": "AI generating beautiful, photorealistic images from text prompts, showing a split screen with text on one side and resulting images on the other, futuristic, high tech, vibrant colors, 4k resolution"
    },
    {
        "name": "automatic_subtitles",
        "prompt": "Video with automatic subtitles being generated in real-time, showing multiple language options, clean modern interface, professional video editing software, 4k resolution"
    },
    {
        "name": "ai_speech_generation",
        "prompt": "AI converting text to natural-sounding speech, visualized with sound waves and voice patterns, futuristic audio studio setup, high tech, 4k resolution"
    },
    {
        "name": "image_to_video",
        "prompt": "Images transforming into video sequences with a 'Coming Soon' banner, showing the transition process, professional video editing software interface, cinematic, 4k resolution"
    },
    {
        "name": "automated_editing",
        "prompt": "Automated video editing with AI, showing transitions, effects, and cuts being applied automatically, professional editing timeline, futuristic interface, 4k resolution"
    },
    {
        "name": "video_rendering",
        "prompt": "Video rendering and compilation process, showing progress bars and preview screens, professional video production studio, high-end equipment, 4k resolution"
    },
    {
        "name": "hero_image",
        "prompt": "A comprehensive vlog content creation studio with AI assistants helping create content, showing multiple screens with different stages of production, vibrant, professional, futuristic, 4k resolution"
    }
]

async def generate_image(prompt, filename, runware):
    """Generate an image using Runware API."""
    try:
        logger.info(f"Generating image for: {filename}")
        
        request_image = IImageInference(
            positivePrompt=prompt,
            negativePrompt="blurry, low quality, deformed, text, watermark",
            model="runware:101@1",
            numberResults=1,
            height=1024,
            width=1024,
            CFGScale=7.5,
        )
                
        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL
        
        # Download the image
        await download_image(image_url, filename)
        
        logger.info(f"Successfully generated image: {filename}")
        return image_url
    except Exception as e:
        logger.error(f"Error generating image {filename}: {str(e)}")
        raise

async def download_image(image_url, filename):
    """Download image from URL and save to local storage."""
    import aiohttp
    import aiofiles
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    # Ensure the images directory exists
                    output_dir = "my-app/public/images"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # Save the image
                    file_path = os.path.join(output_dir, filename)
                    async with aiofiles.open(file_path, mode='wb') as f:
                        await f.write(await response.read())
                    
                    logger.info(f"Successfully downloaded image to {file_path}")
                    return True
                else:
                    logger.error(f"Failed to download image. Status code: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        return False

async def main():
    """Main function to generate all landing page images."""
    logger.info("Starting landing page image generation")
    
    runware = Runware(api_key=RUNWARE_API_KEY)
    await runware.connect()
    
    try:
        for prompt_data in LANDING_PAGE_PROMPTS:
            filename = f"{prompt_data['name']}.jpg"
            await generate_image(prompt_data['prompt'], filename, runware)
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
    finally:
        logger.info("Image generation process completed")

if __name__ == "__main__":
    asyncio.run(main())
