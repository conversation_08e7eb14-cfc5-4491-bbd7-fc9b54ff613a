# Database Management Scripts

This directory contains scripts for managing the database schema.

## add_field_to_table.py

This script can be used to add a field to a table in the database or list the columns in a table.

### Usage

```bash
# Add a field to a table
python add_field_to_table.py --table TABLE_NAME --field FIELD_NAME --type FIELD_TYPE

# List columns in a table
python add_field_to_table.py --table TABLE_NAME --list
```

### Examples

```bash
# Add content_prompt field to content_prompt_claude table
python add_field_to_table.py --table content_prompt_claude --field content_prompt --type TEXT

# List columns in content_prompt_claude table
python add_field_to_table.py --table content_prompt_claude --list

# Add a new field to content_prompt_claude table
python add_field_to_table.py --table content_prompt_claude --field new_field --type TEXT
```

### Default Values

- `--table`: content_prompt_claude
- `--field`: content_prompt
- `--type`: TEXT

If you don't specify these parameters, the script will use the default values.

## add_content_prompt_field.py

This script specifically adds the content_prompt field to the content_prompt_claude table.

### Usage

```bash
python add_content_prompt_field.py
```

## update_schema.py

This script checks if the content_prompt_claude table exists and creates it if it doesn't. It also checks if the content_prompt field exists in the table and adds it if it doesn't.

### Usage

```bash
python update_schema.py
```

## Database Schema

The content_prompt_claude table has the following columns:

1. id (INTEGER) PRIMARY KEY
2. topic (TEXT) NOT NULL
3. category_id (INTEGER)
4. content_type (TEXT)
5. purpose (TEXT)
6. audience (TEXT)
7. tone (TEXT)
8. style (TEXT)
9. word_count (TEXT)
10. persona (TEXT)
11. content_prompt (TEXT)
12. call_to_action (TEXT)
13. output_format (TEXT)
14. generated_at (TIMESTAMP)

## Adding a New Field

If you need to add a new field to the content_prompt_claude table, you can use the add_field_to_table.py script:

```bash
python add_field_to_table.py --table content_prompt_claude --field new_field --type TEXT
```

Replace `new_field` with the name of the field you want to add and `TEXT` with the type of the field.

## Checking the Database Schema

If you want to check the current schema of the content_prompt_claude table, you can use the add_field_to_table.py script with the --list option:

```bash
python add_field_to_table.py --table content_prompt_claude --list
```

This will list all the columns in the content_prompt_claude table along with their types and constraints.
