import os
import logging
import time
import aiohttp
import asyncio
from datetime import datetime
from runware_sdk import IImageInference, RunwareSDK
from config import (
    RUNWARE_API_KEY,
    DB_CONFIG,
    SCRIPT_DIR
)
import aiomysql

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def log_event(pool, event_type, message, status="INFO", error=None):
    """Log events to both file and database."""
    timestamp = datetime.now()
    log_details = {"timestamp": timestamp, "status": status}
    
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", extra=log_details)
    else:
        logger.info(f"{event_type}: {message}", extra=log_details)
    
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log event to database: {e}")

async def download_image(pool, image_url, filename):
    """Downloads an image from the provided URL and saves it to the specified directory."""
    try:
        save_directory = "thumbnail-images"  # Specific directory for thumbnails
        logger.info(f"Starting thumbnail download from URL: {image_url}")
        await log_event(pool, "THUMBNAIL_DOWNLOAD_START", f"Starting download from URL: {image_url}")
        
        save_directory = save_directory.lstrip('/')
        full_directory_path = os.path.join(SCRIPT_DIR, save_directory)
        
        os.makedirs(full_directory_path, exist_ok=True)
        logger.info(f"Directory verified/created: {full_directory_path}")
        
        if '.' not in filename:
            filename = f"{filename}.jpg"
        
        save_path = os.path.join(full_directory_path, filename)
        
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    with open(save_path, 'wb') as f:
                        while True:
                            chunk = await response.content.read(8192)
                            if not chunk:
                                break
                            f.write(chunk)
                    
                    logger.info(f"Image saved successfully to: {save_path}")
                    await log_event(pool, "THUMBNAIL_SAVE_SUCCESS", f"Image saved to {save_path}")
                    return True
                else:
                    await log_event(
                        pool,
                        "THUMBNAIL_DOWNLOAD_ERROR",
                        f"Failed to download image. Status: {response.status}",
                        "ERROR"
                    )
                    return False
                    
    except Exception as e:
        await log_event(
            pool,
            "THUMBNAIL_DOWNLOAD_ERROR",
            f"Error downloading image: {str(e)}",
            "ERROR",
            error=e
        )
        return False

async def update_thumbnail_url(pool, content_id, url):
    """Update the thumbnail URL and generation timestamp in the database."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                await cursor.execute(
                    """
                    UPDATE generated_content 
                    SET thumbnail_url = %s, thumbnail_generated_at = NOW() 
                    WHERE id = %s
                    """,
                    (url, content_id)
                )
                await conn.commit()

                filename = f"thumbnail_{content_id}.jpg"
                
                download_result = await download_image(
                    pool=pool,
                    image_url=url,
                    filename=filename
                )

                if download_result:
                    await log_event(
                        pool,
                        "THUMBNAIL_UPDATE_SUCCESS",
                        f"Updated and downloaded thumbnail for content ID {content_id}",
                        "INFO"
                    )
                else:
                    await log_event(
                        pool,
                        "THUMBNAIL_DOWNLOAD_FAILED",
                        f"Updated URL but failed to download thumbnail for content ID {content_id}",
                        "WARNING"
                    )

            except Exception as e:
                await log_event(
                    pool,
                    "THUMBNAIL_UPDATE_ERROR",
                    f"Failed to update/download thumbnail for content ID {content_id}",
                    "ERROR",
                    error=e
                )
                raise

async def generate_thumbnails(pool, runware):
    """Generate thumbnails for content without thumbnail_url."""
    start_time = time.time()
    
    await log_event(
        pool,
        "THUMBNAIL_GENERATION",
        "Starting thumbnail generation for pending content",
        "INFO"
    )
    
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    """
                    SELECT id, thumbnail_prompt 
                    FROM generated_content 
                    WHERE thumbnail_url IS NULL 
                    AND thumbnail_prompt IS NOT NULL
                    """
                )
                pending_content = await cursor.fetchall()

        for content_id, thumbnail_prompt in pending_content:
            try:
                request_image = IImageInference(
                    positivePrompt=thumbnail_prompt,
                    negativePrompt="blurry, low quality, deformed, text, watermark",
                    model="runware:101@1",
                    numberResults=1,
                    height=1080,  # Standard thumbnail dimensions
                    width=1920,
                    CFGScale=7.5,
                )
                
                images = await runware.imageInference(requestImage=request_image)
                image_url = images[0].imageURL
                
                await update_thumbnail_url(pool, content_id, image_url)
                
                await log_event(
                    pool,
                    "THUMBNAIL_SUCCESS",
                    f"Generated thumbnail for content ID {content_id}: {image_url}",
                    "INFO"
                )
                
            except Exception as e:
                await log_event(
                    pool,
                    "THUMBNAIL_ERROR",
                    f"Failed to generate thumbnail for content ID {content_id}",
                    "ERROR",
                    error=e
                )
        
        execution_time = time.time() - start_time
        await log_event(
            pool,
            "THUMBNAIL_GENERATION_COMPLETE",
            f"Completed generating all thumbnails in {execution_time:.2f} seconds",
            "INFO"
        )
        
    except Exception as e:
        await log_event(
            pool,
            "THUMBNAIL_GENERATION_ERROR",
            "Failed to complete thumbnail generation process",
            "ERROR",
            error=e
        )
        raise

async def main():
    try:
        pool = await aiomysql.create_pool(**DB_CONFIG)
        runware = RunwareSDK(RUNWARE_API_KEY)
        
        await log_event(pool, "SCRIPT_START", "Starting thumbnail generation script")
        
        await generate_thumbnails(pool, runware)
        
        await log_event(pool, "SCRIPT_COMPLETE", "Thumbnail generation script completed successfully")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
    finally:
        pool.close()
        await pool.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())