import os
import sys
import logging
from consolidated_api import app

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    logger.info("Starting consolidated API server...")
    logger.info("API endpoints available at http://localhost:5005/")

    # Run the Flask app on port 5005
    app.run(debug=True, host='0.0.0.0', port=5005)
