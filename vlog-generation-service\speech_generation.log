2025-03-01 13:02:18,456 - INFO - <PERSON><PERSON><PERSON>_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:02:18,861 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Josh' not found.
2025-03-01 13:02:18,870 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Josh' not found.
2025-03-01 13:04:15,036 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:04:16,608 - ERROR - SPEECH_SAVE_ERROR: Failed to save speech content
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 60, in save_speech_content
    await cursor.execute(
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'vlog_generator_service.speech_content' doesn't exist")
2025-03-01 13:04:16,693 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 106, in generate_speech
    speech_id = await save_speech_content(pool, content_id, file_path, duration)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 60, in save_speech_content
    await cursor.execute(
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'vlog_generator_service.speech_content' doesn't exist")
2025-03-01 13:04:16,698 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 106, in generate_speech
    speech_id = await save_speech_content(pool, content_id, file_path, duration)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 60, in save_speech_content
    await cursor.execute(
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'vlog_generator_service.speech_content' doesn't exist")
2025-03-01 13:06:50,639 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:06:52,258 - INFO - SPEECH_GENERATION_SUCCESS: Speech generated and saved with ID: 1
2025-03-01 13:15:44,294 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:15:44,299 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
TypeError: generate() got an unexpected keyword argument 'voice_settings'
2025-03-01 13:15:44,303 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
TypeError: generate() got an unexpected keyword argument 'voice_settings'
2025-03-01 13:16:27,713 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:16:42,384 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Mark' not found.
2025-03-01 13:16:42,396 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Mark' not found.
2025-03-01 13:17:30,077 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:17:30,441 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Andrew - Smooth audio books' not found.
2025-03-01 13:17:30,447 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Andrew - Smooth audio books' not found.
2025-03-01 13:21:58,016 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:21:58,489 - ERROR - SPEECH_GENERATION_ERROR: Failed to generate speech for content ID: 1
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Drew' not found.
2025-03-01 13:21:58,494 - ERROR - Main execution failed
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 136, in main
    result = await generate_speech(pool, content_id=1, text=test_text)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\speech-generation.py", line 91, in generate_speech
    audio = generate(
            ^^^^^^^^^
  File "C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\.venv\Lib\site-packages\elevenlabs\simple.py", line 56, in generate
    raise ValueError(f"Voice '{voice_str}' not found.")
ValueError: Voice 'Drew' not found.
2025-03-01 13:22:51,993 - INFO - SPEECH_GENERATION_START: Starting speech generation for content ID: 1
2025-03-01 13:22:53,689 - INFO - SPEECH_GENERATION_SUCCESS: Speech generated and saved with ID: 2
