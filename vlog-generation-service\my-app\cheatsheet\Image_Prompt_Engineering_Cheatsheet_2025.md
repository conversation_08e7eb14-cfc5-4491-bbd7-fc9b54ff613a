# 🎨 Prompt Engineering Cheatsheet for AI Image Generation (Scene, Mood & Emotion Focused) – 2025 Edition

A refined guide built with 10x iterative logic to help you craft high-quality prompts for generating powerful, emotional, and visually accurate AI-generated images. Ideal for visual storytellers, artists, marketers, and content creators.

---

## 🧠 Core Formula: S.M.A.R.T.A.R.T

> **S.M.A.R.T.A.R.T = Subject + Mood + Action + Realism + Time + Atmosphere + Rendering + Tone**

---

## 🔁 Use This Repetition Loop (10x Method)

1. Start with a basic idea
2. Add **Subject**
3. Layer in **Mood**
4. Describe the **Scene/Action**
5. Add **Time/Lighting**
6. Define the **Style or Render Type**
7. Add **Camera Details** (if needed)
8. Refine with **Emotion or Symbolism**
9. Ensure **Consistency & Focus**
10. Expand or simplify depending on result

---

## 🧩 Prompt Blueprint

```
"A [subject] is [doing something] in a [setting]. The atmosphere feels [mood/emotion], with [lighting/time of day] and [color palette]. Style: [art style or medium]. Rendered in [resolution or engine]."
```

---

## 🎭 Emotion Keywords

| Emotion       | Descriptive Prompts |
|---------------|---------------------|
| Joy           | golden sunlight, wide smile, playful shadows |
| Sadness       | overcast sky, tearful expression, soft blue tones |
| Fear          | dim light, harsh shadows, eerie stillness |
| Wonder        | glowing orbs, sparkles, starry sky, wide-eyed look |
| Peace         | misty sunrise, still water, serene forest |

---

## 🌇 Scene Starters

- “A lone traveler walks through a foggy forest at dawn…”
- “Two lovers stare at the stars from a rooftop in Tokyo…”
- “A child reaches for a balloon floating into the stormy sky…”

---

## 🖌 Mood & Visual Texture

| Mood          | Visual Description |
|---------------|--------------------|
| Nostalgic     | sepia tone, faded colors, retro lighting |
| Hopeful       | bright light rays, soft edges, bloom effects |
| Melancholic   | rainy window, grayscale palette, lonely setting |
| Energetic     | vibrant colors, motion blur, high contrast |
| Romantic      | candlelight, soft focus, warm hues |

---

## 📷 Camera Effects (Optional Enhancements)

- **Close-up** — focus on emotion/eyes
- **Wide shot** — full scene/mood
- **Tilted angle** — instability, chaos
- **Cinematic ratio (16:9)** — epic feel
- **Depth of field** — realism, focus

---

## 🛠 Final Examples

1. **“A girl in a red dress dancing alone on a rainy street at night. Neon lights reflect off the puddles. Mood: nostalgic. Cinematic lighting. Style: digital painting.”**

2. **“An astronaut staring at Earth from a broken spaceship. The feeling is isolation and awe. Render in photorealism, with cold blue lighting.”**

3. **“A fox in a snowy forest during twilight. Peaceful mood. Pastel colors. Style: watercolor.”**

---

## 💡 Tips

- Be specific with **scene composition**
- Choose **color palette** to match emotion
- Always test and refine prompt after first output
- You can stack **multiple moods**: “hopeful yet lonely”

---

## 📦 License

MIT — Adapt, remix, and deploy with creativity.

---

## 👨‍🎨 Optimized For

MidJourney, DALL·E, Firefly, Leonardo AI, Stable Diffusion, and any advanced AI image model.

Created by **PromptGPT AI Repetition Framework™**
