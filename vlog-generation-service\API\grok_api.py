import os
import logging
import json
import requests
import traceback
from flask import Blueprint, jsonify, request
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API setup
XAI_API_KEY = os.getenv('XAI_API_KEY')
GROK_API_URL = "https://api.x.ai/v1/chat/completions"

# Configure detailed logging
logger.info(f"XAI_API_KEY configured: {XAI_API_KEY is not None}")
logger.info(f"GROK_API_URL: {GROK_API_URL}")

# Create Blueprint
grok_bp = Blueprint('grok', __name__, url_prefix='/api/grok')

@grok_bp.route('/generate-content', methods=['POST'])
def generate_content():
    """Generate content using Grok AI based on Claude format parameters"""
    logger.info("Received request to generate content with Grok")

    if not XAI_API_KEY:
        logger.error("XAI_API_KEY not configured")
        return jsonify({'error': 'Grok API key not configured'}), 500

    logger.info(f"Using XAI_API_KEY: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")

    try:
        data = request.json
        logger.info(f"Received data: {data}")

        topic = data.get('topic', '')
        content_type = data.get('content_type', '')
        purpose = data.get('purpose', '')
        audience = data.get('audience', '')
        tone = data.get('tone', '')
        style = data.get('style', '')
        word_count = data.get('word_count', '')
        persona = data.get('persona', '')

        logger.info(f"Processing request - Topic: {topic}, Content Type: {content_type}")

        if not topic:
            logger.warning("Topic is required but not provided")
            return jsonify({'error': 'Topic is required'}), 400

        # Create prompt for Grok
        prompt = f"""
        Generate content about "{topic}" for a {content_type}.

        Purpose: {purpose}
        Target Audience: {audience}
        Tone: {tone}
        Style: {style}
        Word Count: {word_count}
        Writing Persona: {persona}

        Return the response in this exact JSON format:
        {{
          "response": {{
            "content_prompt": "The main content to be used as a prompt (formatted with markdown headings and paragraphs)",
            "call_to_action": "A compelling call to action that specifically asks viewers to like the video, subscribe to the channel, and engage with the content. Make it persuasive and relevant to the topic."
          }}
        }}
        """

        # Call Grok API
        logger.info(f"Calling Grok API at {GROK_API_URL}")
        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "grok-2-vision-latest",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 2000
        }

        logger.info(f"Payload structure prepared (not showing full content)")

        try:
            logger.info("Sending request to Grok API...")
            response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=60)
            logger.info(f"Received response with status code: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Grok API error: {error_text}")
                return jsonify({'error': f'Failed to generate content with Grok: {error_text}'}), 500
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception: {str(e)}")
            return jsonify({'error': f'Network error when calling Grok API: {str(e)}'}), 500

        # Process the response
        try:
            response_json = response.json()
            logger.info("Successfully received JSON response from Grok API")

            # Extract the generated text
            generated_text = response_json["choices"][0]["message"]["content"]
            logger.info(f"Generated text length: {len(generated_text)}")

            # Clean the response text
            generated_text = generated_text.strip()
            if generated_text.startswith('```json'):
                generated_text = generated_text[7:]  # Remove ```json prefix
            if generated_text.endswith('```'):
                generated_text = generated_text[:-3]  # Remove ``` suffix

            # Parse the JSON
            try:
                response_data = json.loads(generated_text)
                logger.info("Successfully parsed JSON from response")
            except json.JSONDecodeError:
                # Try to extract JSON using regex if direct parsing fails
                import re
                json_match = re.search(r'\{\s*"response".*\}\s*\}', generated_text, re.DOTALL)
                if json_match:
                    try:
                        response_data = json.loads(json_match.group(0))
                        logger.info("Successfully extracted JSON using regex")
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse extracted JSON: {str(e)}")
                        return jsonify({'error': f'Failed to parse Grok response: {str(e)}'}), 500
                else:
                    logger.error("Could not extract JSON from response")
                    return jsonify({'error': 'Failed to extract JSON from Grok response'}), 500

            # Extract the content
            if "response" in response_data:
                response_content = response_data["response"]
                content_prompt = response_content.get("content_prompt", "")
                call_to_action = response_content.get("call_to_action", "")

                logger.info("Successfully extracted content from Grok response")

                return jsonify({
                    'content_prompt': content_prompt,
                    'call_to_action': call_to_action
                })
            else:
                logger.error("Response does not contain 'response' key")
                return jsonify({'error': 'Invalid response format from Grok API'}), 500

        except Exception as e:
            logger.error(f"Error processing Grok response: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error processing Grok response: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"Unexpected error in generate_content: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@grok_bp.route('/test', methods=['GET'])
def test_endpoint():
    """Simple test endpoint to verify the blueprint is working"""
    return jsonify({'message': 'Grok API blueprint is working!'}), 200

@grok_bp.route('/status', methods=['GET'])
def check_status():
    """Check if Grok API is configured and available"""
    logger.info("Status endpoint called")
    if not XAI_API_KEY:
        logger.info("XAI_API_KEY not configured")
        return jsonify({'available': False, 'reason': 'API key not configured'}), 200

    logger.info("XAI_API_KEY is configured, returning available=true")
    return jsonify({'available': True}), 200

@grok_bp.route('/generate-prompt', methods=['POST'])
def generate_prompt():
    """Generate content prompt using Grok AI"""
    logger.info("Received request to generate prompt with Grok")

    if not XAI_API_KEY:
        logger.error("XAI_API_KEY not configured")
        return jsonify({'error': 'Grok API key not configured'}), 500

    logger.info(f"Using XAI_API_KEY: {XAI_API_KEY[:5]}...{XAI_API_KEY[-5:] if len(XAI_API_KEY) > 10 else ''}")

    try:
        data = request.json
        logger.info(f"Received data: {data}")

        title = data.get('title', '')
        category = data.get('category', '')
        description = data.get('description', '')

        logger.info(f"Processing request - Title: {title}, Category: {category}")

        if not title:
            logger.warning("Title is required but not provided")
            return jsonify({'error': 'Title is required'}), 400

        # Create prompt for Grok - using a structured JSON format similar to the existing implementation
        prompt = f"""
        Return only a JSON object. Generate a content prompt for a vlog about "{title}".

        Category: {category}
        Additional context: {description}

        Return the response in this exact JSON format:
        {{
          "response": {{
            "Scenario": "A clear and engaging main topic about {title}",
            "Empathetic Advice": "An empathetic perspective on the topic (3-5 sentences)",
            "Practical Advice": "Practical guidance related to the topic (3-5 sentences)",
            "Call To Action": "A compelling call to action that specifically asks viewers to like the video, subscribe to the channel, and engage with the content. Make it persuasive and relevant to the topic.",
            "Thumbnail Image Prompt": "A detailed description for a thumbnail image that would work well for this content"
          }}
        }}
        """

        # Call Grok API
        logger.info(f"Calling Grok API at {GROK_API_URL}")
        headers = {
            "Authorization": f"Bearer {XAI_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "grok-2-vision-latest",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 1000
        }

        logger.info(f"Payload structure prepared (not showing full content)")

        try:
            logger.info("Sending request to Grok API...")
            response = requests.post(GROK_API_URL, headers=headers, json=payload, timeout=30)
            logger.info(f"Received response with status code: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Grok API error: {error_text}")
                return jsonify({'error': f'Failed to generate content with Grok: {error_text}'}), 500
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception: {str(e)}")
            return jsonify({'error': f'Network error when calling Grok API: {str(e)}'}), 500

        # Process the response
        try:
            data = response.json()
            generated_text = data["choices"][0]["message"]["content"]
            logger.info(f"Raw response received, length: {len(generated_text)} characters")

            # Clean the response text
            generated_text = generated_text.strip()
            if generated_text.startswith('```json'):
                generated_text = generated_text[7:]  # Remove ```json prefix
            elif generated_text.startswith('```'):
                generated_text = generated_text[3:]  # Remove ``` prefix

            if generated_text.endswith('```'):
                generated_text = generated_text[:-3]  # Remove ``` suffix

            # Parse the JSON response
            try:
                response_json = json.loads(generated_text)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON. Raw text: {generated_text[:100]}...")
                logger.error(f"JSON parse error: {str(e)}")
                return jsonify({'error': f'Invalid JSON response from Grok API: {str(e)}'}), 500

            # Validate response structure
            if "response" not in response_json:
                logger.error("Missing 'response' key in JSON")
                return jsonify({'error': 'Invalid response format from Grok API'}), 500

            # Extract the content
            response_data = response_json["response"]
            scenario = response_data.get("Scenario") or response_data.get("scenario", "")
            empathetic_advice = response_data.get("Empathetic Advice") or response_data.get("empathetic_advice", "")
            practical_advice = response_data.get("Practical Advice") or response_data.get("practical_advice", "")
            call_to_action = response_data.get("Call To Action") or response_data.get("call_to_action", "")
            thumbnail_prompt = response_data.get("Thumbnail Image Prompt") or response_data.get("thumbnail_prompt", "")

            # Combine for content prompt (excluding call to action)
            content_prompt = f"""# {scenario}

## Empathetic Perspective:
{empathetic_advice}

## Practical Guidance:
{practical_advice}"""

            logger.info("Successfully extracted content from Grok response")

            return jsonify({
                'content_prompt': content_prompt,
                'call_to_action': call_to_action,  # Using the specific call to action
                'title': title,
                'scenario': scenario,
                'empathetic_advice': empathetic_advice,
                'practical_advice': practical_advice,
                'thumbnail_prompt': thumbnail_prompt
            })

        except Exception as e:
            logger.error(f"Error processing Grok response: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error processing Grok response: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"Unexpected error in generate_prompt: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500
