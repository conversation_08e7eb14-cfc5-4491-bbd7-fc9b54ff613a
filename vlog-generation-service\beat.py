"""
Celery beat for the VisionFrame AI queue system.
This script starts the Celery beat process for periodic tasks.
"""

import os
import logging
from celery_config import celery_app
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    logger.info("Starting VisionFrame AI beat scheduler...")
    
    # Start the beat scheduler
    celery_app.start(
        argv=[
            'beat',
            '--loglevel=INFO',
            '-s', 'celerybeat-schedule',  # Schedule file
        ]
    )
