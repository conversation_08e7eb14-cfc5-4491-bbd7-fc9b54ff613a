import os
from dotenv import load_dotenv
import asyncio
import aiohttp
import aiomysql
import logging
import json
from runware import Runware, IImageInference  # Import Runware SDK
import platform
import time
from datetime import datetime
import traceback
from urllib.parse import urlparse
import requests
from elevenlabs import generate, Voice, VoiceSettings
import re


# Get the directory of the current script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# API Keys

# Load environment variables from .env
load_dotenv()

# Configure logging
def setup_logging():
    """Configure logging settings"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler('vlog_generator.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# Database configuration from .env
DB_CONFIG = {
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'db': os.getenv('DB_DATABASE'),
    'autocommit': True
}

# API setup
XAI_API_KEY = os.getenv('XAI_API_KEY')
GROK_API_URL = "https://api.x.ai/v1/chat/completions"
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")

if not RUNWARE_API_KEY:
    raise ValueError("RUNWARE_API_KEY is missing! Check your .env file.")

# Simple token counter (approximation)
def count_tokens(text):
    return len(text) // 4

# Asynchronous log event function
async def log_event(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Enhanced logging function with detailed information"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Prepare detailed log message
    log_details = {
        "timestamp": timestamp,
        "event_type": event_type,
        "status": status,
        "message": message
    }
    
    if error:
        log_details["error"] = str(error)
        log_details["traceback"] = traceback.format_exc()
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", extra=log_details)
    else:
        logger.info(f"{event_type}: {message}", extra=log_details)
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log event to database: {e}")

# Check if a scenario exists in the database
async def scenario_exists(pool, scenario):
    """Check asynchronously if a scenario already exists."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT COUNT(*) FROM generated_content WHERE scenario = %s",
                (scenario,)
            )
            count = (await cursor.fetchone())[0]
            return count > 0

# Save content to the database
async def save_content(pool, input_text, scenario, empathetic_advice, practical_advice, thumbnail_prompt, thumbnail_url=None):
    """Save generated content and token usage asynchronously."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # Insert into generated_content table
                await cursor.execute(
                    """
                    INSERT INTO generated_content (
                        input_text, 
                        scenario,
                        empathetic_advice,
                        practical_advice,
                        thumbnail_prompt,
                        thumbnail_url,
                        created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, NOW())
                    """,
                    (
                        input_text,
                        scenario,
                        empathetic_advice,
                        practical_advice,
                        thumbnail_prompt,
                        thumbnail_url
                    )
                )
                content_id = cursor.lastrowid

                # Calculate token usage for tracking
                input_tokens = count_tokens(input_text)
                output_tokens = count_tokens(
                    f"{scenario}{empathetic_advice}{practical_advice}{thumbnail_prompt}"
                )

                # Insert token usage
                await cursor.execute(
                    """
                    INSERT INTO token_usage (
                        content_id, 
                        input_tokens, 
                        output_tokens
                    ) VALUES (%s, %s, %s)
                    """,
                    (content_id, input_tokens, output_tokens)
                )
                
                await conn.commit()
                logging.info(f"Content saved with ID {content_id}")
                
                await log_event(
                    pool,
                    "CONTENT_SAVED",
                    f"Successfully saved content with ID {content_id}",
                    "INFO"
                )
                
                return content_id

            except Exception as e:
                await conn.rollback()
                await log_event(
                    pool,
                    "CONTENT_SAVE_ERROR",
                    "Failed to save content",
                    "ERROR",
                    error=e
                )
                raise

# Save image prompts to the database
async def save_image_prompts(pool, content_id, image_prompts):
    """Save image prompts asynchronously."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            for prompt in image_prompts:
                await cursor.execute(
                    "INSERT INTO image_prompts (content_id, image_prompt) VALUES (%s, %s)",
                    (content_id, prompt)
                )
            await conn.commit()


# Retrieve image prompts from the database
async def get_image_prompts(pool, content_id):
    """Retrieve image prompts for a given content_id."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT id, image_prompt FROM image_prompts WHERE content_id = %s",
                (content_id,)
            )
            rows = await cursor.fetchall()
            return [{'id': row[0], 'image_prompt': row[1]} for row in rows]

# Update image prompt with generated URL and timestamp
async def update_image_prompt_url(pool, prompt_id, url):
    """Update the image prompt with the generated image URL and timestamp and download the image."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # Update database with URL and timestamp
                await cursor.execute(
                    "UPDATE image_prompts SET url = %s, url_generated_at = NOW() WHERE id = %s",
                    (url, prompt_id)
                )
                await conn.commit()

                filename = f"{prompt_id}.jpg"  # Using prompt_id as filename
                
                download_result = await download_image(
                    pool=pool,
                    image_url=url,
                    filename=filename
                )

                if download_result:
                    await log_event(
                        pool,
                        "IMAGE_UPDATE_SUCCESS",
                        f"Updated and downloaded image for prompt {prompt_id}",
                        "INFO"
                    )
                else:
                    await log_event(
                        pool,
                        "IMAGE_DOWNLOAD_FAILED",
                        f"Updated URL but failed to download image for prompt {prompt_id}",
                        "WARNING"
                    )

            except Exception as e:
                await log_event(
                    pool,
                    "IMAGE_UPDATE_ERROR",
                    f"Failed to update/download image for prompt {prompt_id}",
                    "ERROR",
                    error=e
                )
                raise

# Generate images using Runware API
async def generate_images(pool, content_id, runware):
    """Generate images for all prompts associated with content_id."""
    start_time = time.time()
    
    await log_event(
        pool,
        "IMAGE_GENERATION",
        f"Starting image generation for content ID: {content_id}",
        "INFO"
    )
    
    try:
        prompts = await get_image_prompts(pool, content_id)
        successful_generations = 0
        
        for idx, prompt in enumerate(prompts, 1):
            await log_event(
                pool,
                "SINGLE_IMAGE_GENERATION",
                f"Generating image {idx}/{len(prompts)} for prompt ID: {prompt['id']}",
                "INFO"
            )
            
            try:
                request_image = IImageInference(
                    positivePrompt=prompt['image_prompt'],
                    negativePrompt="blurry, low quality, deformed",
                    model="runware:101@1",
                    numberResults=1,
                    height=2048,
                    width=1152,
                    CFGScale=7.5,
                    
                )
                
                images = await runware.imageInference(requestImage=request_image)
                image_url = images[0].imageURL
                
                await update_image_prompt_url(pool, prompt['id'], image_url)
                
                await log_event(
                    pool,
                    "SINGLE_IMAGE_SUCCESS",
                    f"Generated image for prompt {prompt['id']}: {image_url}",
                    "INFO"
                )
                
            except Exception as e:
                await log_event(
                    pool,
                    "SINGLE_IMAGE_ERROR",
                    f"Failed to generate image for prompt {prompt['id']}",
                    "ERROR",
                    error=e
                )
        
        execution_time = time.time() - start_time
        await log_event(
            pool,
            "IMAGE_GENERATION_COMPLETE",
            f"Completed generating all images in {execution_time:.2f} seconds",
            "INFO"
        )
        
    except Exception as e:
        await log_event(
            pool,
            "IMAGE_GENERATION_ERROR",
            "Failed to complete image generation process",
            "ERROR",
            error=e
        )
        raise

# Generate vlog content using Grok API
async def generate_vlog_content(pool, prompt):
    """Generate vlog content asynchronously using Grok API."""
    start_time = time.time()
    
    await log_event(
        pool,
        "CONTENT_GENERATION",
        f"Starting content generation with prompt length: {len(prompt)} characters",
        "INFO"
    )
    
    try:
        async with aiohttp.ClientSession() as session:
            await log_event(pool, "API_REQUEST", "Initiating Grok API request", "INFO")
            
            headers = {
                "Authorization": f"Bearer {XAI_API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "grok-2-vision-latest",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 1000
            }
            
            async with session.post(GROK_API_URL, headers=headers, json=payload) as response:
                await log_event(
                    pool,
                    "API_RESPONSE",
                    f"Received response from Grok API. Status: {response.status}",
                    "INFO"
                )
                
                response.raise_for_status()
                data = await response.json()
                generated_text = data["choices"][0]["message"]["content"]
                
                # Log the raw response for debugging
                await log_event(
                    pool,
                    "RAW_RESPONSE",
                    f"Raw API response: {generated_text}",
                    "INFO"
                )
                
                # Clean the response text
                generated_text = generated_text.strip()
                if generated_text.startswith('```json'):
                    generated_text = generated_text[7:]  # Remove ```json prefix
                if generated_text.endswith('```'):
                    generated_text = generated_text[:-3]  # Remove ``` suffix
                
                try:
                    response_json = json.loads(generated_text)
                except json.JSONDecodeError as e:
                    await log_event(
                        pool,
                        "JSON_PARSE_ERROR",
                        f"Failed to parse JSON. Raw text: {generated_text}",
                        "ERROR"
                    )
                    raise ValueError(f"Invalid JSON response from API: {generated_text}")

                # Validate response structure
                if "response" not in response_json:
                    raise ValueError("Missing 'response' key in JSON")
                
                # Update keys to match the actual response
                response_data = response_json["response"]
                scenario = response_data.get("Scenario") or response_data.get("scenario")
                empathetic_advice = response_data.get("Empathetic Advice") or response_data.get("empathetic_advice")
                practical_advice = response_data.get("Practical Advice") or response_data.get("practical_advice")
                thumbnail_prompt = response_data.get("Thumbnail Image Prompt") or response_data.get("thumbnail_prompt")

                # Validate all required fields are present
                if not all([scenario, empathetic_advice, practical_advice, thumbnail_prompt]):
                    missing = [k for k, v in {
                        "scenario": scenario,
                        "empathetic_advice": empathetic_advice,
                        "practical_advice": practical_advice,
                        "thumbnail_prompt": thumbnail_prompt
                    }.items() if not v]
                    raise ValueError(f"Missing required keys in response: {missing}")

                execution_time = time.time() - start_time
                await log_event(
                    pool,
                    "GENERATION_SUCCESS",
                    f"Content generated successfully in {execution_time:.2f} seconds",
                    "INFO"
                )
                
                return scenario, empathetic_advice, practical_advice, thumbnail_prompt
                
    except json.JSONDecodeError as e:
        await log_event(
            pool,
            "PARSING_ERROR",
            f"Failed to parse API response as JSON. Error: {str(e)}",
            "ERROR",
            error=e
        )
        raise
    except aiohttp.ClientError as e:
        await log_event(
            pool,
            "API_ERROR",
            f"API request failed: {str(e)}",
            "ERROR",
            error=e
        )
        raise
    except Exception as e:
        await log_event(
            pool,
            "GENERATION_ERROR",
            f"Unexpected error during content generation: {str(e)}",
            "ERROR",
            error=e
        )
        raise

# Generate image prompts based on advice
async def generate_image_prompts(pool, advice):
    """Generate image prompts asynchronously using Grok API."""
    start_time = time.time()
    
    await log_event(
        pool,
        "IMAGE_PROMPT_GENERATION",
        f"Starting image prompt generation for advice length: {len(advice)} characters",
        "INFO"
    )
    
    try:
        prompt = construct_image_prompt(advice)
        
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {XAI_API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "grok-2-vision-latest",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 2000  # Increased token limit
            }
            
            async with session.post(GROK_API_URL, headers=headers, json=payload) as response:
                response.raise_for_status()
                data = await response.json()
                generated_text = data["choices"][0]["message"]["content"]
                
                # Extract quoted content using regex
                quoted_content = re.findall(r'"([^"\\]*(?:\\.[^"\\]*)*)"', generated_text)
                
                # Validate and process prompts
                valid_prompts = []
                for content in quoted_content:
                    cleaned = content.strip()
                    # Skip if it's a structural keyword or empty
                    if (cleaned and 
                        len(cleaned) >= 10 and 
                        len(cleaned) <= 3000 and 
                        "image_prompts" not in cleaned.lower()):
                        valid_prompts.append(cleaned)
                
                # Limit to 6 prompts
                valid_prompts = valid_prompts[:6]
                
                if not valid_prompts:
                    await log_event(
                        pool,
                        "PROMPT_VALIDATION_WARNING",
                        "No valid prompts found in response",
                        "WARNING"
                    )
                    return []

                execution_time = time.time() - start_time
                await log_event(
                    pool,
                    "IMAGE_PROMPT_SUCCESS",
                    f"Generated {len(valid_prompts)} valid prompts in {execution_time:.2f} seconds",
                    "INFO"
                )
                
                return valid_prompts
                
    except Exception as e:
        await log_event(
            pool,
            "IMAGE_PROMPT_ERROR",
            f"Failed to generate image prompts: {str(e)}",
            "ERROR",
            error=e
        )
        return []

def construct_image_prompt(advice):
    """Construct a more structured prompt for image generation."""
    return (
        f"Based on this advice text: '{advice}', "
        f"generate 6 very detailed image prompts for visual representation. "
        f"Each prompt should be a complete, properly terminated string with no line breaks. "
        f"No text should appear in the images. Use these camera settings:\n"
        f"- Camera: Canon EOS R5 or Nikon Z9\n"
        f"- Lens: 85mm f/1.8\n"
        f"- Aperture: f/2.2\n"
        f"- Shutter Speed: 1/500s\n"
        f"- ISO: 100\n"
        f"- Lighting: Natural golden hour lighting\n\n"
        f"Return only a JSON object with the following structure:\n"
        f'{{\n'
        f'  "image_prompts": [\n'
        f'    "A detailed scene description with camera settings.",\n'
        f'    "Another detailed scene description with camera settings.",\n'
        f'    "A third detailed scene description with camera settings.",\n'
        f'    "A fourth detailed scene description with camera settings.",\n'
        f'    "A fifth detailed scene description with camera settings.",\n'
        f'    "A sixth detailed scene description with camera settings."\n'
        f'  ]\n'
        f'}}'
    )
async def download_image(pool, image_url, filename):
    """
    Downloads an image from the provided URL and saves it to the specified directory with the given filename.
    
    Args:
        pool: Database connection pool
        image_url (str): The URL of the image to download
        filename (str): The filename to save the image as (including extension)
    
    Returns:
        bool: True if download and save were successful, False otherwise
    """
    try:
        save_directory = "content-images"  # Moved here
        logger.info(f"Starting image download from URL: {image_url}")
        await log_event(pool, "IMAGE_DOWNLOAD_START", f"Starting download from URL: {image_url}")
        
        # Remove any leading slashes and make path relative to script directory
        save_directory = save_directory.lstrip('/')
        full_directory_path = os.path.join(SCRIPT_DIR, save_directory)
        
        # Create the directory if it doesn't exist
        os.makedirs(full_directory_path, exist_ok=True)
        logger.info(f"Directory verified/created: {full_directory_path}")
        await log_event(pool, "DIRECTORY_CREATE", f"Directory verified/created: {full_directory_path}")
        
        # Get the file extension from the URL if not in the filename
        if '.' not in filename:
            parsed_url = urlparse(image_url)
            original_filename = os.path.basename(parsed_url.path)
            if '.' in original_filename:
                extension = original_filename.split('.')[-1]
                filename = f"{filename}.{extension}"
                logger.info(f"Added extension to filename: {filename}")
                await log_event(pool, "FILENAME_UPDATE", f"Added extension to filename: {filename}")
        
        # Full path where the image will be saved
        save_path = os.path.join(full_directory_path, filename)
        logger.info(f"Full save path: {save_path}")
        await log_event(pool, "SAVE_PATH", f"Full save path: {save_path}")
        
        # Download the image
        logger.info("Initiating download request")
        await log_event(pool, "DOWNLOAD_REQUEST", "Initiating download request")
        
        response = requests.get(image_url, stream=True)
        response.raise_for_status()  # Raise an exception for HTTP errors
        
        # Save the image
        logger.info("Starting to write image to file")
        await log_event(pool, "FILE_WRITE_START", "Starting to write image to file")
        
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        
        logger.info(f"Image successfully downloaded and saved to {save_path}")
        await log_event(
            pool, 
            "DOWNLOAD_SUCCESS", 
            f"Image successfully downloaded and saved to {save_path}"
        )
        return True
    
    except requests.exceptions.RequestException as e:
        error_msg = f"Error downloading image: {e}"
        logger.error(error_msg)
        await log_event(pool, "DOWNLOAD_ERROR", error_msg, "ERROR", error=e)
        return False
    except IOError as e:
        error_msg = f"Error saving image: {e}"
        logger.error(error_msg)
        await log_event(pool, "SAVE_ERROR", error_msg, "ERROR", error=e)
        return False


async def log_event(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Log to file
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    # Log to database
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def save_speech_content(pool, content_id: int, file_path: str, duration: float):
    """Save speech generation details to database"""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO speech_content (content_id, file_path, duration, created_at) "
                    "VALUES (%s, %s, %s, NOW())",
                    (content_id, file_path, duration)
                )
                return cursor.lastrowid
    except Exception as e:
        await log_event(pool, "SPEECH_SAVE_ERROR", "Failed to save speech content", "ERROR", e)
        raise

async def generate_speech(pool, content_id: int, text: str, save_directory: str = "content-speech"):
    """
    Generate speech from text and save it to file system and database
    
    Args:
        pool: Database connection pool
        content_id: ID of the content to generate speech for
        text: Text to convert to speech
        save_directory: Directory to save audio files
    """
    try:
        # Ensure save directory exists
        os.makedirs(save_directory, exist_ok=True)
        
        await log_event(pool, "SPEECH_GENERATION_START", f"Starting speech generation for content ID: {content_id}")
        
        # Generate filename
        filename = f"speech_{content_id}.mp3"
        file_path = os.path.join(save_directory, filename)
        
        # Generate speech
        audio = generate(
            api_key=ELEVENLABS_API_KEY,
            text=text,
            voice="George",
            model="eleven_monolingual_v1"
        )
        
        # Save audio file
        with open(file_path, 'wb') as f:
            f.write(audio)
        
        # Get audio duration (placeholder - you might want to use a proper audio library to get actual duration)
        duration = len(text) / 15  # Rough estimation: 15 characters per second
        
        # Save to database
        speech_id = await save_speech_content(pool, content_id, file_path, duration)
        
        await log_event(
            pool,
            "SPEECH_GENERATION_SUCCESS",
            f"Speech generated and saved with ID: {speech_id}"
        )
        
        return {
            'speech_id': speech_id,
            'file_path': file_path,
            'duration': duration
        }
        
    except Exception as e:
        await log_event(
            pool,
            "SPEECH_GENERATION_ERROR",
            f"Failed to generate speech for content ID: {content_id}",
            "ERROR",
            e
        )
        raise

# Main workflow with retry logic and image generation
async def main():
    """Main async function to orchestrate content, image prompt generation, and image generation."""
    pool = await aiomysql.create_pool(**DB_CONFIG)
    runware = Runware(api_key=RUNWARE_API_KEY)
    await runware.connect()
    try:
        sentence = "5"
        # prompt = (
        #     f"Return only a JSON object. generate 1 scenarios where most people are having issue with love life, present it in a table together with an empathic({sentence} sentences) and practical advice({sentence} sentences) then the  image prompt to create A thumbnail with a bold, attention-grabbing design in the fourth column based on the issue. this is a sample image prompt : A thumbnail with a bold, attention-grabbing design ,The background is a dark gradient with a slight glow effect. The text in the image reads: ""the issue"" in large, bold, uppercase white and yellow letters. Below it, a smaller subtitle reads: 'Make Consistent Profits!' in white. On the right side, there is an image of a rising stock chart with green candles and a red downward trendline. A man in a suit is pointing at the chart with a confident expression. The composition is clear and professional, ensuring the text is sharp and fully visible. Use this structure:\n"
        #     f"{{\n"
        #     f"  \"response\": {{\n"
        #     f"    \"Scenario\": \"Feeling stuck in a loveless relationship\",\n"            
        #     f"    \"Empathetic Advice\": \"It’s so hard when love feels like a duty instead of a joy, isn’t it? You’re not alone in wondering if this is all there is. I know you’re craving something more fulfilling. It’s okay to feel trapped and scared to change. Let’s find a way to bring light back into your life.\"\n"
        #     f"    \"Practical Advice\": \"Take time to reflect on what you truly want—happiness or just comfort? Talk to your partner honestly about your feelings. Start rediscovering your own interests outside the relationship. Seek a counselor if you’re unsure about next steps. Decide if staying or leaving aligns with your future.\"\n"
        #     f"    \"Thumbnail Image Prompt\": \"A thumbnail with a bold, attention-grabbing design. The background is a dark gradient with a muted gray glow effect. The text reads: ""LOVELESS TRAP?"" in large, bold, uppercase white and gray letters. Below, a smaller subtitle reads: ""Find Your Joy Again!"" in white. On the right, a figure stands in a dim cage with a faint heart breaking apart, composition clear and sharp.\"\n"                        
        #     f"  }}\n"
        #     f"}}"
        # )

        prompt=(f"Return only a JSON object. Generate 20 scenarios where most people struggle with their love life. Present them as an array, each containing:\n"
        f"- `Scenario`: The love-related issue.\n"
        f"- `Empathetic Advice`: ({sentence} sentences) A compassionate response that acknowledges the emotional struggle.\n"
        f"- `Practical Advice`: ({sentence} sentences) Actionable guidance on how to handle the situation.\n"
        f"- `Thumbnail Image Prompt`: A structured description for an eye-catching thumbnail based on the issue.\n"
        f"\n"
        f"Example JSON format:\n"
        f"{{\n"
        f"  \"response\": [\n"
        f"    {{\n"
        f"      \"Scenario\": \"Feeling stuck in a loveless relationship\",\n"
        f"      \"Empathetic Advice\": \"It’s so hard when love feels like a duty instead of a joy, isn’t it? You’re not alone in wondering if this is all there is. I know you’re craving something more fulfilling. It’s okay to feel trapped and scared to change. Let’s find a way to bring light back into your life.\",\n"
        f"      \"Practical Advice\": \"Take time to reflect on what you truly want—happiness or just comfort? Talk to your partner honestly about your feelings. Start rediscovering your own interests outside the relationship. Seek a counselor if you’re unsure about next steps. Decide if staying or leaving aligns with your future.\",\n"
        f"      \"Thumbnail Image Prompt\": \"A thumbnail with a bold, attention-grabbing design. The background is a dark gradient with a muted gray glow effect. The text reads: 'LOVELESS TRAP?' in large, bold, uppercase white and gray letters. Below, a smaller subtitle reads: 'Find Your Joy Again!' in white. On the right, a figure stands in a dim cage with a faint heart breaking apart, composition clear and sharp.\"\n"
        f"    }},\n"
        f"    {{\n"
        f"      \"Scenario\": \"Being ghosted after a deep connection\",\n"
        f"      \"Empathetic Advice\": \"I know how confusing and painful it is to be left without closure. You gave your heart, and now you're left with silence. You deserve better than uncertainty. You are not alone in this hurt.\",\n"
        f"      \"Practical Advice\": \"Resist the urge to seek answers from someone who chose to disappear. Focus on self-healing and understanding that their actions are a reflection of them, not you. Lean on friends and self-care to regain your confidence and move forward.\",\n"
        f"      \"Thumbnail Image Prompt\": \"A thumbnail with a striking design. The background is a cold blue gradient. The text reads: 'LEFT ON READ?' in bold, uppercase white and blue letters. Below, a smaller subtitle reads: 'Heal & Move Forward!' in white. On the right, an image of a phone with unanswered messages fading into the background.\"\n"
        f"    }}\n"
        f"  ]\n"
        f"}}"
)
        
        max_retries = 5
        for attempt in range(max_retries):
            try:
                # Generate content
                scenario, empathetic_advice, practical_advice, thumbnail_prompt = await generate_vlog_content(pool, prompt)
                if await scenario_exists(pool, scenario):
                    await log_event(pool, "DUPLICATE_SCENARIO", f"Duplicate scenario found on attempt {attempt + 1}: {scenario}")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        raise Exception("Max retries reached, unable to generate a unique scenario")
                # Save content
                content_id = await save_content(
                    pool,
                    prompt,
                    scenario,
                    empathetic_advice,
                    practical_advice,
                    thumbnail_prompt
                )
                await log_event(pool, "PROCESS_COMPLETE", f"Vlog content generated and saved with ID {content_id}")
                # Generate and save image prompts
                combined_advice = f"{empathetic_advice}\n\n{practical_advice}"
                # image_prompts = await generate_image_prompts(pool, combined_advice)
                # await save_image_prompts(pool, content_id, image_prompts)
                # # Generate images
                # await generate_images(pool, content_id, runware)
                # # Generate speech from question and advice
                # speech_result = await generate_speech(pool, content_id, combined_advice)
                # Display response
                response = {
                    "response": {
                        "Scenario": scenario,
                        "Empathetic Advice": empathetic_advice,
                        "Practical Advice": practical_advice,
                        "Thumbnail Image Prompt": thumbnail_prompt
                    },
                    "original_prompt": prompt
                }
                print(f"Generated content and image prompts:\n{json.dumps(response, indent=2)}")
                break
            except Exception as e:
                if attempt < max_retries - 1:
                    await log_event(pool, "RETRY_ATTEMPT", f"Retrying due to error on attempt {attempt + 1}: {e}")
                    continue
                else:
                    raise Exception(f"Failed after {max_retries} attempts: {e}")
    except Exception as e:
        logging.error(f"An error occurred: {e}")
        print(f"An error occurred: {e}")
    finally:
        #await runware.close()
        pool.close()
        await pool.wait_closed()

# Run the async main function
#if __name__ == "__main__":
#    asyncio.run(main())
if __name__ == "__main__":
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    asyncio.run(main())
