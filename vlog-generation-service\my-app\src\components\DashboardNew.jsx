import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spinner, Table, Container, Row, Col } from 'react-bootstrap';
import './Dashboard.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

function DashboardNew() {
    const [contents, setContents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isExecutingScript, setIsExecutingScript] = useState(false);

    useEffect(() => {
        fetchContent();
    }, []);

    const fetchContent = async () => {
        try {
            setLoading(true);
            const response = await fetch(`${API_BASE_URL}/api/content`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            setContents(data);
            setLoading(false);
        } catch (error) {
            console.error('Error fetching content:', error);
            setError(error.message);
            setLoading(false);
        }
    };

    const handleExecuteScript = async () => {
        setIsExecutingScript(true);
        try {
            const response = await fetch(`${API_BASE_URL}/api/execute/script`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to execute script');
            }

            alert('Script execution completed successfully!');
            await fetchContent();

        } catch (error) {
            console.error('Error executing script:', error);
            alert('Failed to execute script: ' + error.message);
        } finally {
            setIsExecutingScript(false);
        }
    };

    return (
        <Container fluid className="dashboard-container">
            <Row className="mb-4 align-items-center">
                <Col>
                    <div className="d-flex align-items-center">
                        <div className="logo-container position-relative" style={{ width: '40px', height: '40px', marginRight: '15px' }}>
                            <div className="position-absolute" style={{ top: 0, left: 0, width: '100%', height: '100%', background: '#2541b8', borderRadius: '8px', transform: 'rotate(45deg)', animation: 'pulse 3s infinite ease-in-out' }}></div>
                            <div className="position-absolute d-flex align-items-center justify-content-center" style={{ top: 0, left: 0, width: '100%', height: '100%', zIndex: 2 }}>
                                <span className="fw-bold text-white" style={{ fontSize: '1rem' }}>VF</span>
                            </div>
                        </div>
                        <div>
                            <h1 className="dashboard-title" style={{ color: '#2541b8', margin: 0, fontSize: '1.8rem', fontWeight: 'bold' }}>VisionFrame <span style={{ color: '#ff5757' }}>AI</span> Dashboard</h1>
                            <p className="text-muted mb-0">Your all-in-one content creation platform</p>
                        </div>
                    </div>
                </Col>
                <Col xs="auto">
                    <Button
                        variant="primary"
                        onClick={handleExecuteScript}
                        disabled={isExecutingScript}
                        className="me-2"
                        style={{ backgroundColor: '#2541b8', borderColor: '#2541b8' }}
                    >
                        {isExecutingScript ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Executing Script...
                            </>
                        ) : (
                            'Execute Generation Script'
                        )}
                    </Button>
                </Col>
            </Row>

            {loading ? (
                <div className="text-center my-5">
                    <Spinner animation="border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </Spinner>
                </div>
            ) : error ? (
                <div className="alert alert-danger">Error: {error}</div>
            ) : (
                <Table striped bordered hover responsive className="visionframe-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Scenario</th>
                            <th>Content</th>
                        </tr>
                    </thead>
                    <tbody>
                        {contents && contents.length > 0 ? (
                            contents.map((item) => (
                                <tr key={item.id}>
                                    <td>{item.id}</td>
                                    <td>{item.scenario}</td>
                                    <td>
                                        {item.empathetic_advice && (
                                            <div className="mb-2">
                                                <strong>Empathetic Advice:</strong> {item.empathetic_advice}
                                            </div>
                                        )}
                                        {item.practical_advice && (
                                            <div>
                                                <strong>Practical Advice:</strong> {item.practical_advice}
                                            </div>
                                        )}
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan="3" className="text-center">No content available</td>
                            </tr>
                        )}
                    </tbody>
                </Table>
            )}
        </Container>
    );
}

export default DashboardNew;
