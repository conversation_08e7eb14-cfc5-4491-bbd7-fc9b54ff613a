"""
Start All Components Script

This script starts all components of the VisionFrame AI queue system:
1. Redis server (if not already running)
2. Celery worker
3. Celery beat
4. API server
"""

import os
import sys
import time
import subprocess
import threading
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def run_command(cmd, name):
    """Run a command in a subprocess"""
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # Print output in real-time
        for line in process.stdout:
            print(f"[{name}] {line.strip()}")

        process.wait()
        print(f"{name} process exited with code {process.returncode}")

    except Exception as e:
        print(f"Error in {name}: {e}")

def start_redis():
    """Start Redis server if not already running"""
    print("Checking if Redis is running...")

    try:
        # Try to connect to Redis
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("Redis is already running")
        return True
    except:
        print("Red<PERSON> is not running, attempting to start...")

        # Try to start Redis
        cmd = []
        if sys.platform == 'win32':
            # Windows - use portable Redis
            redis_path = os.path.join(os.getcwd(), 'redis-portable', 'redis-server.exe')
            redis_conf = os.path.join(os.getcwd(), 'redis-portable', 'redis.windows.conf')
            cmd = [redis_path, redis_conf]
        else:
            # Linux/Mac
            cmd = ['redis-server']

        thread = threading.Thread(target=run_command, args=(cmd, "Redis"))
        thread.daemon = True
        thread.start()

        # Wait for Redis to start
        time.sleep(3)

        # Check if Redis is now running
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            print("Redis started successfully")
            return True
        except:
            print("Failed to start Redis. Please start it manually.")
            return False

def start_worker():
    """Start Celery worker"""
    print("Starting Celery worker...")

    cmd = []
    if sys.platform == 'win32':
        # Windows
        cmd = [
            'celery',
            '-A', 'tasks',
            'worker',
            '--loglevel=info',
            '--pool=solo',
            '--concurrency=1'
        ]
    else:
        # Linux/Mac
        cmd = [
            'celery',
            '-A', 'tasks',
            'worker',
            '--loglevel=info',
            '--concurrency=4'
        ]

    thread = threading.Thread(target=run_command, args=(cmd, "Worker"))
    thread.daemon = True
    thread.start()

    # Wait for worker to initialize
    time.sleep(2)

def start_beat():
    """Start Celery beat"""
    print("Starting Celery beat...")

    cmd = [
        'celery',
        '-A', 'tasks',
        'beat',
        '--loglevel=info'
    ]

    thread = threading.Thread(target=run_command, args=(cmd, "Beat"))
    thread.daemon = True
    thread.start()

    # Wait for beat to initialize
    time.sleep(2)

def start_api():
    """Start API server"""
    print("Starting API server...")

    cmd = [
        'python',
        'consolidated_api.py'
    ]

    thread = threading.Thread(target=run_command, args=(cmd, "API"))
    thread.daemon = True
    thread.start()

    # Wait for API to initialize
    time.sleep(2)

def main():
    """Start all components"""
    print("Starting all VisionFrame AI components...")

    # Start Redis
    if not start_redis():
        print("Cannot proceed without Redis. Exiting.")
        return

    # Start worker
    start_worker()

    # Start beat
    start_beat()

    # Start API
    start_api()

    print("All components started. Press Ctrl+C to stop.")

    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Stopping all components...")

if __name__ == "__main__":
    main()
