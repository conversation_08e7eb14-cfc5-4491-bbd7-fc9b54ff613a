import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON>, Badge } from 'react-bootstrap';
import { API_BASE_URL } from '../../constants';
import './Cheatsheets.css';
import {
  Psychology as PsychologyIcon,
  Image as ImageIcon
} from '@mui/icons-material';

const AdvancedThumbnailGenerator = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [textOverlay, setTextOverlay] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('xai');
  const [promptPreview, setPromptPreview] = useState('');
  const [characterCount, setCharacterCount] = useState(0);

  // Core Foundations category
  const [topic, setTopic] = useState('');
  const [targetAudience, setTargetAudience] = useState('general');
  const [contentCategory, setContentCategory] = useState('tutorial');
  const [coreMessage, setCoreMessage] = useState('');

  // Subject & Scene Elements category
  const [mainSubject, setMainSubject] = useState('person');
  const [subjectDetails, setSubjectDetails] = useState('');
  const [subjectPosition, setSubjectPosition] = useState('center');
  const [sceneEnvironment, setSceneEnvironment] = useState('');

  // Emotional & Psychological Triggers category
  const [primaryEmotion, setPrimaryEmotion] = useState('curiosity');
  const [facialExpression, setFacialExpression] = useState('neutral');
  const [psychologicalTrigger, setPsychologicalTrigger] = useState('curiosity_gap');

  // Visual Design Elements category
  const [colorPalette, setColorPalette] = useState('vibrant');
  const [lighting, setLighting] = useState('dramatic');
  const [composition, setComposition] = useState('rule_of_thirds');
  const [visualStyle, setVisualStyle] = useState('photorealistic');

  // Text & Typography category
  const [includeText, setIncludeText] = useState(true);
  const [textContent, setTextContent] = useState('');
  const [textStyle, setTextStyle] = useState('bold_contrast');

  // Technical Optimization category
  const [aspectRatio, setAspectRatio] = useState('16_9');
  const [resolution, setResolution] = useState('high');
  const [platform, setPlatform] = useState('youtube');

  // Template selection
  const [useTemplate, setUseTemplate] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('contrast_emotional');

  // Target audience options
  const audienceOptions = [
    { value: 'general', label: 'General Audience' },
    { value: 'young_adults', label: 'Young Adults (18-24)' },
    { value: 'professionals', label: 'Professionals (25-34)' },
    { value: 'mature_adults', label: 'Mature Adults (35-44)' },
    { value: 'seniors', label: 'Seniors (45+)' },
    { value: 'tech_savvy', label: 'Tech-Savvy Users' },
    { value: 'creative', label: 'Creative Professionals' },
    { value: 'students', label: 'Students' }
  ];

  // Content category options
  const categoryOptions = [
    { value: 'tutorial', label: 'Tutorial/How-To' },
    { value: 'vlog', label: 'Vlog/Personal Story' },
    { value: 'review', label: 'Product Review' },
    { value: 'reaction', label: 'Reaction Video' },
    { value: 'educational', label: 'Educational Content' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'gaming', label: 'Gaming Content' },
    { value: 'business', label: 'Business/Professional' },
    { value: 'health', label: 'Health/Wellness' }
  ];

  // Main subject options
  const subjectOptions = [
    { value: 'person', label: 'Person/Character' },
    { value: 'object', label: 'Object/Product' },
    { value: 'scene', label: 'Scene/Location' },
    { value: 'concept', label: 'Abstract Concept' },
    { value: 'text', label: 'Text-Focused' },
    { value: 'multiple', label: 'Multiple Subjects' }
  ];

  // Subject positioning options
  const positionOptions = [
    { value: 'center', label: 'Centered' },
    { value: 'left_third', label: 'Left Third (Rule of Thirds)' },
    { value: 'right_third', label: 'Right Third (Rule of Thirds)' },
    { value: 'foreground', label: 'Foreground Focus' },
    { value: 'background', label: 'Background Focus' },
    { value: 'dynamic', label: 'Dynamic/Action Pose' }
  ];

  // Emotion options
  const emotionOptions = [
    { value: 'curiosity', label: 'Curiosity' },
    { value: 'surprise', label: 'Surprise/Shock' },
    { value: 'fear', label: 'Fear/Anxiety' },
    { value: 'joy', label: 'Joy/Happiness' },
    { value: 'urgency', label: 'Urgency/FOMO' },
    { value: 'inspiration', label: 'Inspiration' },
    { value: 'confusion', label: 'Confusion' },
    { value: 'trust', label: 'Trust/Authority' }
  ];

  // Facial expression options
  const expressionOptions = [
    { value: 'neutral', label: 'Neutral' },
    { value: 'surprised', label: 'Surprised (Wide Eyes/Open Mouth)' },
    { value: 'focused', label: 'Focused/Intense' },
    { value: 'smiling', label: 'Smiling/Happy' },
    { value: 'concerned', label: 'Concerned/Worried' },
    { value: 'excited', label: 'Excited/Enthusiastic' },
    { value: 'thoughtful', label: 'Thoughtful/Contemplative' },
    { value: 'confused', label: 'Confused/Puzzled' }
  ];

  // Psychological trigger options
  const triggerOptions = [
    { value: 'curiosity_gap', label: 'Curiosity Gap' },
    { value: 'scarcity', label: 'Scarcity/Limited Time' },
    { value: 'social_proof', label: 'Social Proof' },
    { value: 'authority', label: 'Authority/Expertise' },
    { value: 'contrast', label: 'Contrast/Before-After' },
    { value: 'novelty', label: 'Novelty/Uniqueness' },
    { value: 'controversy', label: 'Controversy/Debate' },
    { value: 'value_prop', label: 'Clear Value Proposition' }
  ];

  // Color palette options
  const colorOptions = [
    { value: 'vibrant', label: 'Vibrant/High Contrast' },
    { value: 'warm', label: 'Warm (Reds, Oranges, Yellows)' },
    { value: 'cool', label: 'Cool (Blues, Greens, Purples)' },
    { value: 'monochromatic', label: 'Monochromatic' },
    { value: 'complementary', label: 'Complementary Colors' },
    { value: 'dark', label: 'Dark/Moody' },
    { value: 'bright', label: 'Bright/Cheerful' },
    { value: 'pastel', label: 'Pastel/Soft' }
  ];

  // Lighting options
  const lightingOptions = [
    { value: 'dramatic', label: 'Dramatic/High Contrast' },
    { value: 'soft', label: 'Soft/Diffused' },
    { value: 'backlit', label: 'Backlit/Silhouette' },
    { value: 'cinematic', label: 'Cinematic Lighting' },
    { value: 'natural', label: 'Natural Daylight' },
    { value: 'studio', label: 'Studio Lighting' },
    { value: 'low_key', label: 'Low Key (Dark with Highlights)' },
    { value: 'high_key', label: 'High Key (Bright, Low Contrast)' }
  ];

  // Composition options
  const compositionOptions = [
    { value: 'rule_of_thirds', label: 'Rule of Thirds' },
    { value: 'centered', label: 'Centered/Symmetrical' },
    { value: 'diagonal', label: 'Diagonal/Dynamic' },
    { value: 'foreground_interest', label: 'Foreground Interest' },
    { value: 'frame_within_frame', label: 'Frame Within Frame' },
    { value: 'leading_lines', label: 'Leading Lines' },
    { value: 'golden_ratio', label: 'Golden Ratio' },
    { value: 'negative_space', label: 'Negative Space' }
  ];

  // Visual style options
  const styleOptions = [
    { value: 'photorealistic', label: 'Photorealistic' },
    { value: 'illustrative', label: 'Illustrative/Graphic' },
    { value: 'cinematic', label: 'Cinematic/Film-like' },
    { value: 'minimalist', label: 'Minimalist/Clean' },
    { value: 'retro', label: 'Retro/Vintage' },
    { value: 'futuristic', label: 'Futuristic/High-Tech' },
    { value: 'cartoon', label: 'Cartoon/Animated' },
    { value: '3d_render', label: '3D Rendered' }
  ];

  // Text style options
  const textStyleOptions = [
    { value: 'bold_contrast', label: 'Bold with High Contrast' },
    { value: 'minimal_clean', label: 'Minimal & Clean' },
    { value: 'dramatic_shadow', label: 'Dramatic with Shadow' },
    { value: 'retro_vintage', label: 'Retro/Vintage' },
    { value: 'handwritten', label: 'Handwritten Style' },
    { value: 'tech_futuristic', label: 'Tech/Futuristic' },
    { value: 'elegant_serif', label: 'Elegant Serif' },
    { value: 'playful_casual', label: 'Playful/Casual' }
  ];

  // Aspect ratio options
  const aspectRatioOptions = [
    { value: '16_9', label: '16:9 (YouTube Standard)' },
    { value: '1_1', label: '1:1 (Square - Social Media)' },
    { value: '4_3', label: '4:3 (Traditional)' },
    { value: '9_16', label: '9:16 (Mobile/Stories)' },
    { value: '2_1', label: '2:1 (Cinematic)' },
    { value: '21_9', label: '21:9 (Ultrawide)' }
  ];

  // Resolution options
  const resolutionOptions = [
    { value: 'high', label: 'High (1920x1080 or equivalent)' },
    { value: 'medium', label: 'Medium (1280x720 or equivalent)' },
    { value: 'ultra', label: 'Ultra HD (3840x2160 or equivalent)' }
  ];

  // Platform options
  const platformOptions = [
    { value: 'youtube', label: 'YouTube' },
    { value: 'course', label: 'Course Platform' },
    { value: 'blog', label: 'Blog/Website' },
    { value: 'social', label: 'Social Media' },
    { value: 'ecommerce', label: 'E-commerce' }
  ];

  // Template options
  const templateOptions = [
    {
      value: 'contrast_emotional',
      label: 'High Contrast Emotional',
      description: 'Strong contrast with black and red background, bold text, emotional question format',
      example: 'ONE-SIDED LOVE? Why They Don\'t Love You Back!',
      settings: {
        colorPalette: 'dark',
        lighting: 'dramatic',
        composition: 'rule_of_thirds',
        visualStyle: 'photorealistic',
        textStyle: 'bold_contrast',
        psychologicalTrigger: 'curiosity_gap'
      }
    },
    {
      value: 'before_after',
      label: 'Before/After Transformation',
      description: 'Split screen showing before and after states, with bold transformation text',
      example: 'TRANSFORM YOUR BODY IN 30 DAYS',
      settings: {
        colorPalette: 'complementary',
        lighting: 'studio',
        composition: 'centered',
        visualStyle: 'photorealistic',
        textStyle: 'dramatic_shadow',
        psychologicalTrigger: 'contrast'
      }
    },
    {
      value: 'number_list',
      label: 'Numbered List/Steps',
      description: 'Large number with steps or list items, clean organized layout',
      example: '7 HABITS That Are DESTROYING Your Focus',
      settings: {
        colorPalette: 'bright',
        lighting: 'soft',
        composition: 'centered',
        visualStyle: 'minimalist',
        textStyle: 'minimal_clean',
        psychologicalTrigger: 'value_prop'
      }
    }
  ];

  // AI Provider options
  const providerOptions = [
    { value: 'xai', label: 'XAI/Grok' },
    { value: 'deepseek', label: 'DeepSeek' },
    { value: 'gemini', label: 'Gemini 2.0 Flash' }
  ];

  // Apply template settings
  const applyTemplate = (templateValue) => {
    const template = templateOptions.find(t => t.value === templateValue);
    if (template) {
      const { settings } = template;

      // Apply the template settings
      setColorPalette(settings.colorPalette);
      setLighting(settings.lighting);
      setComposition(settings.composition);
      setVisualStyle(settings.visualStyle);
      setTextStyle(settings.textStyle);
      setPsychologicalTrigger(settings.psychologicalTrigger);

      // Always enable text for templates
      setIncludeText(true);

      // If the template is the contrast emotional one, set specific text content as an example
      if (templateValue === 'contrast_emotional' && !textContent) {
        setTextContent('ONE-SIDED LOVE? Why They Don\'t Love You Back!');
      }

      // Update the preview after applying the template
      setTimeout(updatePromptPreview, 0);
    }
  };

  // Handle template change
  const handleTemplateChange = (e) => {
    const value = e.target.value;
    setSelectedTemplate(value);
    applyTemplate(value);
  };

  // Update prompt preview
  const updatePromptPreview = () => {
    // Updated preview to include all categories
    const preview = `Generate a thumbnail for a ${contentCategory} about "${topic}" targeted at ${targetAudience} audience. Core message: ${coreMessage}\n\nMain subject: ${mainSubject} - ${subjectDetails || '[No details provided]'}\nSubject positioning: ${subjectPosition}\nScene environment: ${sceneEnvironment || '[No environment specified]'}\n\nPrimary emotion: ${primaryEmotion}\nFacial expression: ${facialExpression}\nPsychological trigger: ${psychologicalTrigger}\n\nColor palette: ${colorPalette}\nLighting: ${lighting}\nComposition: ${composition}\nVisual style: ${visualStyle}\n\n${includeText ? `Text overlay: ${textContent || 'Generate appropriate text'}\nText style: ${textStyle}\n` : 'No text overlay\n'}\nTechnical specifications:\nAspect ratio: ${aspectRatio}\nResolution: ${resolution}\nOptimized for: ${platform}`;
    setPromptPreview(preview);
    setCharacterCount(preview.length);
  };

  // Handle form submission
  const handleGeneratePrompt = async () => {
    if (!topic) {
      setError('Please enter a topic');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Update the prompt preview before sending
      updatePromptPreview();

      const requestData = {
        prompt_type: 'advanced_thumbnail',
        provider: selectedProvider,
        topic: topic,
        target_audience: targetAudience,
        content_category: contentCategory,
        core_message: coreMessage,
        main_subject: mainSubject,
        subject_details: subjectDetails,
        subject_position: subjectPosition,
        scene_environment: sceneEnvironment,
        primary_emotion: primaryEmotion,
        facial_expression: facialExpression,
        psychological_trigger: psychologicalTrigger,
        color_palette: colorPalette,
        lighting: lighting,
        composition: composition,
        visual_style: visualStyle,
        include_text: includeText,
        text_content: textContent,
        text_style: textStyle,
        aspect_ratio: aspectRatio,
        resolution: resolution,
        platform: platform
      };

      console.log('Sending request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate prompt: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      if (data.success && data.generated_prompt) {
        setGeneratedPrompt(data.generated_prompt);

        if (data.text_overlay) {
          setTextOverlay(data.text_overlay);
        } else {
          setTextOverlay('');
        }

        // Check if there was a fallback to XAI/Grok
        if (data.provider_fallback && selectedProvider !== 'xai') {
          setError(`Note: ${selectedProvider.toUpperCase()} API had insufficient balance. Successfully fell back to XAI/Grok.`);
        }
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }
    } catch (err) {
      console.error('Error generating prompt:', err);
      setError(`Failed to generate prompt: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="cheatsheet-container">
      <Card className="mb-4">
        <Card.Header className="bg-dark text-white">
          <h4 className="mb-0">🖼️ Advanced Thumbnail Prompt Engineering</h4>
        </Card.Header>
        <Card.Body>
          <p className="lead">
            Create powerful, high-CTR thumbnail prompts using the Ultimate Thumbnail Prompt Engineering Master Guide framework.
          </p>
          <Alert variant="info">
            <strong>Multi-Provider Support:</strong> Generate optimized thumbnail prompts for XAI/Grok, DeepSeek, and Gemini.
          </Alert>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header className="d-flex align-items-center">
          <PsychologyIcon className="me-2" />
          <h5 className="mb-0">Thumbnail Generator</h5>
        </Card.Header>
        <Card.Body>
          {error && (
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          )}

          <Form>
            {/* AI Provider Selection */}
            <Row className="mb-3">
              <Col md={12}>
                <Form.Group>
                  <Form.Label><strong>Select AI Provider</strong></Form.Label>
                  <div className="d-flex">
                    {providerOptions.map((provider) => (
                      <Form.Check
                        key={provider.value}
                        type="radio"
                        id={`provider-${provider.value}`}
                        label={provider.label}
                        name="aiProvider"
                        className="me-4"
                        checked={selectedProvider === provider.value}
                        onChange={() => setSelectedProvider(provider.value)}
                      />
                    ))}
                  </div>
                  {selectedProvider === 'deepseek' && (
                    <Alert variant="info" className="mt-2 mb-0 py-2">
                      <small>Note: If DeepSeek has insufficient balance, the system will automatically fall back to XAI/Grok.</small>
                    </Alert>
                  )}
                  {selectedProvider === 'gemini' && (
                    <Alert variant="info" className="mt-2 mb-0 py-2">
                      <small>Using Gemini 2.0 Flash model. If Gemini has quota limits, the system will automatically fall back to XAI/Grok.</small>
                    </Alert>
                  )}
                </Form.Group>
              </Col>
            </Row>

            {/* Thumbnail Template Selection */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Thumbnail Template</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Select a Template Style</Form.Label>
                      <Form.Select
                        value={selectedTemplate}
                        onChange={handleTemplateChange}
                      >
                        {templateOptions.map((template) => (
                          <option key={template.value} value={template.value}>
                            {template.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        Applying a template will set recommended visual settings
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {selectedTemplate && (
                  <Alert variant="info" className="mb-0">
                    <div className="d-flex align-items-start">
                      <div className="flex-grow-1">
                        <p className="mb-1"><strong>Template:</strong> {templateOptions.find(t => t.value === selectedTemplate)?.label}</p>
                        <p className="mb-1"><small>{templateOptions.find(t => t.value === selectedTemplate)?.description}</small></p>
                        <p className="mb-0"><strong>Example:</strong> <em>"{templateOptions.find(t => t.value === selectedTemplate)?.example}"</em></p>
                      </div>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="ms-2"
                        onClick={() => applyTemplate(selectedTemplate)}
                      >
                        Apply
                      </Button>
                    </div>
                  </Alert>
                )}
              </Card.Body>
            </Card>

            {/* Core Foundations Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Core Foundations</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Topic</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter your thumbnail topic"
                        value={topic}
                        onChange={(e) => setTopic(e.target.value)}
                      />
                      <Form.Text className="text-muted">
                        The main subject of your thumbnail
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Target Audience</Form.Label>
                      <Form.Select
                        value={targetAudience}
                        onChange={(e) => setTargetAudience(e.target.value)}
                      >
                        {audienceOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Content Category</Form.Label>
                      <Form.Select
                        value={contentCategory}
                        onChange={(e) => setContentCategory(e.target.value)}
                      >
                        {categoryOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Core Message (5 words max)</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Primary value proposition or emotional trigger"
                        value={coreMessage}
                        onChange={(e) => setCoreMessage(e.target.value)}
                        maxLength={50}
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Subject & Scene Elements Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Subject & Scene Elements</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Main Subject Type</Form.Label>
                      <Form.Select
                        value={mainSubject}
                        onChange={(e) => setMainSubject(e.target.value)}
                      >
                        {subjectOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Subject Positioning</Form.Label>
                      <Form.Select
                        value={subjectPosition}
                        onChange={(e) => setSubjectPosition(e.target.value)}
                      >
                        {positionOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Subject Details</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Age, gender, appearance, clothing, etc."
                        value={subjectDetails}
                        onChange={(e) => setSubjectDetails(e.target.value)}
                      />
                      <Form.Text className="text-muted">
                        Describe the specific details of your main subject
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Scene Environment</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Location, props, setting details"
                        value={sceneEnvironment}
                        onChange={(e) => setSceneEnvironment(e.target.value)}
                      />
                      <Form.Text className="text-muted">
                        Describe the environment or setting for your thumbnail
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Emotional & Psychological Triggers Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Emotional & Psychological Triggers</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Primary Emotion</Form.Label>
                      <Form.Select
                        value={primaryEmotion}
                        onChange={(e) => setPrimaryEmotion(e.target.value)}
                      >
                        {emotionOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        The main emotion you want to evoke in viewers
                      </Form.Text>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Facial Expression</Form.Label>
                      <Form.Select
                        value={facialExpression}
                        onChange={(e) => setFacialExpression(e.target.value)}
                      >
                        {expressionOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        For thumbnails featuring people
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Psychological Pattern Interrupt</Form.Label>
                      <Form.Select
                        value={psychologicalTrigger}
                        onChange={(e) => setPsychologicalTrigger(e.target.value)}
                      >
                        {triggerOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        Psychological trigger to increase click-through rate
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Visual Design Elements Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Visual Design Elements</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Color Palette</Form.Label>
                      <Form.Select
                        value={colorPalette}
                        onChange={(e) => setColorPalette(e.target.value)}
                      >
                        {colorOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Lighting</Form.Label>
                      <Form.Select
                        value={lighting}
                        onChange={(e) => setLighting(e.target.value)}
                      >
                        {lightingOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Composition</Form.Label>
                      <Form.Select
                        value={composition}
                        onChange={(e) => setComposition(e.target.value)}
                      >
                        {compositionOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Visual Style</Form.Label>
                      <Form.Select
                        value={visualStyle}
                        onChange={(e) => setVisualStyle(e.target.value)}
                      >
                        {styleOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Text & Typography Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Text & Typography</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        id="include-text-check"
                        label="Include Text Overlay"
                        checked={includeText}
                        onChange={(e) => setIncludeText(e.target.checked)}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                {includeText && (
                  <>
                    <Row>
                      <Col md={12}>
                        <Form.Group className="mb-3">
                          <Form.Label>Text Content (Optional)</Form.Label>
                          <Form.Control
                            type="text"
                            placeholder="Leave blank to let AI generate appropriate text"
                            value={textContent}
                            onChange={(e) => setTextContent(e.target.value)}
                          />
                          <Form.Text className="text-muted">
                            Specific text to overlay or leave blank for AI suggestions
                          </Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={12}>
                        <Form.Group className="mb-3">
                          <Form.Label>Text Style</Form.Label>
                          <Form.Select
                            value={textStyle}
                            onChange={(e) => setTextStyle(e.target.value)}
                          >
                            {textStyleOptions.map((option) => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>
                  </>
                )}
              </Card.Body>
            </Card>

            {/* Technical Optimization Category */}
            <Card className="mb-3">
              <Card.Header className="bg-light">
                <h6 className="mb-0">Technical Optimization</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Aspect Ratio</Form.Label>
                      <Form.Select
                        value={aspectRatio}
                        onChange={(e) => setAspectRatio(e.target.value)}
                      >
                        {aspectRatioOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Resolution</Form.Label>
                      <Form.Select
                        value={resolution}
                        onChange={(e) => setResolution(e.target.value)}
                      >
                        {resolutionOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Target Platform</Form.Label>
                      <Form.Select
                        value={platform}
                        onChange={(e) => setPlatform(e.target.value)}
                      >
                        {platformOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        Optimizes the thumbnail for specific platform requirements
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Prompt Preview */}
            <Card className="mb-3">
              <Card.Header className="bg-light d-flex justify-content-between align-items-center">
                <h6 className="mb-0">Prompt Preview</h6>
                <Badge bg={characterCount > 1000 ? "warning" : "info"}>
                  {characterCount} characters
                </Badge>
              </Card.Header>
              <Card.Body>
                <Form.Group>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={promptPreview}
                    onChange={(e) => setPromptPreview(e.target.value)}
                    onBlur={() => setCharacterCount(promptPreview.length)}
                  />
                  <Form.Text className="text-muted">
                    This is a preview of the prompt that will be sent to the AI. You can edit it if needed.
                  </Form.Text>
                </Form.Group>
                <div className="d-flex justify-content-end mt-2">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={updatePromptPreview}
                  >
                    Update Preview
                  </Button>
                </div>
              </Card.Body>
            </Card>

            <div className="d-grid">
              <Button
                variant="primary"
                onClick={handleGeneratePrompt}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                    Generating...
                  </>
                ) : (
                  <>Generate Thumbnail Prompt</>
                )}
              </Button>
            </div>
          </Form>

          {generatedPrompt && (
            <div className="mt-4">
              <h6>Generated Prompt:</h6>
              <div className="bg-light p-3 rounded">
                <pre className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                  {generatedPrompt}
                </pre>
              </div>

              {textOverlay && (
                <div className="mt-3">
                  <h6>Text Overlay:</h6>
                  <div
                    className="p-4 rounded text-center"
                    style={{
                      background: selectedTemplate === 'contrast_emotional' ?
                        'linear-gradient(135deg, #000000 0%, #1a0000 40%, #330000 60%, #660000 100%)' :
                        'linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.5))',
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    {selectedTemplate === 'contrast_emotional' && (
                      <>
                        <div
                          className="mb-1"
                          style={{
                            fontSize: '28px',
                            fontWeight: '900',
                            color: '#fff',
                            textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                            letterSpacing: '1px',
                            textTransform: 'uppercase',
                            lineHeight: '1.2'
                          }}
                        >
                          {textOverlay.includes('?') ? textOverlay.split('?')[0] + '?' : textOverlay}
                        </div>
                        {textOverlay.includes('?') && (
                          <div
                            style={{
                              fontSize: '16px',
                              fontWeight: 'bold',
                              color: '#fff',
                              textShadow: '1px 1px 3px rgba(0,0,0,0.8)',
                              marginTop: '5px'
                            }}
                          >
                            {textOverlay.split('?')[1] || ''}
                          </div>
                        )}
                        <div
                          style={{
                            position: 'absolute',
                            bottom: '-20px',
                            right: '-20px',
                            width: '80px',
                            height: '80px',
                            borderRadius: '50%',
                            background: 'rgba(255,0,0,0.3)',
                            zIndex: 0
                          }}
                        />
                      </>
                    )}

                    {(!selectedTemplate || selectedTemplate !== 'contrast_emotional') && (
                      <div
                        className="p-3 mb-0"
                        style={{
                          fontSize: '24px',
                          fontWeight: 'bold',
                          color: '#fff',
                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                          background: 'linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.2))',
                          borderRadius: '4px',
                          display: 'inline-block',
                          padding: '10px 20px'
                        }}
                      >
                        {textOverlay}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="d-grid mt-2">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => {
                    const textToCopy = textOverlay
                      ? `Image Prompt:\n${generatedPrompt}\n\nText Overlay:\n${textOverlay}`
                      : generatedPrompt;
                    navigator.clipboard.writeText(textToCopy);
                  }}
                >
                  Copy to Clipboard
                </Button>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Thumbnail Success Metrics</h5>
        </Card.Header>
        <Card.Body>
          <div className="table-responsive">
            <table className="table table-bordered">
              <thead className="bg-light">
                <tr>
                  <th>Platform</th>
                  <th>Optimal CTR</th>
                  <th>Benchmark</th>
                  <th>Viewing Context</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>YouTube</td>
                  <td>8-12%</td>
                  <td>Industry average: 4-6%</td>
                  <td>Mobile (70%), Desktop (25%), TV (5%)</td>
                </tr>
                <tr>
                  <td>Course Platforms</td>
                  <td>15-20%</td>
                  <td>Industry average: 8-10%</td>
                  <td>Desktop (65%), Mobile (35%)</td>
                </tr>
                <tr>
                  <td>Blog Posts</td>
                  <td>3-5%</td>
                  <td>Industry average: 1-2%</td>
                  <td>Mobile (80%), Desktop (20%)</td>
                </tr>
                <tr>
                  <td>Social Media</td>
                  <td>5-7%</td>
                  <td>Industry average: 2-3%</td>
                  <td>Mobile (95%), Desktop (5%)</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default AdvancedThumbnailGenerator;
