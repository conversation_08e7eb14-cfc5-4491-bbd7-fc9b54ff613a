from moviepy.editor import ImageClip, concatenate_videoclips, AudioFileClip
from pathlib import Path
import os

# Define the directory containing the images and the path to the audio file
base_dir = os.path.dirname(os.path.abspath(__file__))
    

image_directory =Path('content-images/139') #os.path.join(base_dir, "content-images/139") Path('/content-images/139')
audio_path = os.path.join(base_dir, "content-speech", "139.mp3")
# Load the audio file
audio = AudioFileClip(audio_path)

# Get all image file paths from the directory (adjust the pattern as needed)
image_files = sorted(image_directory.glob('*.jpg'))  # or '*.png', etc.

# Calculate the duration each image should be displayed
num_images = len(image_files)
if num_images == 0:
    raise ValueError("No images found in the specified directory.")
image_duration = audio.duration / num_images

# Create ImageClips for each image with the calculated duration
image_clips = [ImageClip(str(img)).set_duration(image_duration) for img in image_files]

# Concatenate all image clips into one video
video = concatenate_videoclips(image_clips, method="compose")

# Set the audio to the video
video = video.set_audio(audio)

# Export the final video
output_path = "content-video/output_video.mp4"
video.write_videofile(output_path, fps=24)
