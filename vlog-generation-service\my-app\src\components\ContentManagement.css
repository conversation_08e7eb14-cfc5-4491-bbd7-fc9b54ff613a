.content-management {
    @apply bg-white;
}

/* Add New Prompt button styling */
.content-management .add-prompt-btn {
    min-width: 150px;
    padding: 10px 20px;
    font-weight: 500;
    background-color: #0d6efd;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-left: auto;
}

.content-management .add-prompt-btn:hover {
    background-color: #0b5ed7;
}

.content-management .add-prompt-btn:active {
    background-color: #0a58ca;
}

/* Modal styling enhancements */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
}

.modal-dialog {
    max-width: 1000px; /* Increased from 800px */
    width: 90%;
    margin: 1.75rem auto;
    position: relative;
    z-index: 1055;
}

.modal-content {
    position: relative;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    width: 100%;
    padding: 24px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    max-height: 90vh; /* Set maximum height to 90% of viewport height */
    overflow-y: auto; /* Enable vertical scrolling */
}

/* Custom scrollbar for modal content */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Ensure modal header stays at top */
.modal-header {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 1;
    padding: 0 0 20px 0;
    border-bottom: 1px solid #eaeaea;
    margin-bottom: 24px;
}

/* Ensure modal footer stays at bottom */
.modal-footer {
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 1;
    padding: 24px 0 0 0;
    border-top: 1px solid #eaeaea;
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Add some padding to prevent content from being hidden behind the footer */
.modal-body {
    padding-bottom: 24px;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1040;
}

/* Form elements within modal */
.modal-content .grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
}

/* Form row styling */
.modal-content .grid-cols-4 {
    display: grid;
    grid-template-columns: 1fr 3fr;
    align-items: center;
    gap: 1rem;
    width: 100%;
    padding: 1rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.modal-content .grid-cols-4:last-child {
    border-bottom: none;
}

/* Label styling */
.modal-content label {
    font-weight: 500;
    color: #374151;
    font-size: 0.95rem;
}

/* Input and select styling */
.modal-content select,
.modal-content input[type="text"],
.modal-content input[type="number"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background-color: #f8fafc;
    font-size: 0.95rem;
    transition: all 0.2s;
}

.modal-content select:hover,
.modal-content input[type="text"]:hover,
.modal-content input[type="number"]:hover {
    border-color: #cbd5e1;
}

.modal-content select:focus,
.modal-content input[type="text"]:focus,
.modal-content input[type="number"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: #fff;
}

/* Checkbox styling */
.modal-content input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    border: 1.5px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
}

/* Remove any table-based constraints */
.modal-content table,
.modal-content td,
.modal-content th {
    width: auto;
    max-width: none;
}

.modal-content input,
.modal-content textarea {
    width: 100%;
    max-width: none;
}

.modal-content .col-span-3 {
    width: 100%;
}

/* Remove any table-based constraints */
.modal-content table,
.modal-content td,
.modal-content th {
    width: auto;
    max-width: none;
}

.modal-content input,
.modal-content textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1.5px solid #e2e8f0;
    border-radius: 8px;
    margin-top: 0.25rem;
    font-size: 1.1rem; /* Increased from 0.95rem */
    transition: all 0.2s ease;
    background-color: #f8fafc;
}

.modal-content input:hover,
.modal-content textarea:hover {
    border-color: #cbd5e1;
    background-color: #fff;
}

.modal-content input:focus,
.modal-content textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: #fff;
}

.modal-content textarea {
    min-height: 200px; /* Increased from 120px */
    resize: vertical;
    line-height: 1.6; /* Increased from 1.5 */
    font-size: 1.2rem; /* Slightly larger than input */
}

/* Modal buttons */
.modal-footer button {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.modal-footer button:hover {
    transform: translateY(-1px);
}

.modal-footer button:active {
    transform: translateY(0);
}

/* Close button */
.modal-header .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f1f5f9;
    border: none;
    color: #64748b;
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

.modal-header .close-btn:hover {
    background-color: #e2e8f0;
    color: #1e293b;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content .grid {
        grid-template-columns: 1fr; /* Stack on mobile */
        gap: 0.75rem;
    }

    .modal-content label {
        text-align: left;
    }
}

/* Table styling */
.content-management table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.content-management thead {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.content-management th {
    padding: 1rem;
    font-weight: 600;
    color: #495057;
    text-align: left;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.content-management td {
    font-size: 0.875rem;
    line-height: 1.4;
    padding: 1rem;
    vertical-align: top;
}

.content-management tbody tr {
    transition: background-color 0.2s ease;
}

.content-management tbody tr:hover {
    background-color: #f8f9fa;
}

.content-management .prompt-template-preview {
    max-height: 100px;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: monospace;
    font-size: 0.875rem;
    white-space: pre-wrap;
}

/* Action buttons in table */
.content-management td button {
    margin-right: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.content-management td button:last-child {
    margin-right: 0;
}

/* Responsive table */
@media (max-width: 768px) {
    .content-management table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* Custom scrollbar for prompt preview */
.content-management .prompt-template-preview::-webkit-scrollbar {
    width: 6px;
}

.content-management .prompt-template-preview::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.content-management .prompt-template-preview::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.content-management .prompt-template-preview::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Table column width adjustments */
.content-management table th:nth-child(1) { /* Title column */
    width: 20%;
    min-width: 150px;
}

.content-management table th:nth-child(2) { /* Prompt Template column */
    width: 60%;
    min-width: 300px;
}

.content-management table th:nth-child(3) { /* Actions column */
    width: 20%;
    min-width: 150px;
}

/* Table cell content handling */
.content-management td {
    max-width: 0; /* Enables text truncation */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-wrap: break-word;
}

/* Prompt template preview adjustments */
.content-management .prompt-template-preview {
    max-height: 150px; /* Increased height for better content visibility */
    min-height: 50px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0.75rem;
    line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content-management table {
        min-width: 600px; /* Minimum width before horizontal scroll */
    }

    .content-management .prompt-template-preview {
        max-height: 100px;
    }
}

/* Table cell styling */
.content-management td {
    font-size: 0.875rem;
    line-height: 1.4;
    padding: 1rem;
    vertical-align: top;
}

/* Category information */
.category-info {
    @apply flex flex-col gap-1;
}

.category-name {
    @apply font-semibold text-gray-800;
}

.category-description {
    @apply text-sm text-gray-600;
}

/* Prompt template and call to action previews */
.prompt-template-preview,
.call-to-action-preview {
    max-height: 120px;
    overflow-y: auto;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: monospace;
    font-size: 0.8rem;
    white-space: pre-wrap;
}

/* Format information */
.format-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
}

/* Settings information */
.settings-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
}

/* Column widths */
.content-management table th:nth-child(1) { width: 50px; } /* ID */
.content-management table th:nth-child(2) { width: 120px; } /* Title */
.content-management table th:nth-child(3) { width: 150px; } /* Category */
.content-management table th:nth-child(4) { width: 250px; } /* Content Prompt */
.content-management table th:nth-child(5) { width: 150px; } /* Call to Action */
.content-management table th:nth-child(6) { width: 100px; } /* Format */
.content-management table th:nth-child(7) { width: 100px; } /* Voice ID */
.content-management table th:nth-child(8) { width: 120px; } /* Settings */
.content-management table th:nth-child(9) { width: 100px; } /* Generated At */
.content-management table th:nth-child(10) { width: 120px; } /* Actions */

/* Responsive adjustments */
@media (max-width: 1200px) {
    .content-management table {
        min-width: 1200px; /* Enable horizontal scroll for smaller screens */
    }
}

/* Add Material UI specific overrides */
.MuiTableCell-root {
    font-size: 0.875rem !important;
}

.MuiTableRow-root:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.MuiCollapse-wrapper {
    background-color: #fafafa;
}

.content-management .MuiPaper-root {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    border-radius: 8px;
    overflow: hidden;
}

.content-management .MuiTableContainer-root {
    max-height: calc(100vh - 200px);
}

/* Responsive styles */
@media (max-width: 1024px) {
    .content-management table {
        @apply min-w-[1000px];
    }
}

/* Animation for expand/collapse */
tr {
    transition: all 0.2s ease-in-out;
}

/* Submenu styling */
.submenu-container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.submenu-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.submenu-tabs .nav-link {
  border-radius: 30px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  background-color: #fff;
  color: #495057;
  border: 1px solid #dee2e6;
  font-weight: 500;
}

.submenu-tabs .nav-link:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submenu-tabs .nav-link.active {
  background-color: #2541b8;
  color: white;
  border-color: #2541b8;
  box-shadow: 0 4px 8px rgba(37, 65, 184, 0.3);
}

@media (max-width: 768px) {
  .submenu-tabs {
    flex-direction: column;
  }

  .submenu-tabs .nav-item {
    width: 100%;
  }
}

/* Add animation for tab content */
.tab-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
