"""
Media Queue Bridge

This module serves as a bridge between the task queue system and the media generation service.
It provides functions to submit content chunks to the media generation pipeline through the task queue.
"""

import os
import json
import logging
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('media_queue_bridge.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:5000')

def submit_chunks_to_media_pipeline(chunk_ids):
    """
    Submit content chunks to the media generation pipeline through the task queue.

    Args:
        chunk_ids (list): List of chunk IDs to process

    Returns:
        dict: Response from the API
    """
    if not chunk_ids:
        logger.error("No chunk IDs provided")
        return {"error": "No chunk IDs provided"}

    # Ensure chunk_ids is a list
    if not isinstance(chunk_ids, list):
        chunk_ids = [chunk_ids]

    logger.info(f"Submitting {len(chunk_ids)} chunks to media generation pipeline")

    try:
        # Submit chunks to the media generation pipeline
        response = requests.post(
            f"{API_BASE_URL}/api/queue/media/submit",
            json={"chunk_ids": chunk_ids},
            headers={"Content-Type": "application/json"}
        )

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully submitted chunks to media generation pipeline: {result['message']}")
            return result
        else:
            error_msg = f"Failed to submit chunks to media generation pipeline: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error submitting chunks to media generation pipeline: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def submit_chunks_to_pipeline_step(chunk_ids, step_name):
    """
    Submit content chunks to a specific step of the media generation pipeline.

    Args:
        chunk_ids (list): List of chunk IDs to process
        step_name (str): Name of the pipeline step to run

    Returns:
        dict: Response from the API
    """
    if not chunk_ids:
        logger.error("No chunk IDs provided")
        return {"error": "No chunk IDs provided"}

    if not step_name:
        logger.error("No step name provided")
        return {"error": "No step name provided"}

    # Ensure chunk_ids is a list
    if not isinstance(chunk_ids, list):
        chunk_ids = [chunk_ids]

    logger.info(f"Submitting {len(chunk_ids)} chunks to pipeline step '{step_name}'")

    try:
        # Submit chunks to the pipeline step
        response = requests.post(
            f"{API_BASE_URL}/api/queue/media/step",
            json={"chunk_ids": chunk_ids, "step_name": step_name},
            headers={"Content-Type": "application/json"}
        )

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully submitted chunks to pipeline step '{step_name}': {result['message']}")
            return result
        else:
            error_msg = f"Failed to submit chunks to pipeline step '{step_name}': {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error submitting chunks to pipeline step '{step_name}': {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def get_pipeline_steps():
    """
    Get the available steps in the media generation pipeline.

    Returns:
        dict: Response from the API containing the available steps
    """
    logger.info("Getting available pipeline steps")

    try:
        # Get the available steps
        response = requests.get(f"{API_BASE_URL}/api/queue/media/steps")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully retrieved {result['count']} pipeline steps")
            return result
        else:
            error_msg = f"Failed to get pipeline steps: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error getting pipeline steps: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def get_queue_status():
    """
    Get the status of the processing queue.

    Returns:
        dict: Response from the API containing the queue status
    """
    logger.info("Getting queue status")

    try:
        # Get the queue status
        response = requests.get(f"{API_BASE_URL}/api/queue/status")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info("Successfully retrieved queue status")
            return result
        else:
            error_msg = f"Failed to get queue status: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error getting queue status: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def get_queue_stats():
    """
    Get statistics on task execution times.

    Returns:
        dict: Response from the API containing the queue statistics
    """
    logger.info("Getting queue statistics")

    try:
        # Get the queue statistics
        response = requests.get(f"{API_BASE_URL}/api/queue/stats")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info("Successfully retrieved queue statistics")
            return result
        else:
            error_msg = f"Failed to get queue statistics: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error getting queue statistics: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def retry_task(task_id):
    """
    Retry a failed task.

    Args:
        task_id (int): ID of the task to retry

    Returns:
        dict: Response from the API
    """
    logger.info(f"Retrying task {task_id}")

    try:
        # Retry the task
        response = requests.post(f"{API_BASE_URL}/api/queue/retry/{task_id}")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully retried task {task_id}: {result['message']}")
            return result
        else:
            error_msg = f"Failed to retry task {task_id}: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error retrying task {task_id}: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

def clear_queue():
    """
    Clear the processing queue.

    Returns:
        dict: Response from the API
    """
    logger.info("Clearing queue")

    try:
        # Clear the queue
        response = requests.post(f"{API_BASE_URL}/api/queue/clear")

        # Check if the request was successful
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully cleared queue: {result['message']}")
            return result
        else:
            error_msg = f"Failed to clear queue: {response.text}"
            logger.error(error_msg)
            return {"error": error_msg}

    except Exception as e:
        error_msg = f"Error clearing queue: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("Usage:")
        print("  python media_queue_bridge.py submit_media <chunk_id1> <chunk_id2> ...")
        print("  python media_queue_bridge.py submit_step <step_name> <chunk_id1> <chunk_id2> ...")
        print("  python media_queue_bridge.py get_steps")
        print("  python media_queue_bridge.py get_status")
        print("  python media_queue_bridge.py get_stats")
        print("  python media_queue_bridge.py retry <task_id>")
        print("  python media_queue_bridge.py clear")
        sys.exit(1)

    command = sys.argv[1]

    if command == "submit_media" and len(sys.argv) >= 3:
        chunk_ids = [int(arg) for arg in sys.argv[2:]]
        result = submit_chunks_to_media_pipeline(chunk_ids)
        print(json.dumps(result, indent=2))

    elif command == "submit_step" and len(sys.argv) >= 4:
        step_name = sys.argv[2]
        chunk_ids = [int(arg) for arg in sys.argv[3:]]
        result = submit_chunks_to_pipeline_step(chunk_ids, step_name)
        print(json.dumps(result, indent=2))

    elif command == "get_steps":
        result = get_pipeline_steps()
        print(json.dumps(result, indent=2))

    elif command == "get_status":
        result = get_queue_status()
        print(json.dumps(result, indent=2))

    elif command == "get_stats":
        result = get_queue_stats()
        print(json.dumps(result, indent=2))

    elif command == "retry" and len(sys.argv) == 3:
        task_id = int(sys.argv[2])
        result = retry_task(task_id)
        print(json.dumps(result, indent=2))

    elif command == "clear":
        result = clear_queue()
        print(json.dumps(result, indent=2))

    else:
        print("Invalid command or arguments")
        print("Usage:")
        print("  python media_queue_bridge.py submit_media <chunk_id1> <chunk_id2> ...")
        print("  python media_queue_bridge.py submit_step <step_name> <chunk_id1> <chunk_id2> ...")
        print("  python media_queue_bridge.py get_steps")
        print("  python media_queue_bridge.py get_status")
        print("  python media_queue_bridge.py get_stats")
        print("  python media_queue_bridge.py retry <task_id>")
        print("  python media_queue_bridge.py clear")
        sys.exit(1)
