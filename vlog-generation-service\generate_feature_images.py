import asyncio
import os
from dotenv import load_dotenv
import logging
from runware import Runware, IImageInference

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('feature_images.log')
    ]
)
logger = logging.getLogger(__name__)

# Runware configuration
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

# Define prompts for the two features
FEATURE_PROMPTS = [
    {
        "name": "ai_content_generation",
        "prompt": """
        A professional visualization of AI-powered content generation. Show a futuristic interface 
        with text being generated by AI, with creative elements flowing from a neural network into 
        well-structured content. Include visual elements like document outlines, text formatting, 
        and content organization. The scene should be in blue and purple tones, professional, 
        clean, and modern. 4K resolution, professional lighting, detailed visualization.
        """
    },
    {
        "name": "ai_image_generation",
        "prompt": """
        A stunning, photorealistic visualization of AI generating images from text prompts. 
        Show a futuristic interface with text prompts on one side and resulting beautiful, 
        diverse images appearing on the other side. Include visual elements like neural networks, 
        creative design elements, and a sense of technological magic. The scene should be vibrant, 
        with orange and teal color palette, and visually impressive with high detail. 4K resolution, 
        professional lighting, cinematic quality.
        """
    }
]

async def generate_image(prompt, filename, runware):
    """Generate an image using Runware API."""
    try:
        logger.info(f"Generating image for: {filename}")
        
        request_image = IImageInference(
            positivePrompt=prompt,
            negativePrompt="blurry, low quality, deformed, text, watermark, signature, pixelated",
            model="runware:101@1",
            numberResults=1,
            height=1024,
            width=1024,
            CFGScale=7.5,
        )
                
        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL
        
        # Download the image
        await download_image(image_url, filename)
        
        logger.info(f"Successfully generated image: {filename}")
        return image_url
    except Exception as e:
        logger.error(f"Error generating image {filename}: {str(e)}")
        raise

async def download_image(image_url, filename):
    """Download image from URL and save to local storage."""
    import aiohttp
    import aiofiles
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    # Ensure the images directory exists
                    output_dir = "my-app/public/images"
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # Save the image
                    file_path = os.path.join(output_dir, filename)
                    async with aiofiles.open(file_path, mode='wb') as f:
                        await f.write(await response.read())
                    
                    logger.info(f"Successfully downloaded image to {file_path}")
                    return True
                else:
                    logger.error(f"Failed to download image. Status code: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        return False

async def main():
    """Main function to generate feature images."""
    logger.info("Starting feature image generation")
    
    runware = Runware(api_key=RUNWARE_API_KEY)
    await runware.connect()
    
    try:
        for feature in FEATURE_PROMPTS:
            filename = f"{feature['name']}.jpg"
            await generate_image(feature['prompt'], filename, runware)
            logger.info(f"Image generation complete: {filename}")
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
    finally:
        logger.info("Image generation process completed")

if __name__ == "__main__":
    asyncio.run(main())
