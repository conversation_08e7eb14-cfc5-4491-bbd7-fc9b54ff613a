# VisionFrame AI Video Processor

A powerful Python-based video processor that converts JSON templates into professional videos, similar to Creatomate's rendering engine. This processor handles multi-scene compositions with images, text overlays, animations, and synchronized audio.

## 🎯 Features

### 🎬 Video Generation
- **Multi-Scene Processing**: Handle complex compositions with multiple scenes
- **Image Backgrounds**: Support for local files, URLs, and placeholder generation
- **Pan/Zoom Animations**: Smooth image animations with configurable easing
- **Color Overlays**: Apply semi-transparent overlays to images

### 📝 Advanced Text Processing
- **Word-by-Word Highlighting**: Synchronized text highlighting with audio
- **Custom Typography**: Font family, size, weight, and color control
- **Text Stroke/Outline**: Configurable stroke width and color
- **Background Styling**: Rounded backgrounds with padding and transparency
- **Multi-line Text**: Automatic text wrapping and line management

### 🎵 Audio Integration
- **ElevenLabs TTS**: Generate speech from text using ElevenLabs API
- **Audio File Support**: Load MP3, WAV, M4A files from local or remote sources
- **Transcript Synchronization**: Automatic word timing generation
- **Audio Duration Detection**: Automatic scene duration based on audio length

### 🎨 Professional Output
- **Multiple Formats**: MP4, AVI, MOV output support
- **Custom Resolutions**: Support for any resolution (720x1280 vertical, 1920x1080 HD, etc.)
- **High Quality**: Professional-grade video encoding with H.264
- **Optimized Performance**: Efficient processing with memory management

## 📋 Requirements

### System Requirements
- Python 3.8 or higher
- FFmpeg (for video processing)
- At least 4GB RAM (8GB recommended)
- 2GB free disk space for temporary files

### Python Dependencies
```bash
pip install -r requirements.txt
```

Required packages:
- `moviepy>=1.0.3` - Video processing
- `pillow>=9.0.0` - Image manipulation
- `numpy>=1.21.0` - Numerical operations
- `requests>=2.25.0` - HTTP requests for downloads
- `opencv-python>=4.5.0` - Computer vision operations

## 🚀 Quick Start

### 1. Installation
```bash
# Clone or download the videoprocessor folder
cd videoprocessor

# Install dependencies
pip install -r requirements.txt

# Ensure FFmpeg is installed on your system
# Windows: Download from https://ffmpeg.org/
# macOS: brew install ffmpeg
# Linux: sudo apt install ffmpeg
```

### 2. Basic Usage
```bash
# Process a template file
python main.py sample_template.json output_video.mp4

# Create sample video
python main.py --sample
```

### 3. With ElevenLabs Integration
```bash
# Set API key as environment variable
export ELEVENLABS_API_KEY="your_api_key_here"

# Or pass it as argument
python main.py template.json output.mp4 --elevenlabs-key your_api_key_here
```

## 📄 Template Structure

The processor accepts JSON templates with the following structure:

```json
{
  "output_format": "mp4",
  "width": 720,
  "height": 1280,
  "elements": [
    {
      "name": "Scene-1",
      "type": "composition",
      "elements": [
        {
          "name": "Background",
          "type": "image",
          "source": "path/to/image.jpg",
          "color_overlay": "rgba(0,0,0,0.15)",
          "animations": [
            {
              "type": "pan",
              "start_scale": "120%",
              "end_scale": "100%"
            }
          ]
        },
        {
          "name": "Subtitle",
          "type": "text",
          "font_family": "Montserrat",
          "font_size": "8 vmin",
          "fill_color": "#ffffff",
          "stroke_color": "#333333",
          "transcript_effect": "highlight",
          "transcript_source": "audio_element_id"
        },
        {
          "name": "Voiceover",
          "type": "audio",
          "source": "path/to/audio.mp3",
          "provider": "elevenlabs model_id=eleven_multilingual_v2 voice_id=XrExE9yKIg1WjnnlVkGX"
        }
      ]
    }
  ]
}
```

## 🎛️ Configuration Options

### Image Elements
- `source`: File path, URL, or placeholder text
- `color_overlay`: RGBA color overlay
- `animations`: Array of animation objects
  - `type`: "pan" for pan/zoom effects
  - `start_scale`, `end_scale`: Scale percentages
  - `easing`: Animation easing (linear, ease-in, ease-out)

### Text Elements
- `font_family`: Font name (Montserrat, Arial, etc.)
- `font_size`: Size in vmin units or pixels
- `fill_color`: Text color in hex format
- `stroke_color`: Outline color
- `stroke_width`: Outline thickness
- `transcript_effect`: "highlight" for word-by-word highlighting
- `transcript_source`: ID of linked audio element
- `x_alignment`, `y_alignment`: Position percentages

### Audio Elements
- `source`: Audio file path or URL
- `provider`: TTS provider configuration
  - ElevenLabs: `elevenlabs model_id=MODEL voice_id=VOICE_ID stability=0.75`
- `transcript`: Text content for TTS generation

## 🔧 Advanced Usage

### Custom Text Processing
```python
from text_processor import TextProcessor

processor = TextProcessor()
text_clip = processor.create_highlighted_text_clip(
    text_element, width, height, duration, transcript_text
)
```

### Audio Generation
```python
from audio_processor import AudioProcessor

audio_proc = AudioProcessor(elevenlabs_api_key)
audio_clip, duration = audio_proc.process_audio_element(
    audio_element, transcript_text
)
```

### Batch Processing
```python
from main import VisionFrameVideoProcessor

processor = VisionFrameVideoProcessor()

templates = ["template1.json", "template2.json", "template3.json"]
for template in templates:
    output = processor.process_template(template)
    print(f"Generated: {output}")
```

## 🎨 Customization

### Adding New Animation Types
Extend the `apply_pan_animation` method in `video_processor.py`:

```python
def apply_custom_animation(self, clip, animation, width, height):
    # Your custom animation logic here
    return modified_clip
```

### Custom Text Effects
Extend the `TextProcessor` class:

```python
def create_custom_text_effect(self, text_element, width, height, duration):
    # Your custom text effect logic
    return text_clip
```

## 🐛 Troubleshooting

### Common Issues

1. **FFmpeg not found**
   ```bash
   # Install FFmpeg
   # Windows: Download from https://ffmpeg.org/
   # macOS: brew install ffmpeg
   # Linux: sudo apt install ffmpeg
   ```

2. **Memory errors with large videos**
   - Reduce video resolution
   - Process scenes individually
   - Increase system RAM

3. **Font not found errors**
   - Install required fonts on your system
   - Use system default fonts
   - Provide custom font paths

4. **ElevenLabs API errors**
   - Check API key validity
   - Verify account credits
   - Check rate limits

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 Performance Tips

1. **Optimize Images**: Use compressed images (JPEG) for backgrounds
2. **Limit Resolution**: Use appropriate resolution for your needs
3. **Batch Processing**: Process multiple templates in sequence
4. **Memory Management**: Close clips after processing
5. **Temporary Files**: Regular cleanup of temp directory

## 🔗 Integration

### With VisionFrame AI Editor
The processor is designed to work seamlessly with JSON exports from the VisionFrame AI editor:

```bash
# Export from editor -> template.json
# Process with video processor
python main.py template.json final_video.mp4
```

### API Integration
```python
from main import VisionFrameVideoProcessor

def process_video_api(template_data, output_path):
    processor = VisionFrameVideoProcessor()
    return processor.process_template(template_data, output_path)
```

## 📝 License

This video processor is part of the VisionFrame AI project and follows the same licensing terms.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in `video_processing.log`
3. Create an issue with template and error details
