
import os
import json
import logging
import ffmpeg
import urllib.request
from datetime import datetime
from typing import List, Dict, Tu<PERSON>, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from dotenv import load_dotenv
import numpy as np
from faster_whisper import WhisperModel
from moviepy.editor import (
    TextClip, CompositeVideoClip, VideoFileClip, 
    ColorClip, AudioFileClip
)
import aiomysql

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'db': os.getenv('DB_DATABASE'),
}



@dataclass
class SubtitleConfig:
    max_chars: int = 30
    max_duration: float = 2.5
    max_gap: float = 1.5
    font: str = "Arial"
    font_size: Optional[int] = None
    text_color: str = 'white'
    highlight_color: str = 'yellow'
    stroke_color: str = 'black'
    stroke_width: float = 1.5
    background_color: tuple = (64, 64, 64)
    background_opacity: float = 0.6

@dataclass
class VideoConfig:
    fps: int = 24
    video_codec: str = "libx264"
    audio_codec: str = "aac"
    temp_audio: str = "temp-audio.m4a"
    whisper_model_size: str = "medium"
    device: str = "cuda"

            
class FileConfig:
    def __init__(
        self,
        base_dir: str = os.path.dirname(os.path.abspath(__file__)),
        image_dir: str = "content-images",
        speech_dir: str = "content-speech",
        output_dir: str = "content-video",
        required_files: int = 3  # Minimum number of image files required
    ):
        self.base_dir = base_dir
        self.image_dir = image_dir
        self.speech_dir = speech_dir
        self.output_dir = output_dir
        self.required_files = required_files

class DatabaseConfig:
    host: str = os.getenv('DB_HOST', DB_CONFIG['host'])
    user: str = os.getenv('DB_USER', DB_CONFIG['user'])
    password: str = os.getenv('DB_PASSWORD', DB_CONFIG['password'])
    db: str = os.getenv('DB_DATABASE', DB_CONFIG['db'])
    
    @classmethod
    def get_config(cls) -> dict:
        return {
            'host': cls.host,
            'user': cls.user,
            'password': cls.password,
            'db': cls.db
        }

async def create_db_pool() -> aiomysql.Pool:
    """Create and return a database connection pool"""
    return await aiomysql.create_pool(**DatabaseConfig.get_config())

async def get_pending_video_content(pool: aiomysql.Pool) -> List[Dict]:
    """
    Get all records from generated_content where video needs to be created
    """
    try:
        async with pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute("""
                    SELECT id, scenario, empathetic_advice, practical_advice 
                    FROM generated_content 
                    WHERE video_created IS NULL OR video_created = 0
                    ORDER BY id
                """)
                results = await cursor.fetchall()
                
                await log_event(
                    pool,
                    "PENDING_VIDEO_QUERY",
                    f"Found {len(results)} records pending video creation"
                )
                return results
    except Exception as e:
        await log_event(
            pool,
            "PENDING_VIDEO_QUERY_ERROR",
            "Failed to fetch pending video content",
            "ERROR",
            e
        )
        raise

def verify_required_files(content_id: str, file_config: FileConfig) -> Tuple[bool, str, Dict[str, str]]:
    """
    Verify all required files exist for video creation
    
    Args:
        content_id: The ID of the content to verify
        file_config: Configuration object containing directory settings
        
    Returns:
        Tuple containing:
            - bool: Success status
            - str: Status message
            - Dict: Dictionary of verified file paths
    """
    try:
        # Step 1: Construct and validate directory paths
        image_dir = os.path.join(file_config.base_dir, file_config.image_dir, str(content_id))
        speech_dir = os.path.join(file_config.base_dir, file_config.speech_dir)
        output_dir = os.path.join(file_config.base_dir, file_config.output_dir)
        
        # Step 2: Verify base directories exist
        if not os.path.exists(file_config.base_dir):
            return False, f"Base directory not found: {file_config.base_dir}", {}
            
        if not os.path.exists(image_dir):
            return False, f"Image directory not found: {image_dir}", {}
        if not os.path.exists(speech_dir):
            return False, f"Speech directory not found: {speech_dir}", {}

        # Step 3: Check for image files
        image_files = [
            f for f in os.listdir(image_dir) 
            if f.lower().endswith(('.png', '.jpg', '.jpeg'))
        ]
        
        # Validate image count
        if not image_files:
            return False, f"No image files found in: {image_dir}", {}
            
        # if len(image_files) < file_config.required_files:
        #     return False, (
        #         f"Insufficient image files. Found {len(image_files)}, "
        #         f"required {file_config.required_files}"
        #     ), {}

        # Step 4: Check for audio file
        audio_file = f"{content_id}.mp3"
        audio_path = os.path.join(speech_dir, audio_file)
        if not os.path.exists(audio_path):
            return False, f"Audio file not found: {audio_path}", {}
            
        # Validate audio file size
        if os.path.getsize(audio_path) == 0:
            return False, f"Audio file is empty: {audio_path}", {}

        # Step 5: Construct and return file paths dictionary
        file_paths = {
            'image_dir': image_dir,
            'speech_file': audio_path,
            'output_dir': output_dir,
            'image_files': sorted([os.path.join(image_dir, f) for f in image_files])
        }

        return True, "All required files found", file_paths

    except Exception as e:
        return False, f"Error verifying files: {str(e)}", {}

async def update_video_generation_status(
    pool: aiomysql.Pool, 
    content_id: str, 
    success: bool = True,
    error_message: str = None
) -> bool:
    """
    Update the video generation status in generated_content table after validating video existence
    """
    try:
        # Construct video path with content-video folder
        base_dir = os.path.dirname(os.path.abspath(__file__))
        video_filename = f"{content_id}_with_subtitles.mp4"
        video_dir = os.path.join(base_dir, "content-video")
        video_path = os.path.join(video_dir, video_filename)

        # Validate video directory exists
        if not os.path.exists(video_dir):
            await log_event(
                pool,
                "VIDEO_VALIDATION_ERROR",
                f"Video directory not found: {video_dir}",
                "ERROR"
            )
            return False

        # Validate video file exists and has content
        if not os.path.exists(video_path):
            await log_event(
                pool,
                "VIDEO_VALIDATION_ERROR",
                f"Video file not found: {video_path}",
                "ERROR"
            )
            return False

        if os.path.getsize(video_path) == 0:
            await log_event(
                pool,
                "VIDEO_VALIDATION_ERROR",
                f"Video file is empty: {video_path}",
                "ERROR"
            )
            return False

        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    UPDATE generated_content 
                    SET 
                        video_created = 1,
                        video_path = %s
                    WHERE id = %s
                """, (video_path, content_id))
                await conn.commit()
                
                await log_event(
                    pool,
                    "VIDEO_GENERATION_STATUS",
                    f"Content ID {content_id}: Video generation {'successful' if success else 'failed'}"
                    + (f" - Error: {error_message}" if error_message else "")
                )
                return True

    except Exception as e:
        await log_event(
            pool,
            "VIDEO_STATUS_UPDATE_ERROR",
            f"Failed to update video status for content ID {content_id}",
            "ERROR",
            e
        )
        return False

async def process_pending_videos(
    pool: aiomysql.Pool,
    file_config: FileConfig = FileConfig(),
    subtitle_config: SubtitleConfig = SubtitleConfig(),
    video_config: VideoConfig = VideoConfig()
) -> None:
    """
    Main function to process all pending videos
    """
    try:
        # Create base directories if they don't exist
        os.makedirs(file_config.base_dir, exist_ok=True)
        os.makedirs(os.path.join(file_config.base_dir, file_config.image_dir), exist_ok=True)
        os.makedirs(os.path.join(file_config.base_dir, file_config.speech_dir), exist_ok=True)
        os.makedirs(os.path.join(file_config.base_dir, file_config.output_dir), exist_ok=True)

        pending_content = await get_pending_video_content(pool)
        
        if not pending_content:
            await log_event(pool, "NO_PENDING_VIDEOS", "No pending videos found")
            return

        for content in pending_content:
            content_id = str(content['id'])
            try:
                # Create content-specific directories
                content_image_dir = os.path.join(file_config.base_dir, file_config.image_dir, content_id)
                content_speech_dir = os.path.join(file_config.base_dir, file_config.speech_dir, content_id)
                os.makedirs(content_image_dir, exist_ok=True)
                os.makedirs(content_speech_dir, exist_ok=True)

                # Verify required files
                success, message, file_paths = verify_required_files(content_id, file_config)
                
                if not success:
                    await log_event(
                        pool,
                        "FILE_VERIFICATION_FAILED",
                        f"Content ID {content_id}: {message}",
                        "ERROR"
                    )
                    await update_video_generation_status(
                        pool, 
                        content_id, 
                        success=False, 
                        error_message=message
                    )
                    continue

                # Ensure output directory exists
                os.makedirs(file_paths['output_dir'], exist_ok=True)
                
                # Define output paths
                output_video = os.path.join(file_paths['output_dir'], f"{content_id}.mp4")
                final_video = os.path.join(file_paths['output_dir'], f"{content_id}_with_subtitles.mp4")

                # Process video and add subtitles
                await process_video_subtitles(
                    input_video_path=output_video,
                    output_video_path=final_video,
                    pool=pool,
                    subtitle_config=subtitle_config,
                    video_config=video_config,
                    content_id=content_id
                )

                # Verify the final video file exists and has size > 0
                if os.path.exists(final_video) and os.path.getsize(final_video) > 0:
                    await log_event(
                        pool,
                        "VIDEO_CREATION_SUCCESS",
                        f"Successfully created video at {final_video}"
                    )
                    # Only update status if file exists and has content
                    await update_video_generation_status(pool, content_id, success=True)
                else:
                    error_msg = f"Final video file missing or empty: {final_video}"
                    await log_event(
                        pool,
                        "VIDEO_CREATION_FAILED",
                        error_msg,
                        "ERROR"
                    )
                    await update_video_generation_status(
                        pool, 
                        content_id, 
                        success=False, 
                        error_message=error_msg
                    )

            except Exception as e:
                error_message = str(e)
                await log_event(
                    pool,
                    "VIDEO_PROCESSING_ERROR",
                    f"Error processing content ID {content_id}: {error_message}",
                    "ERROR",
                    e
                )
                await update_video_generation_status(
                    pool, 
                    content_id, 
                    success=False, 
                    error_message=error_message
                )
                logger.error(f"Error processing content ID {content_id}: {error_message}")

    except Exception as e:
        await log_event(
            pool,
            "BATCH_PROCESSING_ERROR",
            "Error in batch video processing",
            "ERROR",
            e
        )
        raise

async def log_event(pool: aiomysql.Pool, event_type: str, message: str, status: str = "INFO", error: Exception = None) -> None:
    """Log events to both file and database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    if status == "ERROR":
        logger.error(f"{event_type}: {message}", exc_info=error)
    else:
        logger.info(f"{event_type}: {message}")
    
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

def extract_audio(video_path: str, audio_path: str) -> None:
    """Extract audio from video file"""
    input_stream = ffmpeg.input(video_path)
    audio = input_stream.audio
    output_stream = ffmpeg.output(audio, audio_path)
    output_stream = ffmpeg.overwrite_output(output_stream)
    ffmpeg.run(output_stream)

def transcribe_audio(audio_path: str, config: VideoConfig) -> List[Dict]:
    """Transcribe audio using Whisper model"""
    #model = WhisperModel(config.whisper_model_size, device=config.device)
    model = WhisperModel(config.whisper_model_size)
    segments, _ = model.transcribe(audio_path, word_timestamps=True)
    
    wordlevel_info = []
    for segment in segments:
        for word in segment.words:
            wordlevel_info.append({
                'word': word.word,
                'start': word.start,
                'end': word.end
            })
    return wordlevel_info

def split_text_into_lines(data: List[Dict], config: SubtitleConfig) -> List[Dict]:
    """Split transcribed text into lines based on configuration"""
    subtitles = []
    line = []
    line_duration = 0

    for idx, word_data in enumerate(data):
        line.append(word_data)
        line_duration += word_data["end"] - word_data["start"]
        temp = " ".join(item["word"] for item in line)

        duration_exceeded = line_duration > config.max_duration
        chars_exceeded = len(temp) > config.max_chars
        maxgap_exceeded = (idx > 0 and (word_data['start'] - data[idx-1]['end']) > config.max_gap)

        if duration_exceeded or chars_exceeded or maxgap_exceeded:
            if line:
                subtitle_line = {
                    "word": " ".join(item["word"] for item in line),
                    "start": line[0]["start"],
                    "end": line[-1]["end"],
                    "textcontents": line
                }
                subtitles.append(subtitle_line)
                line = []
                line_duration = 0

    if line:
        subtitles.append({
            "word": " ".join(item["word"] for item in line),
            "start": line[0]["start"],
            "end": line[-1]["end"],
            "textcontents": line
        })

    return subtitles

def create_caption(
    text_json: Dict, 
    frame_size: Tuple[int, int], 
    config: SubtitleConfig
) -> Tuple[List[TextClip], List[Dict]]:
    """Create caption clips with highlighting"""
    word_clips = []
    xy_positions = []
    x_pos = 0
    y_pos = 0
    line_width = 0

    frame_width, frame_height = frame_size
    x_buffer = frame_width * 0.1
    max_line_width = frame_width - 2 * x_buffer
    
    if config.font_size is None:
        config.font_size = int(frame_height * 0.075)

    for word_json in text_json['textcontents']:
        duration = word_json['end'] - word_json['start']
        word_clip = TextClip(
            word_json['word'], 
            font=config.font,
            fontsize=config.font_size, 
            color=config.text_color,
            stroke_color=config.stroke_color,
            stroke_width=config.stroke_width
        ).set_start(text_json['start']).set_duration(text_json['end'] - text_json['start'])

        space_clip = TextClip(
            " ", 
            font=config.font,
            fontsize=config.font_size, 
            color=config.text_color
        ).set_start(text_json['start']).set_duration(text_json['end'] - text_json['start'])

        word_width, word_height = word_clip.size
        space_width, _ = space_clip.size

        # Handle line wrapping
        if line_width + word_width + space_width <= max_line_width:
            position = {"x_pos": x_pos, "y_pos": y_pos}
            x_pos += word_width + space_width
            line_width += word_width + space_width
        else:
            x_pos = 0
            y_pos += word_height + 10
            line_width = word_width + space_width
            position = {"x_pos": x_pos, "y_pos": y_pos}
            x_pos = word_width + space_width

        xy_positions.append({
            **position,
            "width": word_width,
            "height": word_height,
            **word_json
        })

        word_clip = word_clip.set_position((position["x_pos"], position["y_pos"]))
        space_clip = space_clip.set_position((position["x_pos"] + word_width, position["y_pos"]))
        
        word_clips.extend([word_clip, space_clip])

    # Add highlighted words
    for pos in xy_positions:
        highlight_clip = TextClip(
            pos['word'], 
            font=config.font,
            fontsize=config.font_size, 
            color=config.highlight_color,
            stroke_color=config.stroke_color,
            stroke_width=config.stroke_width
        ).set_start(pos['start']).set_duration(pos['end'] - pos['start'])
        
        highlight_clip = highlight_clip.set_position((pos['x_pos'], pos['y_pos']))
        word_clips.append(highlight_clip)

    return word_clips, xy_positions

async def process_video_subtitles(
    input_video_path: str,
    output_video_path: str,
    pool: aiomysql.Pool,
    subtitle_config: SubtitleConfig = SubtitleConfig(),
    video_config: VideoConfig = VideoConfig(),
    content_id: int = None
) -> None:
    """Main function to process video and add subtitles"""
    try:
        await log_event(pool, "SUBTITLE_PROCESS_START", f"Starting subtitle processing for {input_video_path}")
        
        # Extract audio
        audio_path = input_video_path.replace(".mp4", ".mp3")
        extract_audio(input_video_path, audio_path)
        await log_event(pool, "AUDIO_EXTRACTION", f"Audio extracted to {audio_path}")

        # Transcribe audio
        word_info = transcribe_audio(     ,  video_config)
        await log_event(pool, "TRANSCRIPTION_COMPLETE", f"Transcribed {len(word_info)} words")

        # Save transcription
        with open('data.json', 'w') as f:
            json.dump(word_info, f, indent=4)

        # Process subtitles
        subtitle_lines = split_text_into_lines(word_info, subtitle_config)
        await log_event(pool, "SUBTITLE_SPLIT", f"Split into {len(subtitle_lines)} lines")

        # Create video with subtitles
        input_video = VideoFileClip(input_video_path)
        all_subtitle_clips = []

        for line in subtitle_lines:
            clips, positions = create_caption(line, input_video.size, subtitle_config)
            
            # Create background
            max_width = max(pos['x_pos'] + pos['width'] for pos in positions)
            max_height = max(pos['y_pos'] + pos['height'] for pos in positions)
            
            color_clip = (ColorClip(size=(int(max_width * 1.1), int(max_height * 1.1)),color=subtitle_config.background_color)
                         .set_opacity(subtitle_config.background_opacity)
                         .set_start(line['start'])
                         .set_duration(line['end'] - line['start']))

            clip_to_overlay = CompositeVideoClip([color_clip] + clips)
            clip_to_overlay = clip_to_overlay.set_position("bottom")
            all_subtitle_clips.append(clip_to_overlay)

        # Create final video
        final_video = CompositeVideoClip([input_video] + all_subtitle_clips)
        final_video = final_video.set_audio(input_video.audio)

        # Save intermediate video with subtitles
        temp_output = output_video_path.replace('.mp4', '_temp.mp4')
        final_video.write_videofile(
            temp_output,
            fps=video_config.fps,
            codec=video_config.video_codec,
            audio_codec=video_config.audio_codec,
            temp_audiofile=video_config.temp_audio,
            remove_temp=True
        )

        # Close MoviePy clips
        final_video.close()
        input_video.close()

        # Combine video with two audio tracks using ffmpeg
        video_clip = ffmpeg.input(temp_output)
        audio_clip = ffmpeg.input(audio_path)

        await log_event(pool, "COMBINING_AUDIO", f"Mixing main audio from {audio_path} with background music")

        try:
        ##Create a video with audio (no subtitles yet)
            ffmpeg.output(
                video_clip.video, 
                audio_clip.audio, 
                output_video_path,
                c='copy'  # Copy codecs without re-encoding
            ).overwrite_output().run()

            # If ffmpeg.output succeeds, update the status as successful
            await update_video_generation_status(
                pool, 
                content_id, 
                success=True, 
                error_message=None
            )
            
        except ffmpeg.Error as e:
            # If ffmpeg.output fails, capture the error message
            message = f"FFmpeg error during final video creation: {str(e)}"
            await log_event(pool, "FFMPEG_ERROR", message, "ERROR", e)
            await update_video_generation_status(
                pool, 
                content_id, 
                success=False, 
                error_message=message
            )
            raise

        # Remove temporary video file
        if os.path.exists(temp_output):
            os.remove(temp_output)

        await log_event(pool, "SUBTITLE_PROCESS_COMPLETE", f"Video with subtitles saved to {output_video_path}")

    except Exception as e:
        await log_event(pool, "SUBTITLE_PROCESS_ERROR", "Error processing subtitles", "ERROR", e)
        raise

async def main():
    """Main entry point"""
    pool = None
    try:
        pool = await create_db_pool()
        # Configure your settings
        file_config = FileConfig(
            base_dir= os.path.dirname(os.path.abspath(__file__)),
            image_dir="content-images",
            speech_dir="content-speech",
            output_dir="content-video"
        )
        
        subtitle_config = SubtitleConfig()
        video_config = VideoConfig()
        
        # Process all pending videos
        await process_pending_videos(
            pool=pool,
            file_config=file_config,
            subtitle_config=subtitle_config,
            video_config=video_config
        )

    except Exception as e:
        if pool:
            await log_event(pool, "SCRIPT_ERROR", "Critical error in main execution", "ERROR", e)
        logger.error("Script execution failed", exc_info=e)
    finally:
        if pool:
            pool.close()
            await pool.wait_closed()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
