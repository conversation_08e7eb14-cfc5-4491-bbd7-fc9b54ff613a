import os
import pymysql
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Database connection details
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_NAME = os.getenv('DB_DATABASE')

DB_CONFIG = {
    'host': DB_HOST,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME,
    'cursorclass': pymysql.cursors.DictCursor
}

def check_schema():
    """Check database schema for foreign key constraints"""
    try:
        logger.info("Connecting to database...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Check generated_content table schema
        logger.info("Checking generated_content table schema...")
        cursor.execute("SHOW CREATE TABLE generated_content")
        result = cursor.fetchone()
        if result:
            logger.info("Generated Content Table Schema:")
            logger.info(result['Create Table'])
        
        # Check content_prompt table schema
        logger.info("Checking content_prompt table schema...")
        cursor.execute("SHOW CREATE TABLE content_prompt")
        result = cursor.fetchone()
        if result:
            logger.info("Content Prompt Table Schema:")
            logger.info(result['Create Table'])
        
        # Check content_prompt_claude table schema
        logger.info("Checking content_prompt_claude table schema...")
        cursor.execute("SHOW CREATE TABLE content_prompt_claude")
        result = cursor.fetchone()
        if result:
            logger.info("Content Prompt Claude Table Schema:")
            logger.info(result['Create Table'])
        
        # Close the connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Error checking schema: {str(e)}")

if __name__ == "__main__":
    check_schema()
