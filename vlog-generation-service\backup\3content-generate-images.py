import asyncio
import aiomysql
import os
from dotenv import load_dotenv
import logging
from datetime import datetime
import time
from runware import Runware, IImageInference  # Changed from runware_sdk to runware
import aiohttp
import aiofiles

# Load environment variables
load_dotenv()

# Configure logging to show in both console and file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console handler
        logging.FileHandler('image_generation.log')  # File handler
    ]
)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'db': os.getenv('DB_DATABASE'),
}

# Runware configuration
RUNWARE_API_KEY = os.getenv('RUNWARE_API_KEY')

async def log_to_db(pool, event_type: str, message: str, status: str = "INFO", error: Exception = None):
    """Log events to database"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(
                    "INSERT INTO event_logs (timestamp, event_type, status, message, error_details) "
                    "VALUES (%s, %s, %s, %s, %s)",
                    (timestamp, event_type, status, message, str(error) if error else None)
                )
                await conn.commit()
    except Exception as e:
        logger.error(f"Failed to log to database: {e}")

async def get_pending_image_prompts(pool):
    """Retrieve content that needs thumbnails generated."""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT generated_content.id as content_id,image_prompts.id, image_prompts.image_prompt 
                    FROM image_prompts inner join generated_content on image_prompts.content_id = generated_content.id 
                    WHERE image_prompts.url IS NULL order by image_prompts.id
                """)
                results = await cursor.fetchall()
                await log_to_db(pool, "PENDING_imageprompts_QUERY", f"Found {len(results)} pending thumbnails")
                return results
    except Exception as e:
        await log_to_db(pool, "PENDING_THUMBNAILS_QUERY_ERROR", "Failed to fetch pending thumbnails", "ERROR", e)
        raise

async def get_pending_thumbnails(pool):
    """Retrieve content that needs thumbnails generated."""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    SELECT id, thumbnail_prompt 
                    FROM generated_content 
                    WHERE thumbnail_url IS NULL 
                    order by id
                """)
                results = await cursor.fetchall()
                await log_to_db(pool, "PENDING_THUMBNAILS_QUERY", f"Found {len(results)} pending thumbnails")
                return results
    except Exception as e:
        await log_to_db(pool, "PENDING_THUMBNAILS_QUERY_ERROR", "Failed to fetch pending thumbnails", "ERROR", e)
        raise



async def generate_thumbnail(pool, image_prompt,id, runware):
    """Generate images for all prompts associated with content_id."""
    start_time = time.time()
    
    await log_to_db(
        pool,
        "IMAGE_GENERATION",
        f"Starting image generation for content ID: {id}",
        "INFO"
    )
    
    try:
        request_image = IImageInference(
        positivePrompt=image_prompt,
        negativePrompt="blurry, low quality, deformed, text, watermark",
        model="runware:101@1",
        numberResults=1,
        height=2048,
        width=1152,
        CFGScale=7.5,
        )
                
        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL
                
        #await update_image_prompt_url(pool, id, image_url)
        await update_thumbnail_url(pool, id, image_url)
    except Exception as e:
        await log_to_db(
                    pool,
                    "IMAGE_GENERATION_ERROR",
                    f"Error processing prompt ID {id}: {str(e)}",
                    "ERROR",
                    e
                )
        
    execution_time = time.time() - start_time
    await log_to_db(
            pool,
            "IMAGE_GENERATION_COMPLETE",
            f"Completed generating all images in {execution_time:.2f} seconds",
            "INFO"
        )
    raise


async def generate_images(pool, image_prompt,id, runware,contentid):
    """Generate images for all prompts associated with content_id."""
    start_time = time.time()
    
    await log_to_db(
        pool,
        "IMAGE_GENERATION",
        f"Starting image generation for content ID: {id}",
        "INFO"
    )
    
    try:
        # Validate prompt length before sending to Runware
        if not image_prompt or len(image_prompt) < 3 or len(image_prompt) > 3000:
            error_msg = f"Invalid prompt length ({len(image_prompt) if image_prompt else 0} chars). Must be between 3 and 3000 characters."
            await log_to_db(
                pool,
                "IMAGE_GENERATION_ERROR",
                f"Prompt validation failed for ID {id}: {error_msg}",
                "ERROR"
            )
            return

        request_image = IImageInference(
            positivePrompt=image_prompt,
            negativePrompt="blurry, low quality, deformed, text, watermark",
            model="runware:101@1",
            numberResults=1,
            height=2048,
            width=1152,
            CFGScale=7.5,
        )
                
        images = await runware.imageInference(requestImage=request_image)
        image_url = images[0].imageURL
                
        await update_image_prompt_url(pool, id, image_url,contentid)
    except Exception as e:
        await log_to_db(
            pool,
            "IMAGE_GENERATION_ERROR",
            f"Error processing prompt ID {id}: {str(e)}",
            "ERROR",
            e
        )
        
    execution_time = time.time() - start_time
    await log_to_db(
            pool,
            "IMAGE_GENERATION_COMPLETE",
            f"Completed generating all images in {execution_time:.2f} seconds",
            "INFO"
        )
    raise


async def update_thumbnail_url(pool, prompt_id, url):
    """Update the image prompt with the generated image URL and timestamp and download the image."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # Update database with URL and timestamp
                await cursor.execute(
                    "UPDATE generated_content SET thumbnail_url = %s, thumbnail_generated_at = NOW() WHERE id = %s",
                    (url, prompt_id)
                )
                await conn.commit()

                # Create content-specific directory path
                content_dir = os.path.join("content-thumbnail", f"{prompt_id}")
                filename = f"{prompt_id}.jpg"  # Using prompt_id as filename
                
                download_result = await download_image(
                    pool=pool,
                    image_url=url,
                    filename=filename,
                    outputpath=content_dir  # Pass the content-specific directory
                )

                if download_result:
                    await log_to_db(
                        pool,
                        "IMAGE_UPDATE_SUCCESS",
                        f"Updated and downloaded image for prompt {prompt_id} in directory {content_dir}",
                        "INFO"
                    )
                else:
                    await log_to_db(
                        pool,
                        "IMAGE_DOWNLOAD_FAILED",
                        f"Updated URL but failed to download image for prompt {prompt_id}",
                        "WARNING"
                    )

            except Exception as e:
                await log_to_db(
                    pool,
                    "IMAGE_UPDATE_ERROR",
                    f"Failed to update/download image for prompt {prompt_id}",
                    "ERROR",
                    e
                )
                raise

async def update_image_prompt_url(pool, prompt_id, url,contentid):
    """Update the image prompt with the generated image URL and timestamp and download the image."""
    async with pool.acquire() as conn:
        async with conn.cursor() as cursor:
            try:
                # Update database with URL and timestamp
                await cursor.execute(
                    "UPDATE image_prompts SET url = %s, url_generated_at = NOW() WHERE id = %s",
                    (url, prompt_id)
                )
                await conn.commit()

                # Create content-specific directory path
                content_dir = os.path.join("content-images", f"{contentid}")
                filename = f"{prompt_id}.jpg"  # Using prompt_id as filename
                
                download_result = await download_image(
                    pool=pool,
                    image_url=url,
                    filename=filename,
                    outputpath=content_dir  # Pass the content-specific directory
                )

                if download_result:
                    await log_to_db(
                        pool,
                        "IMAGE_UPDATE_SUCCESS",
                        f"Updated and downloaded image for prompt {prompt_id} in directory {content_dir}",
                        "INFO"
                    )
                else:
                    await log_to_db(
                        pool,
                        "IMAGE_DOWNLOAD_FAILED",
                        f"Updated URL but failed to download image for prompt {prompt_id}",
                        "WARNING"
                    )

            except Exception as e:
                await log_to_db(
                    pool,
                    "IMAGE_UPDATE_ERROR",
                    f"Failed to update/download image for prompt {prompt_id}",
                    "ERROR",
                    e
                )
                raise

async def download_image(pool, image_url, filename, outputpath):
    """Download image from URL and save to local storage."""
    import aiohttp
    import aiofiles
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                if response.status == 200:
                    # Ensure the images directory exists
                    os.makedirs(outputpath, exist_ok=True)
                    
                    # Save the image
                    file_path = os.path.join(outputpath, filename)
                    async with aiofiles.open(file_path, mode='wb') as f:
                        await f.write(await response.read())
                    
                    await log_to_db(
                        pool,
                        "IMAGE_DOWNLOAD_SUCCESS",
                        f"Successfully downloaded image to {file_path}"
                    )
                    return True
                else:
                    await log_to_db(
                        pool,
                        "IMAGE_DOWNLOAD_ERROR",
                        f"Failed to download image. Status code: {response.status}",
                        "ERROR"
                    )
                    return False
    except Exception as e:
        await log_to_db(
            pool,
            "IMAGE_DOWNLOAD_ERROR",
            f"Error downloading image: {str(e)}",
            "ERROR",
            e
        )
        return False

async def process_pending_imageprompts():
    """Main function to process all pending thumbimage promptnails and generate images."""
    logger.info("Starting process_pending_imageprompts")
    pool = await aiomysql.create_pool(**DB_CONFIG)
    runware = Runware(api_key=RUNWARE_API_KEY)  # Changed from RunwareSDK to Runware
    await runware.connect()  # Add this line to initialize the connection
    
    try:
        # ---------------------------------pending image prompt start---------------------------------
        try:
            logger.info("Starting pending image prompt processing")
            await log_to_db(pool, "PROCESS_START", "Starting pending image prompt and images processing")        
            
            pending_image_prompt = await get_pending_image_prompts(pool)
            
            if pending_image_prompt:
                logger.info(f"Found {len(pending_image_prompt)} items pending for image prompt generation")
                for content_id, prompt_id, image_prompt in pending_image_prompt:
                    logger.info(f"Processing prompt ID: {prompt_id}, Content ID: {content_id}")
                    try:
                        await generate_images(pool, image_prompt, prompt_id, runware, content_id)
                        logger.info(f"Successfully generated image for prompt ID: {prompt_id}")
                    except Exception as e:
                        logger.error(f"Error processing prompt ID {prompt_id}: {str(e)}")
                        continue
            else:
                logger.info("No pending image prompts found for generation")

        except Exception as e:
            logger.error(f"Error in image prompt processing: {str(e)}")
            await log_to_db(pool, "pending image prompt PROCESS_ERROR", f"Error in main process: {str(e)}", "ERROR", e)
        finally:
            logger.info("Finished pending image prompt processing")
            await log_to_db(pool, "pending image prompt PROCESS_END", "Ending processing")

        # ---------------------------------pending image prompt End---------------------------------

        # ---------------------------------pending thumbnail start---------------------------------
        try:
            logger.info("Starting pending thumbnail processing")
            await log_to_db(pool, "PROCESS_START", "Starting thumbnail processing")        
            
            pending_thumbnail = await get_pending_thumbnails(pool)
            
            if pending_thumbnail:
                logger.info(f"Found {len(pending_thumbnail)} items pending for thumbnail generation")
                for id, image_prompt in pending_thumbnail:
                    logger.info(f"Processing thumbnail for ID: {id}")
                    try:
                        await generate_thumbnail(pool, image_prompt, id, runware)
                        logger.info(f"Successfully generated thumbnail for ID: {id}")
                    except Exception as e:
                        logger.error(f"Error processing content ID {id}: {str(e)}")
                        continue
            else:
                logger.info("No pending thumbnails found for generation")

        except Exception as e:
            logger.error(f"Error in thumbnail processing: {str(e)}")
            await log_to_db(pool, "THUMBNAIL_PROCESS_ERROR", f"Error in thumbnail process: {str(e)}", "ERROR", e)
        finally:
            logger.info("Finished pending thumbnail processing")
            await log_to_db(pool, "THUMBNAIL_PROCESS_END", "Ending thumbnail processing")

    except Exception as e:
        logger.error(f"Critical error in main process: {str(e)}")
        await log_to_db(pool, "PROCESS_ERROR", f"Critical error in main process: {str(e)}", "ERROR", e)
    finally:
        logger.info("Cleaning up resources")
        pool.close()
        await pool.wait_closed()
        logger.info("Process completed")
def main():
    asyncio.run(process_pending_imageprompts())

if __name__ == "__main__":
    main()









