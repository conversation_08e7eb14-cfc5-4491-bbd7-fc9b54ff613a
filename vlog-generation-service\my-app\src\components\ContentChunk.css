h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2541b8;
  margin-bottom: 0.5rem;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  padding-left: 35px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  height: 38px;
}

.search-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(37, 65, 184, 0.25);
  border-color: #2541b8;
}

.add-content-btn {
  background-color: #2541b8;
  border-color: #2541b8;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-content-btn:hover {
  background-color: #1a2f8a;
  border-color: #1a2f8a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 65, 184, 0.3);
}

.content-table {
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  margin-bottom: 0;
}

.content-table thead {
  background-color: #f8f9fa;
}

.content-table th {
  color: #333;
  font-weight: 600;
  padding: 10px 16px;
  border-bottom: 2px solid #dee2e6;
}

.content-table td {
  padding: 10px 16px;
  vertical-align: middle;
  border-color: #dee2e6;
}

.content-table tbody tr:hover {
  background-color: #f8f9fa;
}

.action-btn {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin: 0 2px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

/* Pagination styling */
.pagination {
  margin-top: 1.5rem;
}

.pagination .page-link {
  color: #2541b8;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #2541b8;
  border-color: #2541b8;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  color: #1a2f8a;
}

/* Modal styling */
.modal-header {
  background-color: #2541b8;
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  color: white;
  opacity: 1;
}

.modal-footer {
  border-top: none;
}

.modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Form styling */
.form-label {
  font-weight: 500;
  color: #495057;
}

.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  padding: 10px 12px;
}

.form-control:focus, .form-select:focus {
  border-color: #2541b8;
  box-shadow: 0 0 0 0.25rem rgba(37, 65, 184, 0.25);
}

textarea.form-control {
  min-height: 120px;
}
