import React, { useState } from 'react';
import { Card, Table, Row, Col, Badge, Alert, <PERSON>, Button, Spinner } from 'react-bootstrap';
import './Cheatsheets.css';
import { API_BASE_URL } from '../../constants';
import { Psychology as PsychologyIcon } from '@mui/icons-material';

const ClaudePromptCheatsheet = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [topic, setTopic] = useState('');
  const [contentType, setContentType] = useState('blog_post');
  const [purpose, setPurpose] = useState('inform');
  const [audience, setAudience] = useState('general');
  const [tone, setTone] = useState('conversational');
  const [style, setStyle] = useState('descriptive');
  const [wordCount, setWordCount] = useState('500');
  const [customWordCount, setCustomWordCount] = useState('');
  const [showCustomWordCount, setShowCustomWordCount] = useState(false);
  const [persona, setPersona] = useState('expert');

  // Options for dropdowns
  const contentTypes = [
    { value: 'blog_post', label: 'Blog Post/Article' },
    { value: 'video_script', label: 'Video Script' },
    { value: 'story', label: 'Story/Narrative' },
    { value: 'essay', label: 'Essay/Academic Writing' },
    { value: 'social_media', label: 'Social Media Content' },
    { value: 'marketing_copy', label: 'Marketing Copy' },
    { value: 'educational_content', label: 'Educational Content' },
    { value: 'vlog_script', label: 'Vlog Script' },
    { value: 'analysis', label: 'Analysis/Commentary' }
  ];

  const purposes = [
    { value: 'inform', label: 'Inform/Educate' },
    { value: 'persuade', label: 'Persuade/Convince' },
    { value: 'entertain', label: 'Entertain' },
    { value: 'inspire', label: 'Inspire/Motivate' },
    { value: 'analyze', label: 'Analyze/Examine' },
    { value: 'instruct', label: 'Instruct/Guide' },
    { value: 'storytell', label: 'Tell a Story' }
  ];

  const audiences = [
    { value: 'general', label: 'General Audience' },
    { value: 'beginners', label: 'Beginners' },
    { value: 'intermediate', label: 'Intermediate Level' },
    { value: 'advanced', label: 'Advanced/Experts' },
    { value: 'professionals', label: 'Professionals' },
    { value: 'students', label: 'Students' },
    { value: 'millennials', label: 'Millennials' },
    { value: 'gen_z', label: 'Gen Z' },
    { value: 'business_owners', label: 'Business Owners' }
  ];

  const tones = [
    { value: 'conversational', label: 'Conversational' },
    { value: 'formal', label: 'Formal' },
    { value: 'educational', label: 'Educational' },
    { value: 'entertaining', label: 'Entertaining' },
    { value: 'professional', label: 'Professional' },
    { value: 'humorous', label: 'Humorous' },
    { value: 'inspirational', label: 'Inspirational' },
    { value: 'empathetic', label: 'Empathetic' },
    { value: 'critical', label: 'Critical' },
    { value: 'scholarly', label: 'Scholarly' }
  ];

  const styles = [
    { value: 'descriptive', label: 'Descriptive' },
    { value: 'minimalist', label: 'Minimalist' },
    { value: 'analytical', label: 'Analytical' },
    { value: 'narrative', label: 'Narrative' },
    { value: 'persuasive', label: 'Persuasive' },
    { value: 'technical', label: 'Technical' },
    { value: 'creative', label: 'Creative' }
  ];

  const wordCounts = [
    { value: '100', label: '100 words (Very Short)' },
    { value: '200', label: '200 words (Brief)' },
    { value: '300', label: '300 words (Short)' },
    { value: '500', label: '500 words (Medium)' },
    { value: '1000', label: '1000 words (Long)' },
    { value: '1500', label: '1500 words (Very Long)' },
    { value: '2000', label: '2000 words (Comprehensive)' },
    { value: 'custom', label: 'Custom word count' }
  ];

  const personas = [
    { value: 'expert', label: 'Subject Matter Expert' },
    { value: 'journalist', label: 'Journalist' },
    { value: 'teacher', label: 'Teacher/Educator' },
    { value: 'storyteller', label: 'Storyteller' },
    { value: 'coach', label: 'Coach/Mentor' },
    { value: 'analyst', label: 'Analyst' },
    { value: 'researcher', label: 'Researcher' }
  ];

  // Handle word count change
  const handleWordCountChange = (e) => {
    const value = e.target.value;
    setWordCount(value);
    setShowCustomWordCount(value === 'custom');
  };

  const handleGeneratePrompt = async () => {
    if (!topic) {
      setError('Please enter a topic');
      return;
    }

    // Validate custom word count if selected
    if (wordCount === 'custom') {
      if (!customWordCount || isNaN(customWordCount) || parseInt(customWordCount) <= 0) {
        setError('Please enter a valid custom word count');
        return;
      }
    }

    setLoading(true);
    setError(null);

    try {
      const requestData = {
        prompt_type: 'claude_prompt',
        topic: topic,
        content_type: contentType,
        purpose: purpose,
        audience: audience,
        tone: tone,
        style: style,
        word_count: wordCount === 'custom' ? customWordCount : wordCount,
        persona: persona
      };

      console.log('Sending request with data:', requestData);

      const response = await fetch(`${API_BASE_URL}/api/grok/generate-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate prompt: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      if (data.success && data.generated_prompt) {
        setGeneratedPrompt(data.generated_prompt);
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }
    } catch (err) {
      console.error('Error generating prompt:', err);
      setError(`Failed to generate prompt: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="cheatsheet-container">
      <Card className="mb-4">
        <Card.Header className="bg-dark text-white">
          <h4 className="mb-0">🤖 Claude Ultimate Prompt Engineering for Content Creators</h4>
        </Card.Header>
        <Card.Body>
          <p className="lead">
            Generate powerful, structured prompts for Claude AI to create high-quality content based on the Ultimate Prompt Engineering Cheatsheet.
          </p>
          <Alert variant="info">
            <strong>How it works:</strong> Select your content parameters below, and our system will generate a perfectly structured prompt optimized for Claude AI.
          </Alert>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header className="d-flex align-items-center">
          <PsychologyIcon className="me-2" />
          <h5 className="mb-0">Generate Claude-Optimized Prompt</h5>
        </Card.Header>
        <Card.Body>
          {error && (
            <Alert variant="danger" onClose={() => setError(null)} dismissible>
              {error}
            </Alert>
          )}

          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Topic</Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Enter your topic"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                  />
                  <Form.Text className="text-muted">
                    Be specific about what you want Claude to write about
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Content Type</Form.Label>
                  <Form.Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value)}
                  >
                    {contentTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Specific Purpose</Form.Label>
                  <Form.Select
                    value={purpose}
                    onChange={(e) => setPurpose(e.target.value)}
                  >
                    {purposes.map((p) => (
                      <option key={p.value} value={p.value}>
                        {p.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Target Audience</Form.Label>
                  <Form.Select
                    value={audience}
                    onChange={(e) => setAudience(e.target.value)}
                  >
                    {audiences.map((a) => (
                      <option key={a.value} value={a.value}>
                        {a.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Tone</Form.Label>
                  <Form.Select
                    value={tone}
                    onChange={(e) => setTone(e.target.value)}
                  >
                    {tones.map((t) => (
                      <option key={t.value} value={t.value}>
                        {t.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Writing Style</Form.Label>
                  <Form.Select
                    value={style}
                    onChange={(e) => setStyle(e.target.value)}
                  >
                    {styles.map((s) => (
                      <option key={s.value} value={s.value}>
                        {s.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Word Count/Length</Form.Label>
                  <Form.Select
                    value={wordCount}
                    onChange={handleWordCountChange}
                  >
                    {wordCounts.map((wc) => (
                      <option key={wc.value} value={wc.value}>
                        {wc.label}
                      </option>
                    ))}
                  </Form.Select>
                  {showCustomWordCount && (
                    <Form.Control
                      type="number"
                      placeholder="Enter custom word count"
                      className="mt-2"
                      value={customWordCount}
                      onChange={(e) => setCustomWordCount(e.target.value)}
                      min="1"
                    />
                  )}
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Claude Persona</Form.Label>
                  <Form.Select
                    value={persona}
                    onChange={(e) => setPersona(e.target.value)}
                  >
                    {personas.map((p) => (
                      <option key={p.value} value={p.value}>
                        {p.label}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <div className="d-grid">
              <Button
                variant="primary"
                onClick={handleGeneratePrompt}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                    Generating...
                  </>
                ) : (
                  <>Generate Claude-Optimized Prompt</>
                )}
              </Button>
            </div>
          </Form>

          {generatedPrompt && (
            <div className="mt-4">
              <h6>Generated Prompt for Claude:</h6>
              <div className="bg-light p-3 rounded">
                <pre className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                  {generatedPrompt}
                </pre>
              </div>
              <div className="d-grid mt-2">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(generatedPrompt);
                  }}
                >
                  Copy to Clipboard
                </Button>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Core Principles</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>1. Be Specific</h6>
              <ul>
                <li><strong>Bad</strong>: "Write about climate change."</li>
                <li><strong>Good</strong>: "Write a 500-word blog post about three innovative technologies addressing climate change, with statistics from the last two years."</li>
              </ul>
            </Col>
            <Col md={6}>
              <h6>2. Structure Your Prompts</h6>
              <ul>
                <li>Use clear sections with headers</li>
                <li>Number your requirements</li>
                <li>Use bullet points for clarity</li>
                <li>Provide context before requests</li>
              </ul>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col md={6}>
              <h6>3. Define Output Parameters</h6>
              <ul>
                <li>Specify word count/length</li>
                <li>Mention style, tone, and format</li>
                <li>State target audience</li>
                <li>Include content structure preferences</li>
              </ul>
            </Col>
            <Col md={6}>
              <h6>4. Use Persona Direction</h6>
              <ul>
                <li>"Act as an expert in [field]"</li>
                <li>"Write as if you are a [profession/expert]"</li>
                <li>"Adopt the writing style of [author/publication]"</li>
              </ul>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Content-Specific Frameworks</h5>
        </Card.Header>
        <Card.Body>
          <div className="mb-4">
            <h6>Blog Posts/Articles</h6>
            <div className="bg-light p-3 rounded">
              <pre className="mb-0">
{`Write a [word count] [content type] about [specific topic] for [target audience].
Include:
1. An engaging headline that [objective]
2. [Number] key points focused on [specific aspects]
3. Data/statistics to support claims
4. A clear call-to-action encouraging readers to [desired action]
Tone should be [descriptive tone] and writing style should resemble [publication/writer].`}
              </pre>
            </div>
          </div>

          <div className="mb-4">
            <h6>Video Scripts</h6>
            <div className="bg-light p-3 rounded">
              <pre className="mb-0">
{`Create a [duration] video script about [topic] aimed at [audience].
Structure:
1. Hook (10-15 seconds): Capture attention with [approach]
2. Introduction (30 seconds): Introduce [topic/problem]
3. Main content: Break down [topic] into [number] key segments
   * Segment 1: [specific focus]
   * Segment 2: [specific focus]
4. Conclusion (30 seconds): Summarize key points and include [call to action]
Include camera directions and visual cues in [brackets].
Tone: [conversational/educational/entertaining]`}
              </pre>
            </div>
          </div>

          <div className="mb-4">
            <h6>Storytelling/Narratives</h6>
            <div className="bg-light p-3 rounded">
              <pre className="mb-0">
{`Write a [genre] story about [brief concept] with these elements:
1. Main character: [brief description with key traits]
2. Setting: [time period and location]
3. Central conflict: [description of the challenge/problem]
4. Theme: Explore [theme] throughout the narrative
5. Resolution style: [hopeful/ambiguous/tragic]
Style should be [descriptive style] with [pacing preference].
Maximum length: [word count]`}
              </pre>
            </div>
          </div>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Advanced Techniques</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>Chain-of-Thought</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
{`To solve [complex problem], please:
1. Break down the key elements of [problem]
2. Consider different approaches to addressing [aspect]
3. Analyze the pros and cons of each approach
4. Recommend the best solution based on [specific criteria]
5. Explain your reasoning for this recommendation`}
                </pre>
              </div>
            </Col>
            <Col md={6}>
              <h6>Multi-Step Generation</h6>
              <div className="bg-light p-3 rounded mb-3">
                <pre className="mb-0">
{`First, generate 5 potential [headlines/topics/angles] for [content type] about [subject].
Then, I'll select one, and you'll create an outline.
Finally, expand that outline into a complete [content type].`}
                </pre>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Troubleshooting AI Responses</h5>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover responsive>
            <thead className="bg-light">
              <tr>
                <th>Problem</th>
                <th>Solution</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Too Generic</td>
                <td>"Avoid clichés and generic advice. Include specific, actionable insights that aren't commonly found in basic articles."</td>
              </tr>
              <tr>
                <td>Too Short</td>
                <td>"Elaborate on each point with at least [X] sentences of explanation and one specific example."</td>
              </tr>
              <tr>
                <td>Lack Creativity</td>
                <td>"Approach this from an unexpected angle that most people overlook."</td>
              </tr>
              <tr>
                <td>Too General</td>
                <td>"Include at least [X] specific statistics from reputable sources."</td>
              </tr>
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Footer className="text-center">
          <p className="mb-0">
            <small>
              Based on the Ultimate Prompt Engineering Cheatsheet for Content Creators
            </small>
          </p>
        </Card.Footer>
      </Card>
    </div>
  );
};

export default ClaudePromptCheatsheet;
