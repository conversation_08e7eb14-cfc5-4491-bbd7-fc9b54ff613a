import sqlite3

# Connect to the database
conn = sqlite3.connect('vlog_content.db')
cursor = conn.cursor()

# Get all tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("Tables in the database:")
for table in tables:
    print(table[0])
    
# Check if content_prompt_claude table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='content_prompt_claude'")
if cursor.fetchone():
    # Get schema of content_prompt_claude table
    cursor.execute("PRAGMA table_info(content_prompt_claude)")
    columns = cursor.fetchall()
    print("\nColumns in content_prompt_claude table:")
    for column in columns:
        print(column)
    
    # Check if content_prompt column exists
    has_content_prompt = False
    for column in columns:
        if column[1] == 'content_prompt':
            has_content_prompt = True
            break
    
    if not has_content_prompt:
        print("\nAdding content_prompt column to content_prompt_claude table...")
        cursor.execute("ALTER TABLE content_prompt_claude ADD COLUMN content_prompt TEXT")
        conn.commit()
        print("Column added successfully!")
else:
    print("\ncontent_prompt_claude table does not exist.")
    
# Close the connection
conn.close()
