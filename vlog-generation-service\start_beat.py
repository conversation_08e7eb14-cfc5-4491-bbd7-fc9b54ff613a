"""
Start Celery Beat Script

This script starts Celery Beat for scheduling periodic tasks in the VisionFrame AI queue system.
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def start_beat():
    """Start Celery Beat"""
    print("Starting Celery Beat...")
    
    # Start Celery Beat
    cmd = [
        'celery',
        '-A', 'tasks',
        'beat',
        '--loglevel=info'
    ]
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("Beat stopped by user")
    except Exception as e:
        print(f"Error starting beat: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_beat()
