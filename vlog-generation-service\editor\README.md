# VisionFrame AI - Browser-Based Video Editor

A powerful, browser-based video editor built with HTML5, CSS, and JavaScript that allows you to create animated video compositions and export them to JSON format for use with video rendering services.

## Features

### 🎬 Core Editor Features
- **Multi-Element Support**: Add text, images, and videos to your composition
- **Real-Time Preview**: See your animations play in real-time with timeline scrubbing
- **Keyframe Animation System**: Animate properties like position, opacity, scale, and rotation
- **Timeline Controls**: Play/pause functionality with precise time control
- **Element Management**: Easy selection, editing, and deletion of elements

### 🎞️ Animation Properties
Each element supports animations for:
- **Position** (x, y coordinates)
- **Opacity** (fade in/out effects)
- **Scale** (size changes)
- **Rotation** (spinning effects)

### 📤 Export Capabilities
- Export projects to structured JSON format
- Compatible with video rendering services like Creatomate
- Includes all element properties and animation keyframes
- Configurable output format, dimensions, and duration

## Getting Started

### 1. Open the Editor
Simply open `index.html` in your web browser. No server setup required!

### 2. Load Sample Project
Click "Load Sample Project" to see a demonstration with 5 animated elements showcasing different animation types.

### 3. Create Your First Element
1. Click "Add Text", "Add Image", or "Add Video"
2. Fill in the required information in the modal
3. Click the confirm button to add the element

### 4. Edit Elements
1. Click on an element in the Elements list or preview area
2. Use the Properties panel on the right to modify:
   - Basic properties (position, content)
   - Animation keyframes (time and value pairs)

### 5. Preview Your Animation
- Use the Play/Pause button to see your animation in action
- Drag the timeline scrubber to jump to specific times
- Elements will animate according to their keyframes

### 6. Export Your Project
Click "Export to JSON" to download your project as a JSON file ready for video rendering.

## Project Structure

```
editor/
├── index.html          # Main HTML file with UI structure
├── styles.css          # Custom CSS styles and animations
├── script.js           # Main JavaScript application logic
├── sample-project.json # Example project with 5 animated elements
└── README.md          # This documentation file
```

## JSON Export Format

The exported JSON follows this structure:

```json
{
  "output_format": "mp4",
  "width": 1920,
  "height": 1080,
  "duration": 10,
  "elements": [
    {
      "type": "text|image|video",
      "text": "Content for text elements",
      "src": "URL for image/video elements",
      "x": "50%",
      "y": "50%",
      "start": 0,
      "end": 5,
      "animations": [
        {
          "property": "opacity|x|y|scale|rotation",
          "keyframes": [
            { "time": 0, "value": 0 },
            { "time": 1, "value": 1 }
          ]
        }
      ]
    }
  ]
}
```

## Element Types

### Text Elements
- **Properties**: text content, position
- **Animations**: All standard properties
- **Usage**: Titles, captions, call-to-action text

### Image Elements
- **Properties**: source URL, position
- **Animations**: All standard properties
- **Usage**: Logos, graphics, photos
- **Note**: Use publicly accessible image URLs

### Video Elements
- **Properties**: source URL, position, start/end times
- **Animations**: All standard properties
- **Usage**: Background videos, clips, overlays
- **Note**: Use publicly accessible video URLs

## Animation System

### Keyframes
Each animation consists of keyframes that define:
- **Time**: When the animation occurs (in seconds)
- **Value**: The property value at that time

### Interpolation
The editor automatically interpolates between keyframes for smooth animations:
- **Linear interpolation** for numeric values
- **Immediate changes** for non-numeric values

### Animation Properties
- **opacity**: 0 (transparent) to 1 (opaque)
- **x/y**: Position as percentage ("50%") or pixels (100)
- **scale**: Size multiplier (1 = normal, 0.5 = half size, 2 = double size)
- **rotation**: Degrees (0-360)

## Tips and Best Practices

### 1. Planning Your Animation
- Sketch your timeline before starting
- Consider the flow and timing of elements
- Use opacity animations for smooth entrances/exits

### 2. Positioning Elements
- Use percentage values for responsive positioning
- Center elements with 50% x/y and transform: translate(-50%, -50%)
- Test different screen sizes if needed

### 3. Animation Timing
- Start with simple fade-ins (opacity 0 to 1)
- Add movement animations for dynamic effects
- Use scale animations for emphasis
- Combine multiple properties for complex effects

### 4. Performance
- Keep animation durations reasonable (avoid very long projects)
- Limit the number of simultaneous animations
- Use appropriate image/video sizes

## Browser Compatibility

This editor works in all modern browsers that support:
- HTML5 Canvas
- CSS3 Transforms
- ES6 JavaScript features
- Fetch API

Tested browsers:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Troubleshooting

### Images/Videos Not Loading
- Ensure URLs are publicly accessible
- Check for CORS restrictions
- Use HTTPS URLs when possible

### Animations Not Smooth
- Check browser performance
- Reduce the number of simultaneous animations
- Use simpler animation properties

### Export Issues
- Ensure all required fields are filled
- Check browser's download settings
- Try a different browser if issues persist

## Future Enhancements

Potential features for future versions:
- Drag-and-drop element positioning
- More animation easing options
- Audio track support
- Template library
- Collaborative editing
- Direct video rendering

## Support

For issues or questions:
1. Check this README for common solutions
2. Review the browser console for error messages
3. Test with the sample project to isolate issues
4. Ensure all URLs are accessible and properly formatted

---

**VisionFrame AI Video Editor** - Create professional video animations in your browser!
