import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, InputGroup } from 'react-bootstrap';
import { Visibility, VisibilityOff } from '@mui/icons-material';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';
const GROK_API_URL = process.env.REACT_APP_GROK_API_URL || API_BASE_URL;

const ApiSettings = ({ setError }) => {
  const [apiSettings, setApiSettings] = useState({
    apiBaseUrl: API_BASE_URL,
    grokApiUrl: GROK_API_URL,
    xaiApiKey: '',
    dbHost: '',
    dbUser: '',
    dbPassword: '',
    dbName: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  
  // This is a placeholder function since we're not actually saving the settings to the server
  // In a real implementation, this would make an API call to update the settings
  const handleSaveSettings = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSuccessMessage('API settings have been saved successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    }, 1000);
  };
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setApiSettings({
      ...apiSettings,
      [name]: value
    });
  };
  
  return (
    <Card>
      <Card.Header>
        <h5 className="mb-0">API Settings</h5>
      </Card.Header>
      <Card.Body>
        {successMessage && (
          <Alert variant="success" onClose={() => setSuccessMessage(null)} dismissible>
            {successMessage}
          </Alert>
        )}
        
        <Form>
          <Form.Group className="mb-3">
            <Form.Label>API Base URL</Form.Label>
            <Form.Control
              type="text"
              name="apiBaseUrl"
              value={apiSettings.apiBaseUrl}
              onChange={handleInputChange}
              placeholder="http://localhost:5000"
            />
            <Form.Text className="text-muted">
              The base URL for the API server.
            </Form.Text>
          </Form.Group>
          
          <Form.Group className="mb-3">
            <Form.Label>Grok API URL</Form.Label>
            <Form.Control
              type="text"
              name="grokApiUrl"
              value={apiSettings.grokApiUrl}
              onChange={handleInputChange}
              placeholder="http://localhost:5000"
            />
            <Form.Text className="text-muted">
              The URL for the Grok API server.
            </Form.Text>
          </Form.Group>
          
          <Form.Group className="mb-3">
            <Form.Label>XAI API Key</Form.Label>
            <InputGroup>
              <Form.Control
                type={showApiKey ? "text" : "password"}
                name="xaiApiKey"
                value={apiSettings.xaiApiKey}
                onChange={handleInputChange}
                placeholder="Enter your XAI API key"
              />
              <Button 
                variant="outline-secondary"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <VisibilityOff /> : <Visibility />}
              </Button>
            </InputGroup>
            <Form.Text className="text-muted">
              Your API key for accessing the Grok API.
            </Form.Text>
          </Form.Group>
          
          <hr className="my-4" />
          
          <h6>Database Configuration</h6>
          
          <Form.Group className="mb-3">
            <Form.Label>Database Host</Form.Label>
            <Form.Control
              type="text"
              name="dbHost"
              value={apiSettings.dbHost}
              onChange={handleInputChange}
              placeholder="localhost"
            />
          </Form.Group>
          
          <Form.Group className="mb-3">
            <Form.Label>Database Name</Form.Label>
            <Form.Control
              type="text"
              name="dbName"
              value={apiSettings.dbName}
              onChange={handleInputChange}
              placeholder="vlog_generator"
            />
          </Form.Group>
          
          <Form.Group className="mb-3">
            <Form.Label>Database User</Form.Label>
            <Form.Control
              type="text"
              name="dbUser"
              value={apiSettings.dbUser}
              onChange={handleInputChange}
              placeholder="root"
            />
          </Form.Group>
          
          <Form.Group className="mb-3">
            <Form.Label>Database Password</Form.Label>
            <InputGroup>
              <Form.Control
                type={showPassword ? "text" : "password"}
                name="dbPassword"
                value={apiSettings.dbPassword}
                onChange={handleInputChange}
                placeholder="Enter database password"
              />
              <Button 
                variant="outline-secondary"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </Button>
            </InputGroup>
          </Form.Group>
          
          <div className="d-grid gap-2 d-md-flex justify-content-md-end">
            <Button 
              variant="primary" 
              onClick={handleSaveSettings}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                  Saving...
                </>
              ) : (
                'Save Settings'
              )}
            </Button>
          </div>
        </Form>
        
        <Alert variant="info" className="mt-4">
          <strong>Note:</strong> These settings are currently for demonstration purposes only. In a production environment, these would be securely stored and managed through environment variables or a configuration service.
        </Alert>
      </Card.Body>
    </Card>
  );
};

export default ApiSettings;
